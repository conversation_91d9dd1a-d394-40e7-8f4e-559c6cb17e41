import { enhanceRouteHandler } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { createTeamAccountsAuthApi } from '@kit/team-accounts/api';

import appConfig from '~/config/app.config';
import { createCorsResponse } from '~/lib/cors';

import { ZaloAuthSchema } from './schema';

/**
 * Enhanced Zalo authentication with optional account_id support
 *
 * Account Resolution Priority:
 * 1. temp_theme_id (temporary theme with expiration)
 * 2. theme_id (permanent theme configuration)
 * 3. Default account (slug: 'makerkit')
 * 4. null (no account context - guest/public access)
 *
 * This allows authentication in multiple scenarios:
 * - With account context (normal users)
 * - Without account context (guest users, public access)
 * - With theme-specific branding (mini app themes)
 * - With temporary themes (time-limited access)
 */
const resolveAccountId = async (
  client: ReturnType<typeof getSupabaseServerAdminClient>,
  tempThemeId?: string | null,
  themeId?: string | null,
): Promise<string | null> => {
  // Try temp theme first
  if (tempThemeId) {
    const { data, error } = await client
      .from('temp_themes')
      .select('account_id, expires_at')
      .eq('id', tempThemeId)
      .single();

    if (!error && data && new Date(data.expires_at) >= new Date()) {
      return data.account_id;
    }
  }

  // Try theme ID
  if (themeId) {
    const { data, error } = await client
      .from('account_themes')
      .select('account_id')
      .eq('id', themeId)
      .single();

    if (!error && data) {
      return data.account_id;
    }
  }

  // Return null if no account found (optional account_id)
  return null;
};

export const POST = enhanceRouteHandler(
  async ({ body, request }) => {
    const logger = await getLogger();
    const adminClient = getSupabaseServerAdminClient();
    const authApi = createTeamAccountsAuthApi(adminClient);

    try {
      const accountId = await resolveAccountId(
        adminClient,
        body.temp_theme_id,
        body.theme_id,
      );

      const customer = await authApi.getOrCreateCustomer(
        accountId, // Can be null now
        body.access_token,
      );
      if (!customer) throw new Error('Failed to authenticate customer');

      // Generate and verify magic link in one step
      const linkOptions: any = {
        data: { role: customer.accountRole },
      };

      // Include account_id in metadata if available
      if (accountId) {
        linkOptions.data.account_id = accountId;
      }

      const { data: linkData, error: linkError } =
        await adminClient.auth.admin.generateLink({
          type: 'magiclink',
          email: customer.email,
          options: linkOptions,
        });

      if (linkError || !linkData?.properties?.hashed_token) {
        throw new Error('Failed to generate auth token');
      }

      const { data: sessionData, error: sessionError } =
        await adminClient.auth.verifyOtp({
          token_hash: linkData.properties.hashed_token,
          type: 'magiclink',
        });

      if (sessionError || !sessionData?.session) {
        throw new Error('Failed to create session');
      }

      // Get organization info and user role (only if accountId exists)
      let organization = null;
      let membership = null;
      let guardian = null;
      let instructorPrograms = null;

      if (accountId) {
        // Get organization info
        const { data: orgData } = await adminClient
          .from('accounts')
          .select('id, name, logo_url, settings')
          .eq('id', accountId) // Fix: use 'id' not 'account_id'
          .single();
        organization = orgData;

        // Get user's role in the organization
        const { data: membershipData } = await adminClient
          .from('accounts_memberships')
          .select('account_role')
          .eq('user_id', customer.userId)
          .eq('account_id', accountId)
          .single();
        membership = membershipData;

        // Check if user is guardian
        const { data: guardianData } = await adminClient
          .from('guardians')
          .select('id, full_name')
          .eq('user_id', customer.userId)
          .eq('account_id', accountId)
          .single();
        guardian = guardianData;

        // Check if user is instructor
        const { data: programsData } = await adminClient
          .from('programs')
          .select('id, name')
          .eq('account_id', accountId)
          .or(
            `instructor_id.eq.${customer.userId},assistant_id.eq.${customer.userId}`,
          );
        instructorPrograms = programsData;
      }

      // Determine user roles
      const roles = [];
      if (membership?.account_role) {
        roles.push(membership.account_role);
      }
      if (guardian) {
        roles.push('guardian');
      }
      if (instructorPrograms && instructorPrograms.length > 0) {
        roles.push('instructor');
      }

      logger.info(
        { userId: customer.userId, accountId: accountId || 'none', roles },
        'Zalo auth successful',
      );

      const response = createCorsResponse(
        request,
        {
          success: true,
          user: {
            id: customer.userId,
            name: customer.name,
            picture: customer.picture,
            phone: customer.phone,
          },
          access_token: sessionData.session.access_token,
          refresh_token: sessionData.session.refresh_token,
          account_id: accountId || null, // Can be null
          organization: organization
            ? {
                id: organization.id,
                name: organization.name,
                logo_url: organization.logo_url,
                settings: organization.settings,
              }
            : null,
          roles: roles,
          primary_role: roles[0] || 'member',
          permissions: {
            can_view_dashboard:
              roles.includes('owner') ||
              roles.includes('admin') ||
              roles.includes('education_admin') ||
              roles.includes('instructor'),
            can_manage_learners:
              roles.includes('owner') ||
              roles.includes('admin') ||
              roles.includes('education_admin'),
            can_take_attendance:
              roles.includes('instructor') || roles.includes('assistant'),
            can_create_reports:
              roles.includes('instructor') || roles.includes('assistant'),
            can_view_children: roles.includes('guardian'),
            can_pay_fees: roles.includes('guardian'),
          },
          guardian_info: guardian
            ? {
                id: guardian.id,
                full_name: guardian.full_name,
              }
            : null,
          instructor_programs: instructorPrograms || [],
          theme_id: body.theme_id || null,
          temp_theme_id: body.temp_theme_id || null,
        },
        200,
      );

      response.cookies.set({
        name: `sb-${process.env.NEXT_PUBLIC_SUPABASE_URL!.split('.')[0]}-auth-token`,
        value: JSON.stringify({
          access_token: sessionData.session.access_token,
          refresh_token: sessionData.session.refresh_token,
        }),
        httpOnly: true,
        secure: appConfig.production,
        path: '/',
        maxAge: 60 * 60 * 24 * 7, // 7 days
      });

      return response;
    } catch (error: any) {
      logger.error(
        {
          error: error.message,
          stack: error.stack,
          body: {
            ...body,
            access_token: body.access_token ? '[REDACTED]' : undefined,
          },
        },
        'Zalo auth failed',
      );

      // Determine appropriate error response
      const isAuthError =
        error.message.includes('authenticate') ||
        error.message.includes('token') ||
        error.message.includes('session');

      const statusCode = isAuthError ? 401 : 500;
      const errorMessage = isAuthError
        ? 'Authentication failed'
        : 'Internal server error';

      return createCorsResponse(
        request,
        {
          success: false,
          error: errorMessage,
          details: error.message,
          account_id: null, // Always null on error
        },
        statusCode,
      );
    }
  },
  { schema: ZaloAuthSchema, auth: false },
);
