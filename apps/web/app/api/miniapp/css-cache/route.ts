/**
 * CSS Cache API
 * 
 * Provides endpoints for CSS cache management and optimization
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';

import { 
  getCSSCacheStats, 
  validateCSSCache,
  forceCSSRegeneration,
  generateConfigMD5 
} from '~/app/home/<USER>/miniapp/setup/customization/_lib/services/css-auto-build.service';
import type { EnhancedThemeConfig } from '~/app/home/<USER>/miniapp/setup/customization/_lib/types/theme-config.types';

// Request schemas
const GetCacheStatsSchema = z.object({
  themeId: z.string().optional(),
  tempThemeId: z.string().optional(),
  accountThemeId: z.string().optional(),
});

const RegenerateCSSSchema = z.object({
  themeId: z.string().optional(),
  tempThemeId: z.string().optional(),
  accountThemeId: z.string().optional(),
  force: z.boolean().default(false),
});

/**
 * GET /api/miniapp/css-cache
 * Get CSS cache statistics and validation info
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = getSupabaseServerClient();
    const api = createTeamAccountsApi(supabase);
    
    // Parse query parameters
    const url = new URL(request.url);
    const params = Object.fromEntries(url.searchParams.entries());
    const { themeId, tempThemeId, accountThemeId } = GetCacheStatsSchema.parse(params);

    // Determine which table to query
    let query;
    let tableName: string;
    
    if (tempThemeId) {
      query = supabase.from('temp_themes').select('*').eq('id', tempThemeId);
      tableName = 'temp_themes';
    } else if (accountThemeId) {
      query = supabase.from('account_themes').select('*').eq('id', accountThemeId);
      tableName = 'account_themes';
    } else if (themeId) {
      query = supabase.from('themes').select('*').eq('id', themeId);
      tableName = 'themes';
    } else {
      return NextResponse.json(
        { error: 'Must provide themeId, tempThemeId, or accountThemeId' },
        { status: 400 }
      );
    }

    const { data: theme, error } = await query.single();

    if (error) {
      console.error(`Error fetching theme from ${tableName}:`, error);
      return NextResponse.json(
        { error: 'Theme not found' },
        { status: 404 }
      );
    }

    // Create enhanced config for analysis
    const enhancedConfig: EnhancedThemeConfig = {
      ...theme.config,
      optimizedCSS: theme.optimized_css,
      cssHash: theme.css_hash,
      cssMetadata: theme.css_metadata,
      configMd5: theme.config_md5,
    };

    // Get cache stats
    const cacheStats = getCSSCacheStats(enhancedConfig);
    
    // Validate cache
    const validation = validateCSSCache(enhancedConfig);
    
    // Calculate current config MD5
    const currentConfigMD5 = generateConfigMD5(theme.config);
    const isConfigChanged = currentConfigMD5 !== theme.config_md5;

    return NextResponse.json({
      success: true,
      data: {
        themeId: theme.id,
        tableName,
        cacheStats,
        validation,
        configInfo: {
          currentMD5: currentConfigMD5,
          storedMD5: theme.config_md5,
          isChanged: isConfigChanged,
        },
        timestamps: {
          createdAt: theme.created_at,
          updatedAt: theme.updated_at,
          expiresAt: theme.expires_at, // Only for temp_themes
        },
      },
    });

  } catch (error) {
    console.error('CSS cache stats error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get CSS cache stats',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/miniapp/css-cache/regenerate
 * Force CSS regeneration for a theme
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = getSupabaseServerClient();
    const api = createTeamAccountsApi(supabase);
    
    const body = await request.json();
    const { themeId, tempThemeId, accountThemeId, force } = RegenerateCSSSchema.parse(body);

    // Determine which table to update
    let query;
    let updateQuery;
    let tableName: string;
    
    if (tempThemeId) {
      query = supabase.from('temp_themes').select('*').eq('id', tempThemeId);
      updateQuery = (data: any) => supabase.from('temp_themes').update(data).eq('id', tempThemeId);
      tableName = 'temp_themes';
    } else if (accountThemeId) {
      query = supabase.from('account_themes').select('*').eq('id', accountThemeId);
      updateQuery = (data: any) => supabase.from('account_themes').update(data).eq('id', accountThemeId);
      tableName = 'account_themes';
    } else if (themeId) {
      query = supabase.from('themes').select('*').eq('id', themeId);
      updateQuery = (data: any) => supabase.from('themes').update(data).eq('id', themeId);
      tableName = 'themes';
    } else {
      return NextResponse.json(
        { error: 'Must provide themeId, tempThemeId, or accountThemeId' },
        { status: 400 }
      );
    }

    const { data: theme, error } = await query.single();

    if (error) {
      console.error(`Error fetching theme from ${tableName}:`, error);
      return NextResponse.json(
        { error: 'Theme not found' },
        { status: 404 }
      );
    }

    // Force CSS regeneration
    console.log(`🔄 Force regenerating CSS for ${tableName}:${theme.id}`);
    const enhancedConfig = await forceCSSRegeneration(theme.config);

    // Update the theme with new CSS
    const { error: updateError } = await updateQuery({
      optimized_css: enhancedConfig.optimizedCSS,
      css_hash: enhancedConfig.cssHash,
      css_metadata: enhancedConfig.cssMetadata,
      config_md5: enhancedConfig.configMd5,
      updated_at: new Date().toISOString(),
    });

    if (updateError) {
      console.error(`Error updating ${tableName}:`, updateError);
      return NextResponse.json(
        { error: 'Failed to update theme with new CSS' },
        { status: 500 }
      );
    }

    console.log(`✅ CSS regenerated for ${tableName}:${theme.id}`, {
      cssHash: enhancedConfig.cssHash,
      configMd5: enhancedConfig.configMd5,
      cssSize: enhancedConfig.cssMetadata?.stats.minifiedSize || 0,
    });

    return NextResponse.json({
      success: true,
      data: {
        themeId: theme.id,
        tableName,
        cssHash: enhancedConfig.cssHash,
        configMd5: enhancedConfig.configMd5,
        cssSize: enhancedConfig.cssMetadata?.stats.minifiedSize || 0,
        stats: enhancedConfig.cssMetadata?.stats,
      },
    });

  } catch (error) {
    console.error('CSS regeneration error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to regenerate CSS',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/miniapp/css-cache
 * Clear CSS cache for a theme
 */
export async function DELETE(request: NextRequest) {
  try {
    const supabase = getSupabaseServerClient();
    
    const url = new URL(request.url);
    const params = Object.fromEntries(url.searchParams.entries());
    const { themeId, tempThemeId, accountThemeId } = GetCacheStatsSchema.parse(params);

    // Determine which table to update
    let updateQuery;
    let tableName: string;
    
    if (tempThemeId) {
      updateQuery = supabase.from('temp_themes').update({
        optimized_css: null,
        css_hash: null,
        css_metadata: null,
        updated_at: new Date().toISOString(),
      }).eq('id', tempThemeId);
      tableName = 'temp_themes';
    } else if (accountThemeId) {
      updateQuery = supabase.from('account_themes').update({
        optimized_css: null,
        css_hash: null,
        css_metadata: null,
        updated_at: new Date().toISOString(),
      }).eq('id', accountThemeId);
      tableName = 'account_themes';
    } else if (themeId) {
      updateQuery = supabase.from('themes').update({
        optimized_css: null,
        css_hash: null,
        css_metadata: null,
        updated_at: new Date().toISOString(),
      }).eq('id', themeId);
      tableName = 'themes';
    } else {
      return NextResponse.json(
        { error: 'Must provide themeId, tempThemeId, or accountThemeId' },
        { status: 400 }
      );
    }

    const { error } = await updateQuery;

    if (error) {
      console.error(`Error clearing CSS cache for ${tableName}:`, error);
      return NextResponse.json(
        { error: 'Failed to clear CSS cache' },
        { status: 500 }
      );
    }

    console.log(`🧹 CSS cache cleared for ${tableName}`);

    return NextResponse.json({
      success: true,
      message: `CSS cache cleared for ${tableName}`,
    });

  } catch (error) {
    console.error('CSS cache clear error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to clear CSS cache',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
