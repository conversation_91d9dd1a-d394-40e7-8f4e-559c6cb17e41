import { enhanceRouteHandler } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { createCorsResponse } from '~/lib/cors';
import { z } from 'zod';

const SubscribeSchema = z.object({
  userId: z.string(),
  planId: z.string(),
  interval: z.enum(['month', 'year']).optional(),
  businessInfo: z.object({
    name: z.string(),
    type: z.string().optional(),
    industry: z.string().optional(),
    address: z.string().optional(),
    phone: z.string().optional(),
    email: z.string().optional(),
    description: z.string().optional()
  })
});

/**
 * API endpoint for Mini App subscription and business creation
 * This endpoint handles:
 * 1. Creating subscription (or free plan)
 * 2. Creating business account
 * 3. Generating sample data
 * 4. Setting up Mini App theme
 */
export const POST = enhanceRouteHandler(
  async ({ request, body }) => {
    const logger = await getLogger();
    const adminClient = getSupabaseServerAdminClient();

    try {
      const { userId, planId, interval, businessInfo } = body;

      logger.info(
        { userId, planId, interval, businessName: businessInfo.name },
        'Processing Mini App subscription and business creation'
      );

      // 1. Verify user exists
      const { data: user, error: userError } = await adminClient.auth.admin.getUserById(userId);
      
      if (userError || !user) {
        throw new Error('User not found');
      }

      // 2. Create team account using the existing function
      const { data: teamData, error: teamError } = await adminClient.rpc(
        'create_team_account',
        {
          account_name: businessInfo.name,
        },
      );

      if (teamError || !teamData) {
        logger.error({ error: teamError }, 'Failed to create team account');
        throw new Error('Failed to create business account');
      }

      const teamId = teamData.id;
      const teamSlug = teamData.slug;

      logger.info({ teamId, teamSlug }, 'Team account created successfully');

      // 3. Update account with additional business info
      const { error: updateError } = await adminClient
        .from('accounts')
        .update({
          email: businessInfo.email || user.user.email,
          phone: businessInfo.phone,
          public_data: {
            industry: businessInfo.industry || 'education',
            business_type: businessInfo.type || 'school',
            address: businessInfo.address,
            description: businessInfo.description,
            created_via: 'miniapp',
            plan_id: planId,
            subscription_interval: interval
          }
        })
        .eq('id', teamId);

      if (updateError) {
        logger.error({ error: updateError }, 'Failed to update account info');
      }

      // 4. Create sample data based on industry
      const industry = businessInfo.industry || 'education';
      let sampleDataResult = null;

      try {
        if (industry === 'education') {
          // Use education sample data function
          const { data: sampleData, error: sampleError } = await adminClient
            .rpc('create_education_sample_data', {
              p_account_id: teamId,
            });

          if (sampleError) {
            logger.error({ error: sampleError }, 'Failed to create education sample data');
          } else {
            sampleDataResult = sampleData;
            logger.info('Education sample data created successfully');
          }
        } else {
          // Use commerce sample data function for other industries
          const { data: sampleData, error: sampleError } = await adminClient
            .rpc('create_sample_data_for_account', {
              p_account_id: teamId,
              p_industry_template: JSON.stringify({
                industry: industry,
                categories: [],
                products: [],
                branches: [],
                account_themes: []
              })
            });

          if (sampleError) {
            logger.error({ error: sampleError }, 'Failed to create sample data');
          } else {
            sampleDataResult = sampleData;
            logger.info('Sample data created successfully');
          }
        }
      } catch (sampleError) {
        logger.error({ error: sampleError }, 'Sample data creation failed, continuing...');
      }

      // 5. Create default theme for Mini App (if not exists)
      let themeId = null;
      try {
        const { data: existingTheme } = await adminClient
          .from('account_themes')
          .select('id')
          .eq('account_id', teamId)
          .single();

        if (existingTheme) {
          themeId = existingTheme.id;
        } else {
          // Create default theme
          const { data: newTheme, error: themeError } = await adminClient
            .from('account_themes')
            .insert({
              account_id: teamId,
              theme_name: `${businessInfo.name} Theme`,
              mini_app_id: process.env.NEXT_PUBLIC_ZALO_MINI_APP_ID || '3423472366583461276',
              primary_color: '#4CAF50',
              secondary_color: '#FFC107',
              logo_url: null,
              is_active: true
            })
            .select('id')
            .single();

          if (!themeError && newTheme) {
            themeId = newTheme.id;
            logger.info({ themeId }, 'Default theme created');
          }
        }
      } catch (themeError) {
        logger.error({ error: themeError }, 'Theme creation failed, continuing...');
      }

      // 6. Handle subscription based on plan
      let subscriptionResult = null;
      if (planId !== 'free') {
        // TODO: Implement actual subscription creation with billing provider
        // For now, just log the intent
        logger.info(
          { planId, interval, teamId },
          'Subscription creation needed (not implemented yet)'
        );
        
        subscriptionResult = {
          planId,
          interval,
          status: 'pending',
          message: 'Subscription creation will be implemented with billing provider'
        };
      }

      // 7. Return success response
      const response = {
        success: true,
        data: {
          business: {
            id: teamId,
            slug: teamSlug,
            name: businessInfo.name,
            industry: industry,
            themeId: themeId
          },
          subscription: subscriptionResult,
          sampleData: sampleDataResult ? {
            created: true,
            stats: sampleDataResult.stats || {}
          } : null,
          accessUrl: `/home/<USER>
          miniAppAccess: themeId ? {
            themeId: themeId,
            url: `${process.env.NEXT_PUBLIC_APP_URL}?themeId=${themeId}`
          } : null
        },
        message: 'Business created successfully'
      };

      logger.info(
        { 
          teamId, 
          teamSlug, 
          planId,
          hasTheme: !!themeId,
          hasSampleData: !!sampleDataResult
        },
        'Mini App subscription and business creation completed'
      );

      return createCorsResponse(request, response, 200);

    } catch (error: any) {
      logger.error(
        {
          error: error.message,
          stack: error.stack,
          userId: body?.userId,
          planId: body?.planId
        },
        'Failed to process Mini App subscription'
      );

      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Failed to create business',
          details: error.message
        },
        500
      );
    }
  },
  { 
    schema: SubscribeSchema,
    auth: false 
  }
);
