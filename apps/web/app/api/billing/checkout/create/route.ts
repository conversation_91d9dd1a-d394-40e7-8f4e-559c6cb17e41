import { z } from 'zod';

import { getBillingGatewayProvider } from '@kit/billing-gateway';
import { enhanceRouteHandler } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import billingConfig from '~/config/billing.config';
import { createCorsResponse } from '~/lib/cors';

const CreateCheckoutSchema = z.object({
  accountId: z.string(),
  planId: z.string(),
  productId: z.string(),
  interval: z.enum(['month', 'year']).optional(),
  returnUrl: z.string().url(),
  enableDiscountField: z.boolean().optional(),
  customerEmail: z.string().email().optional(), // Add customer email support
  variantQuantities: z.array(
    z.object({
      variantId: z.string(),
      quantity: z.number(),
    }),
  ),
});

/**
 * API endpoint to create checkout session using existing SaaS billing system
 * This reuses the same billing service that SaaS web app uses
 */
export const POST = enhanceRouteHandler(
  async ({ request, body }) => {
    const logger = await getLogger();

    try {
      const {
        accountId,
        planId,
        productId,
        interval,
        returnUrl,
        enableDiscountField,
        customerEmail,
        variantQuantities,
      } = body;

      logger.info(
        { planId, productId, interval, accountId },
        'Creating checkout session using SaaS billing service',
      );

      // Find the plan from billing config (same as SaaS web app)
      const product = billingConfig.products.find((p) =>
        p.plans.some((plan) => plan.id === planId),
      );

      if (!product) {
        throw new Error(`Product not found for plan: ${planId}`);
      }

      const plan = product.plans.find((p) => p.id === planId);
      if (!plan) {
        throw new Error(`Plan not found: ${planId}`);
      }

      // Get billing gateway provider (same service used by SaaS web app)
      const client = getSupabaseServerClient();
      const billingService = await getBillingGatewayProvider(client);

      // Validate that accountId is a valid UUID (account should exist)
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(accountId)) {
        throw new Error('Invalid account ID. Account must be created first.');
      }

      // Verify account exists
      const { data: account, error: accountError } = await client
        .from('accounts')
        .select('id, name, public_data')
        .eq('id', accountId)
        .single();

      if (accountError || !account) {
        throw new Error('Account not found. Please create account first.');
      }

      logger.info(
        { accountId, accountName: account.name },
        'Account verified for checkout',
      );

      // Create checkout session using the existing account
      const checkoutParams: any = {
        accountId, // Valid UUID of existing account
        plan,
        returnUrl,
        enableDiscountField: enableDiscountField ?? true,
        variantQuantities: variantQuantities || [
          {
            variantId: productId,
            quantity: 1,
          },
        ],
        // Add metadata to track Mini App source
        metadata: {
          source: 'miniapp',
          accountName: account.name,
          planId,
          interval: interval || 'month',
        },
      };

      // Add customer email if provided for auto-fill
      if (customerEmail) {
        checkoutParams.customerEmail = customerEmail;
      }

      logger.info(
        { checkoutParams },
        'Creating checkout session with billing gateway',
      );

      // This calls the same createCheckoutSession method used by SaaS web app
      const { checkoutToken, sessionId } =
        await billingService.createCheckoutSession(checkoutParams);

      logger.info(
        {
          checkoutToken: checkoutToken ? 'generated' : 'null',
          sessionId,
          accountId,
        },
        'Checkout session created successfully',
      );

      const response = {
        success: true,
        data: {
          checkoutToken, // This is the Lemon Squeezy checkout URL
          sessionId: sessionId || accountId, // Use real session ID from billing service
          accountId, // Also include account ID for reference
          status: 'pending_payment',
          planInfo: {
            planId,
            productId,
            interval,
          },
        },
        message: 'Checkout session created successfully',
      };

      return createCorsResponse(request, response, 200);
    } catch (error: any) {
      logger.error(
        {
          error: error.message,
          stack: error.stack,
          planId: body?.planId,
          productId: body?.productId,
        },
        'Failed to create checkout session',
      );

      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Failed to create checkout session',
          details: error.message,
        },
        500,
      );
    }
  },
  {
    schema: CreateCheckoutSchema,
    auth: false,
  },
);
