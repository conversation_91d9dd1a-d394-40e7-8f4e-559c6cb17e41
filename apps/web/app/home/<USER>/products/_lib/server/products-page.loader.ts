'use server';

import { SupabaseClient } from '@supabase/supabase-js';

import { Database } from '~/lib/database.types';

export type Product = {
  id: string;
  name: string;
  price: number;
  category_name: string | null;
  type: 'physical' | 'digital' | 'service';
  status: 'active' | 'inactive' | 'draft';
  image_url: string | null;
  created_at: string;
  track_inventory: boolean;
  inventory: Array<{
    branch_id: string;
    stock: number;
    reserved_stock: number;
    attribute_id?: string;
  }>;
  branch_products: Array<{
    branch_id: string;
    is_active: boolean;
  }>;
  product_attributes: Array<{
    id: string;
    name: string;
    value: string;
    price_modifier: number;
  }>;
};

const ITEMS_PER_PAGE = 10;

export async function loadProducts(
  client: SupabaseClient<Database>,
  accountId: string,
  page: number = 1,
  searchQuery?: string,
): Promise<{
  data: Product[];
  total: number;
}> {
  if (!accountId) {
    throw new Error('Account ID is required');
  }

  try {
    let countQuery = client
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('account_id', accountId);

    if (searchQuery) {
      countQuery = countQuery.ilike('name', `%${searchQuery}%`);
    }

    const { count } = await countQuery;
    const total = count || 0;
    const offset = (page - 1) * ITEMS_PER_PAGE;

    const now = new Date().toISOString();

    let query = client
      .from('products')
      .select(
        `
        *,
        categories (
          name
        ),
        inventory (
          branch_id,
          stock,
          reserved_stock,
          attribute_id
        ),
        branch_products (
          branch_id,
          is_active
        ),
        product_attributes (
          id,
          name,
          value,
          price_modifier
        ),
        flash_sale:flash_sale_products(
          flash_sale_id,
          discount_percentage,
          quantity_limit,
          quantity_sold,
          flash_sales!inner(
            id,
            name,
            start_time,
            end_time,
            status
          )
        )
      `,
      )
      .eq('account_id', accountId);

    if (searchQuery) {
      query = query.ilike('name', `%${searchQuery}%`);
    }

    const { data, error } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + ITEMS_PER_PAGE - 1);

    if (error) {
      console.error('Supabase query error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        query: 'products.select',
        accountId,
      });
      throw error;
    }

    const products = (data || []).map((product) => {
      // Process flash sale data
      let flashSale = null;
      let finalPrice = product.price;

      if (product.flash_sale && product.flash_sale.length > 0) {
        const fsProduct = product.flash_sale[0];
        const fs = fsProduct.flash_sales;

        // Check if flash sale is active
        const currentTime = new Date();
        const startTime = new Date(fs.start_time);
        const endTime = new Date(fs.end_time);

        if (fs.status === 'active' && currentTime >= startTime && currentTime <= endTime) {
          const discountedPrice = product.price * (1 - fsProduct.discount_percentage / 100);

          flashSale = {
            id: fs.id,
            name: fs.name,
            discount_percentage: fsProduct.discount_percentage,
            start_time: fs.start_time,
            end_time: fs.end_time,
            quantity_limit: fsProduct.quantity_limit,
            quantity_sold: fsProduct.quantity_sold,
            original_price: product.price,
            discounted_price: discountedPrice
          };

          finalPrice = discountedPrice;
        }
      }

      return {
        ...product,
        category_name: product.categories?.name ?? null,
        flash_sale: flashSale,
        final_price: finalPrice
      };
    }) as unknown as Product[];

    return {
      data: products,
      total,
    };
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to load products:', {
      error: message,
      accountId,
      context: 'products-page.loader',
    });
    throw error; 
  }
}

// Thêm hàm mới để lấy product theo ID
export async function loadProductById(
  client: SupabaseClient<Database>,
  accountId: string,
  productId: string,
): Promise<Product | null> {
  try {
    const { data, error } = await client
      .from('products')
      .select(
        `
        *,
        categories (
          name
        ),
        inventory (
          branch_id,
          stock,
          reserved_stock,
          attribute_id
        ),
        branch_products (
          branch_id,
          is_active
        ),
        product_attributes (
          id,
          name,
          value,
          price_modifier
        )
      `,
      )
      .eq('account_id', accountId)
      .eq('id', productId)
      .single();

    if (error) {
      console.error('Supabase query error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        query: 'products.select.single',
        accountId,
        productId,
      });
      return null;
    }

    if (!data) return null;

    return {
      ...data,
      category_name: data.categories?.name ?? null,
    } as Product;
  } catch (error: any) {
    console.error('Failed to load product:', {
      error: error.message,
      accountId,
      productId,
      context: 'loadProductById',
    });
    return null;
  }
}
