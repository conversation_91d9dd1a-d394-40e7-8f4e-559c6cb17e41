import { redirect } from 'next/navigation';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import AddVehicleForm from './_components/add-vehicle-form';

interface Props {
  params: Promise<{
    account: string;
  }>;
}

export const metadata = {
  title: 'Thêm phương tiện',
};

async function NewVehiclePage({ params }: Props) {
  const i18n = await createI18nServerInstance();
  const resolvedParams = await params;
  const { user, account } = await loadTeamWorkspace(resolvedParams.account);

  if (!user) {
    redirect('/auth/sign-in');
  }

  return (
    <div className="flex flex-1 flex-col space-y-4 pb-36">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title="Thêm phương tiện"
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education`}>
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education/transportation`}>
              {i18n.t('education:breadcrumbs.transportation')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education/transportation/vehicles`}>
              Phương tiện
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>
              Thêm mới
            </AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-4xl">
        <AddVehicleForm account={resolvedParams.account} />
      </div>
    </div>
  );
}

export default NewVehiclePage;
