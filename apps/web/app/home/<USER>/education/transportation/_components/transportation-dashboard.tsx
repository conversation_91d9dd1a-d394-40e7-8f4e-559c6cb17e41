'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';

import { Bus, Clock, Navigation, Plus, Route, User } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

interface Props {
  account: string;
}

function TransportationDashboard({ account }: Props) {
  const { t } = useTranslation('education');
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalVehicles: 0,
    activeDrivers: 0,
    totalRoutes: 0,
    studentsTransported: 0,
  });
  const [activeVehicles, setActiveVehicles] = useState<any[]>([]);
  const [upcomingTrips, setUpcomingTrips] = useState<any[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const accountId = workspace.account.id;

      // Load vehicles count
      const { data: vehiclesData, error: vehiclesError } = await supabase
        .from('vehicles')
        .select('id, vehicle_number, status')
        .eq('account_id', accountId);

      if (vehiclesError) throw vehiclesError;

      // Mock data for features not yet implemented
      const driversData: any[] = [];
      const routesData: any[] = [];

      // TODO: Implement these tables when needed:
      // - drivers
      // - routes

      // Mock students transportation data
      const studentsData: any[] = [];

      // TODO: Implement student_transportation table when needed

      // Mock vehicle assignments data
      const assignmentsData: any[] = [];

      // TODO: Implement vehicle_assignments table when needed

      // Update stats
      setStats({
        totalVehicles: vehiclesData?.length || 0,
        activeDrivers: 0, // Will be implemented later
        totalRoutes: 0,   // Will be implemented later
        studentsTransported: 0, // Will be implemented later
      });

      // Set empty arrays for features not yet implemented
      setActiveVehicles([]);
      setUpcomingTrips([]);
    } catch (error) {
      console.error('Error loading transportation dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto max-w-7xl space-y-6 px-4 py-6">
        <div className="flex items-center justify-center py-12">
          <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-7xl space-y-8 px-4 py-6">
      {/* Modern Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 via-cyan-600 to-teal-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Bus className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">Quản lý giao thông</h1>
                  <p className="text-white/90 text-lg">Quản lý phương tiện và lộ trình đưa đón</p>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Bus className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.totalVehicles}</div>
                      <div className="text-white/80 text-sm">Tổng phương tiện</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <User className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.activeDrivers}</div>
                      <div className="text-white/80 text-sm">Tài xế hoạt động</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Route className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.totalRoutes}</div>
                      <div className="text-white/80 text-sm">Tuyến đường</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Navigation className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.studentsTransported}</div>
                      <div className="text-white/80 text-sm">Học viên đưa đón</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" asChild className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm transition-all duration-200">
                <Link href={`/home/<USER>/education/transportation/vehicles`}>
                  <Bus className="mr-2 h-4 w-4" />
                  Xem phương tiện
                </Link>
              </Button>
              <Button asChild className="bg-white text-blue-600 hover:bg-white/90 transition-all duration-200">
                <Link
                  href={`/home/<USER>/education/transportation/vehicles/new`}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Thêm phương tiện
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Tổng phương tiện
                </p>
                <p className="text-2xl font-bold">{stats.totalVehicles}</p>
              </div>
              <Bus className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Tài xế hoạt động
                </p>
                <p className="text-2xl font-bold">{stats.activeDrivers}</p>
              </div>
              <User className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Tuyến đường
                </p>
                <p className="text-2xl font-bold">{stats.totalRoutes}</p>
              </div>
              <Route className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Học viên đưa đón
                </p>
                <p className="text-2xl font-bold">
                  {stats.studentsTransported}
                </p>
              </div>
              <Navigation className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Tổng quan</TabsTrigger>
          <TabsTrigger value="vehicles">Phương tiện</TabsTrigger>
          <TabsTrigger value="drivers">Tài xế</TabsTrigger>
          <TabsTrigger value="routes">Tuyến đường</TabsTrigger>
          <TabsTrigger value="tracking">Theo dõi</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Active Vehicles */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Bus className="h-5 w-5" />
                  <span>Phương tiện đang hoạt động</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {activeVehicles.map((vehicle) => (
                    <div
                      key={vehicle.id}
                      className="flex items-center justify-between rounded-lg border p-3"
                    >
                      <div className="flex-1">
                        <h4 className="font-medium">{vehicle.vehicleNumber}</h4>
                        <p className="text-muted-foreground text-sm">
                          {vehicle.driver}
                        </p>
                        <div className="mt-2 flex items-center space-x-2">
                          <Badge variant="outline">{vehicle.route}</Badge>
                          <Badge className="bg-green-100 text-green-800">
                            {vehicle.status === 'active' ? 'Hoạt động' : 'Dừng'}
                          </Badge>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {vehicle.currentLocation}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Upcoming Trips */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Clock className="h-5 w-5" />
                  <span>Chuyến đi sắp tới</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingTrips.map((trip) => (
                    <div
                      key={trip.id}
                      className="flex items-center space-x-3 rounded-lg border p-3"
                    >
                      <div className="flex-shrink-0">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                          <Clock className="h-5 w-5 text-blue-600" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium">{trip.route}</h4>
                        <p className="text-muted-foreground text-sm">
                          {trip.vehicle}
                        </p>
                        <div className="mt-1 flex items-center space-x-2">
                          <span className="text-muted-foreground text-xs">
                            {trip.time}
                          </span>
                          <span className="text-xs">•</span>
                          <span className="text-muted-foreground text-xs">
                            {trip.students} học viên
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="vehicles">
          <Card>
            <CardContent className="p-6">
              <div className="py-12 text-center">
                <Bus className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Quản lý phương tiện
                </h3>
                <p className="mb-6 text-gray-500">
                  Quản lý đội xe và thông tin phương tiện.
                </p>
                <Button asChild>
                  <Link
                    href={`/home/<USER>/education/transportation/vehicles/new`}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Thêm phương tiện đầu tiên
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="drivers">
          <Card>
            <CardContent className="p-6">
              <div className="py-12 text-center">
                <User className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Quản lý tài xế
                </h3>
                <p className="mb-6 text-gray-500">
                  Quản lý thông tin tài xế và giấy phép lái xe.
                </p>
                <Button asChild>
                  <Link
                    href={`/home/<USER>/education/transportation/drivers/new`}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Thêm tài xế
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="routes">
          <Card>
            <CardContent className="p-6">
              <div className="py-12 text-center">
                <Route className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Quản lý tuyến đường
                </h3>
                <p className="mb-6 text-gray-500">
                  Tạo và quản lý các tuyến đường đưa đón.
                </p>
                <Button asChild>
                  <Link
                    href={`/home/<USER>/education/transportation/routes/new`}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Tạo tuyến đường
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tracking">
          <Card>
            <CardContent className="p-6">
              <div className="py-12 text-center">
                <Navigation className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Theo dõi GPS
                </h3>
                <p className="mb-6 text-gray-500">
                  Theo dõi vị trí thời gian thực của các phương tiện.
                </p>
                <Button asChild>
                  <Link
                    href={`/home/<USER>/education/transportation/tracking`}
                  >
                    <Navigation className="mr-2 h-4 w-4" />
                    Xem bản đồ theo dõi
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default TransportationDashboard;
