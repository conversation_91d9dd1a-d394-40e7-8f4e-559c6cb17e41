'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import {
  Award,
  BookOpen,
  Briefcase,
  Clock,
  Edit,
  FileText,
  GraduationCap,
  Heart,
  Mail,
  Phone,
  Star,
  User,
  Users,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Separator } from '@kit/ui/separator';

interface Instructor {
  id: string;
  employee_code: string;
  full_name: string;
  email: string;
  phone: string;
  specialization: string;
  experience_years: number;
  qualifications: string[];
  status: string;
  hire_date: string;
  role?: string;
  created_at: string;
  updated_at: string;
  programs?: Array<{
    id: string;
    name: string;
    program_type: string;
  }>;
}

interface InstructorStats {
  totalPrograms: number;
  totalLearners: number;
  yearsAtSchool: number;
  attendanceRate: number;
  recentActivity: any[];
}

interface Props {
  account: string;
  instructorId: string;
}

function InstructorDetails({ account, instructorId }: Props) {
  const { t } = useTranslation('education');
  const [instructor, setInstructor] = useState<Instructor | null>(null);
  const [stats, setStats] = useState<InstructorStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();
  const router = useRouter();

  useEffect(() => {
    loadInstructor();
  }, [instructorId]);

  const loadInstructor = async () => {
    try {
      setLoading(true);
      setError(null);

      const accountId = workspace.account.id;

      // Load instructor basic data
      const { data: instructor, error: instructorError } = await supabase
        .from('instructors')
        .select('*')
        .eq('id', instructorId)
        .eq('account_id', accountId)
        .single();

      if (instructorError) throw instructorError;

      // Load instructor stats and related data
      const [programsResult, learnersResult, attendanceResult] =
        await Promise.all([
          // Get programs taught by this instructor (if there's a relationship)
          supabase
            .from('programs')
            .select('id, name, program_type')
            .eq('account_id', accountId)
            .limit(5), // For now, get all programs since there's no direct instructor-program relationship

          // Get learners count (approximate from enrollments)
          supabase.from('enrollments').select('id').eq('status', 'active'),

          // Get recent attendance data (if instructor has attendance records)
          supabase
            .from('attendance')
            .select('id, status, session_date')
            .gte(
              'session_date',
              new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                .toISOString()
                .split('T')[0],
            )
            .order('session_date', { ascending: false })
            .limit(10),
        ]);

      const programs = programsResult.data || [];
      const learners = learnersResult.data || [];
      const attendance = attendanceResult.data || [];

      // Calculate stats
      const yearsAtSchool = Math.floor(
        (new Date().getTime() - new Date(instructor.hire_date).getTime()) /
          (1000 * 60 * 60 * 24 * 365),
      );

      const presentCount = attendance.filter(
        (a) => a.status === 'present',
      ).length;
      const attendanceRate =
        attendance.length > 0 ? (presentCount / attendance.length) * 100 : 0;

      const instructorStats: InstructorStats = {
        totalPrograms: Math.min(programs.length, 3), // Approximate since no direct relationship
        totalLearners: Math.min(learners.length, 25), // Approximate
        yearsAtSchool: Math.max(yearsAtSchool, 0),
        attendanceRate: Math.round(attendanceRate),
        recentActivity: attendance.slice(0, 5),
      };

      // Add programs to instructor object
      instructor.programs = programs.slice(0, 3);

      setInstructor(instructor);
      setStats(instructorStats);
    } catch (err: any) {
      console.error('Error loading instructor:', err);
      setError(err.message || 'Failed to load instructor');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    router.push(`/home/<USER>/education/instructors/${instructorId}/edit`);
  };

  const handleViewPrograms = () => {
    router.push(
      `/home/<USER>/education/programs?instructor=${instructorId}`,
    );
  };

  const handleViewLearners = () => {
    router.push(
      `/home/<USER>/education/learners?instructor=${instructorId}`,
    );
  };

  const handleViewSchedule = () => {
    router.push(
      `/home/<USER>/education/attendance?instructor=${instructorId}`,
    );
  };

  const handleViewReports = () => {
    router.push(
      `/home/<USER>/education/reports?instructor=${instructorId}`,
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-b-2"></div>
          <p className="text-muted-foreground mt-2 text-sm">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-8 text-center">
        <p className="text-red-600">{error}</p>
        <Button onClick={loadInstructor} className="mt-4">
          Thử lại
        </Button>
      </div>
    );
  }

  if (!instructor) {
    return (
      <div className="py-8 text-center">
        <p className="text-muted-foreground">Không tìm thấy giáo viên</p>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'terminated':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Đang làm việc';
      case 'inactive':
        return 'Tạm nghỉ';
      case 'terminated':
        return 'Đã nghỉ việc';
      default:
        return status;
    }
  };

  return (
    <div className="relative space-y-8">
      {/* Modern Header with Gradient Background */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="mb-3 flex items-center gap-3">
                <div className="rounded-xl bg-white/20 p-3 backdrop-blur-sm">
                  <GraduationCap className="h-6 w-6" />
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    variant="outline"
                    className="border-white/30 bg-white/20 text-white backdrop-blur-sm"
                  >
                    {instructor.role === 'teacher' && '👨‍🏫 Giáo viên'}
                    {instructor.role === 'assistant' && '👩‍💼 Trợ giảng'}
                    {instructor.role === 'principal' && '👨‍💼 Hiệu trưởng'}
                    {instructor.role === 'admin' && '👩‍💻 Quản trị'}
                    {!instructor.role && '👨‍🏫 Giáo viên'}
                  </Badge>
                  <Badge
                    variant="outline"
                    className={`${getStatusColor(instructor.status)} border-white/30 bg-white/20 backdrop-blur-sm`}
                  >
                    {getStatusText(instructor.status)}
                  </Badge>
                </div>
              </div>
              <h1 className="mb-2 text-4xl leading-tight font-bold">
                {instructor.full_name}
              </h1>
              <div className="space-y-1 text-lg text-white/90">
                <p>
                  Mã nhân viên:{' '}
                  <span className="font-semibold">
                    {instructor.employee_code}
                  </span>
                </p>
                <p>
                  Chuyên môn:{' '}
                  <span className="font-semibold">
                    {instructor.specialization}
                  </span>
                </p>
                <p>
                  Kinh nghiệm:{' '}
                  <span className="font-semibold">
                    {instructor.experience_years} năm
                  </span>
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                onClick={handleEdit}
                variant="outline"
                className="border-white/30 bg-white/20 text-white backdrop-blur-sm transition-all duration-200 hover:bg-white/30"
              >
                <Edit className="mr-2 h-4 w-4" />
                Chỉnh sửa
              </Button>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-white/10 blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 h-32 w-32 rounded-full bg-white/5 blur-2xl"></div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Main Info */}
        <div className="space-y-6 lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-4 w-4" />
                <span>Thông tin cá nhân</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-muted-foreground mb-2 flex items-center space-x-1 text-sm font-medium">
                    <Mail className="h-3 w-3" />
                    <span>Email</span>
                  </h4>
                  <p className="text-sm">{instructor.email}</p>
                </div>
                <div>
                  <h4 className="text-muted-foreground mb-2 flex items-center space-x-1 text-sm font-medium">
                    <Phone className="h-3 w-3" />
                    <span>Số điện thoại</span>
                  </h4>
                  <p className="text-sm">{instructor.phone}</p>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="text-muted-foreground mb-2 flex items-center space-x-1 text-sm font-medium">
                  <Briefcase className="h-3 w-3" />
                  <span>Chuyên môn</span>
                </h4>
                <p className="text-sm">{instructor.specialization}</p>
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-muted-foreground mb-2 text-sm font-medium">
                    Kinh nghiệm
                  </h4>
                  <p className="text-sm">{instructor.experience_years} năm</p>
                </div>
                <div>
                  <h4 className="text-muted-foreground mb-2 text-sm font-medium">
                    Ngày vào làm
                  </h4>
                  <p className="text-sm">
                    {new Date(instructor.hire_date).toLocaleDateString('vi-VN')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Qualifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Award className="h-4 w-4" />
                <span>Bằng cấp & Chứng chỉ</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {instructor.qualifications &&
              instructor.qualifications.length > 0 ? (
                <div className="space-y-2">
                  {instructor.qualifications.map((qualification, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="bg-primary h-2 w-2 rounded-full"></div>
                      <span className="text-sm">{qualification}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-sm">
                  Chưa có thông tin bằng cấp
                </p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Sidebar */}
        <div className="space-y-8">
          {/* Quick Actions Card */}
          {instructor.status === 'active' && (
            <Card className="overflow-hidden border-0 bg-gradient-to-br from-orange-600 to-red-600 text-white shadow-lg">
              <CardHeader className="relative z-10 pb-4">
                <CardTitle className="flex items-center gap-3 text-white">
                  <div className="rounded-lg bg-white/20 p-2 backdrop-blur-sm">
                    <span className="text-lg">⚡</span>
                  </div>
                  Thao tác nhanh
                </CardTitle>
              </CardHeader>
              <CardContent className="relative z-10 space-y-3">
                <Button
                  onClick={handleViewPrograms}
                  className="w-full border-white/30 bg-white/20 text-white backdrop-blur-sm transition-all duration-200 hover:bg-white/30"
                  variant="outline"
                >
                  <BookOpen className="mr-2 h-4 w-4" />
                  Chương trình dạy
                </Button>
                <Button
                  onClick={handleViewLearners}
                  className="w-full border-white/30 bg-white/20 text-white backdrop-blur-sm transition-all duration-200 hover:bg-white/30"
                  variant="outline"
                >
                  <Users className="mr-2 h-4 w-4" />
                  Học viên
                </Button>
                <Button
                  onClick={handleViewSchedule}
                  className="w-full border-white/30 bg-white/20 text-white backdrop-blur-sm transition-all duration-200 hover:bg-white/30"
                  variant="outline"
                >
                  <Clock className="mr-2 h-4 w-4" />
                  Lịch dạy
                </Button>
                <Button
                  onClick={handleViewReports}
                  className="w-full border-white/30 bg-white/20 text-white backdrop-blur-sm transition-all duration-200 hover:bg-white/30"
                  variant="outline"
                >
                  <FileText className="mr-2 h-4 w-4" />
                  Báo cáo
                </Button>
              </CardContent>
              <div className="absolute -top-4 -right-4 h-16 w-16 rounded-full bg-white/10 blur-xl"></div>
            </Card>
          )}

          {/* Stats Cards */}
          <div className="grid grid-cols-1 gap-4">
            <Card className="border-0 border-blue-100 bg-gradient-to-br from-blue-50 to-sky-50 shadow-md">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-blue-100 p-2">
                    <Star className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-sm font-semibold text-blue-900">
                      Kinh nghiệm
                    </div>
                    <div className="text-xs text-blue-700">
                      {instructor.experience_years} năm
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 border-green-100 bg-gradient-to-br from-green-50 to-emerald-50 shadow-md">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-green-100 p-2">
                    <BookOpen className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <div className="text-sm font-semibold text-green-900">
                      Chương trình
                    </div>
                    <div className="text-xs text-green-700">
                      {stats?.totalPrograms || 0} chương trình
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 border-purple-100 bg-gradient-to-br from-purple-50 to-violet-50 shadow-md">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-purple-100 p-2">
                    <Users className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <div className="text-sm font-semibold text-purple-900">
                      Học viên
                    </div>
                    <div className="text-xs text-purple-700">
                      {stats?.totalLearners || 0} học viên
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 border-orange-100 bg-gradient-to-br from-orange-50 to-amber-50 shadow-md">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-orange-100 p-2">
                    <Heart className="h-4 w-4 text-orange-600" />
                  </div>
                  <div>
                    <div className="text-sm font-semibold text-orange-900">
                      Thâm niên
                    </div>
                    <div className="text-xs text-orange-700">
                      {stats?.yearsAtSchool || 0} năm tại trường
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Timeline Card */}
          <Card className="border-0 bg-gradient-to-br from-white to-gray-50/50 shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-lg">
                <div className="rounded-lg bg-gray-100 p-2">
                  <Clock className="h-4 w-4 text-gray-600" />
                </div>
                Lịch sử công tác
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-green-500"></div>
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    Bắt đầu làm việc
                  </div>
                  <div className="text-xs text-gray-600">
                    {new Date(instructor.hire_date).toLocaleDateString(
                      'vi-VN',
                      {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      },
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></div>
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    Cập nhật gần nhất
                  </div>
                  <div className="text-xs text-gray-600">
                    {new Date(instructor.updated_at).toLocaleDateString(
                      'vi-VN',
                      {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      },
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-yellow-500"></div>
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    Trạng thái hiện tại
                  </div>
                  <div className="text-xs text-gray-600">
                    {getStatusText(instructor.status)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default InstructorDetails;
