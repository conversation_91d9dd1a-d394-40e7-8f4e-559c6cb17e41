'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Textarea } from '@kit/ui/textarea';

interface Program {
  id: string;
  name: string;
  program_type: string;
  description: string;
  age_group: string; // Use age_group instead of age_range
  duration_weeks: number; // Use duration_weeks instead of duration_months
  capacity: number;
  schedule: any;
  status: string;
}

interface Props {
  account: string;
  programId: string;
}

function EditProgramForm({ account, programId }: Props) {
  const { t } = useTranslation('education');
  const [program, setProgram] = useState<Program | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();
  const router = useRouter();

  // Form state with default values to prevent undefined
  const [formData, setFormData] = useState({
    name: '',
    program_type: '',
    description: '',
    age_group: '', // Use age_group instead of age_range
    duration_weeks: 12, // Use duration_weeks instead of duration_months
    capacity: 20,
    schedule: '',
    tuition_fee: 0, // Add missing field
    meal_fee: 0, // Add missing field
    status: 'active',
  });

  useEffect(() => {
    loadProgram();
  }, [programId]);

  const loadProgram = async () => {
    try {
      setLoading(true);
      setError(null);

      const accountId = workspace.account.id;

      const { data: program, error: programError } = await supabase
        .from('programs')
        .select('*')
        .eq('id', programId)
        .eq('account_id', accountId)
        .single();

      if (programError) throw programError;

      setProgram(program);

      // Populate form data with fallback values to prevent undefined
      setFormData({
        name: program.name || '',
        program_type: program.program_type || '',
        description: program.description || '',
        age_group: program.age_group || '',
        duration_weeks: program.duration_weeks || 12,
        capacity: program.capacity || 20,
        schedule: (typeof program.schedule === 'string' ? program.schedule : program.schedule?.schedule) || '',
        tuition_fee: program.schedule?.tuition_fee || 0, // Extract from schedule JSONB
        meal_fee: program.schedule?.meal_fee || 0, // Extract from schedule JSONB
        status: program.status || 'active',
      });
    } catch (err: any) {
      console.error('Error loading program:', err);
      setError(err.message || 'Failed to load program');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError(null);

      const accountId = workspace.account.id;

      const { error: updateError } = await supabase
        .from('programs')
        .update({
          name: formData.name,
          program_type: formData.program_type,
          description: formData.description,
          age_group: formData.age_group, // Use age_group instead of age_range
          duration_weeks: formData.duration_weeks, // Use duration_weeks instead of duration_months
          capacity: formData.capacity,
          schedule: {
            schedule: formData.schedule,
            tuition_fee: formData.tuition_fee, // Store in schedule JSONB
            meal_fee: formData.meal_fee, // Store in schedule JSONB
          },
          status: formData.status,
          updated_at: new Date().toISOString(),
        })
        .eq('id', programId)
        .eq('account_id', accountId);

      if (updateError) throw updateError;

      router.push(`/home/<USER>/education/programs/${programId}`);
    } catch (err: any) {
      console.error('Error updating program:', err);
      setError(err.message || 'Failed to update program');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value || (typeof prev[field] === 'number' ? 0 : ''), // Prevent undefined values
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-b-2"></div>
          <p className="text-muted-foreground mt-2 text-sm">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (error && !program) {
    return (
      <div className="py-8 text-center">
        <p className="text-red-600">{error}</p>
        <Button onClick={loadProgram} className="mt-4">
          Thử lại
        </Button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Thông tin chương trình</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Tên chương trình *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Nhập tên chương trình"
                required
              />
            </div>
            <div>
              <Label htmlFor="program_type">Loại chương trình *</Label>
              <Select
                value={formData.program_type}
                onValueChange={(value) =>
                  handleInputChange('program_type', value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn loại chương trình" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="nursery">Nhà trẻ</SelectItem>
                  <SelectItem value="preschool">Mầm non</SelectItem>
                  <SelectItem value="kindergarten">Mẫu giáo</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Mô tả</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Mô tả chương trình"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="age_group">Độ tuổi</Label>
              <Input
                id="age_group"
                value={formData.age_group}
                onChange={(e) => handleInputChange('age_group', e.target.value)}
                placeholder="VD: 3-4 tuổi"
              />
            </div>
            <div>
              <Label htmlFor="duration_weeks">Thời gian (tuần)</Label>
              <Input
                id="duration_weeks"
                type="number"
                value={formData.duration_weeks}
                onChange={(e) =>
                  handleInputChange('duration_weeks', parseInt(e.target.value) || 0)
                }
                min="1"
                max="260"
              />
            </div>
            <div>
              <Label htmlFor="capacity">Sức chứa</Label>
              <Input
                id="capacity"
                type="number"
                value={formData.capacity}
                onChange={(e) =>
                  handleInputChange('capacity', parseInt(e.target.value) || 0)
                }
                min="1"
                max="100"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="schedule">Lịch học</Label>
            <Input
              id="schedule"
              value={formData.schedule}
              onChange={(e) => handleInputChange('schedule', e.target.value)}
              placeholder="VD: Thứ 2 - Thứ 6, 7:30 - 16:30"
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Cấu trúc học phí</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="tuition_fee">Học phí (VNĐ)</Label>
              <Input
                id="tuition_fee"
                type="number"
                value={formData.tuition_fee}
                onChange={(e) =>
                  handleInputChange('tuition_fee', parseInt(e.target.value) || 0)
                }
                min="0"
                step="100000"
              />
            </div>
            <div>
              <Label htmlFor="meal_fee">Tiền ăn (VNĐ)</Label>
              <Input
                id="meal_fee"
                type="number"
                value={formData.meal_fee}
                onChange={(e) =>
                  handleInputChange('meal_fee', parseInt(e.target.value) || 0)
                }
                min="0"
                step="50000"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Trạng thái</CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <Label htmlFor="status">Trạng thái chương trình</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleInputChange('status', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Đang hoạt động</SelectItem>
                <SelectItem value="inactive">Tạm dừng</SelectItem>
                <SelectItem value="draft">Bản nháp</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {error && (
        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() =>
            router.push(`/home/<USER>/education/programs/${programId}`)
          }
        >
          Hủy
        </Button>
        <Button type="submit" disabled={saving}>
          {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
        </Button>
      </div>
    </form>
  );
}

export default EditProgramForm;
