'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Progress } from '@kit/ui/progress';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from 'sonner';

import {
  ArrowLeft,
  Clock,
  Heart,
  Play,
  Pause,
  RotateCcw,
  Star,
  Trophy,
  Users,
  Zap,
} from 'lucide-react';

import { GameEngine } from './game-engine';

interface Props {
  account: string;
  gameId: string;
}

interface Game {
  id: string;
  title: string;
  description: string;
  instructions: string;
  game_type: string;
  difficulty_level: number;
  estimated_duration: number;
  game_config: any;
  category: {
    name: string;
    icon: string;
    color: string;
  };
}

interface GameSession {
  id: string;
  started_at: string;
  score: number;
  max_score: number;
  is_completed: boolean;
}

function GamePlayer({ account, gameId }: Props) {
  const router = useRouter();
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  const [loading, setLoading] = useState(true);
  const [game, setGame] = useState<Game | null>(null);
  const [session, setSession] = useState<GameSession | null>(null);
  const [gameState, setGameState] = useState<'loading' | 'ready' | 'playing' | 'paused' | 'completed'>('loading');
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [currentScore, setCurrentScore] = useState(0);
  const [lives, setLives] = useState(3);

  useEffect(() => {
    loadGame();
  }, [gameId]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (gameState === 'playing') {
      interval = setInterval(() => {
        setTimeElapsed(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [gameState]);

  const loadGame = async () => {
    try {
      setLoading(true);
      const accountId = workspace.account.id;

      const { data: gameData, error } = await supabase
        .from('games')
        .select(`
          id,
          title,
          description,
          instructions,
          game_type,
          difficulty_level,
          estimated_duration,
          game_config,
          category:game_categories(name, icon, color)
        `)
        .eq('id', gameId)
        .eq('account_id', accountId)
        .single();

      if (error) throw error;
      if (!gameData) throw new Error('Game not found');

      setGame(gameData);
      setGameState('ready');
    } catch (error) {
      console.error('Error loading game:', error);
      toast.error('Không thể tải game');
      router.push(`/home/<USER>/education/games`);
    } finally {
      setLoading(false);
    }
  };

  const startGame = async () => {
    try {
      const accountId = workspace.account.id;
      const userId = workspace.user?.id;

      if (!userId) {
        toast.error('Vui lòng đăng nhập để chơi game');
        return;
      }

      // Create game session
      const { data: sessionData, error } = await supabase
        .from('game_sessions')
        .insert({
          account_id: accountId,
          game_id: gameId,
          student_id: userId,
          session_type: 'practice',
          max_score: 100,
        })
        .select()
        .single();

      if (error) throw error;

      setSession(sessionData);
      setGameState('playing');
      setTimeElapsed(0);
      setCurrentScore(0);
      setLives(3);

      toast.success('Game đã bắt đầu!');
    } catch (error) {
      console.error('Error starting game:', error);
      toast.error('Không thể bắt đầu game');
    }
  };

  const pauseGame = () => {
    setGameState('paused');
  };

  const resumeGame = () => {
    setGameState('playing');
  };

  const restartGame = () => {
    setGameState('ready');
    setTimeElapsed(0);
    setCurrentScore(0);
    setLives(3);
    setSession(null);
  };

  const completeGame = async (finalScore: number) => {
    try {
      if (!session) return;

      const accountId = workspace.account.id;
      const accuracy = Math.round((finalScore / session.max_score) * 100);

      // Update session
      const { error } = await supabase
        .from('game_sessions')
        .update({
          completed_at: new Date().toISOString(),
          duration_seconds: timeElapsed,
          score: finalScore,
          accuracy_percentage: accuracy,
          is_completed: true,
        })
        .eq('id', session.id);

      if (error) throw error;

      setGameState('completed');
      setCurrentScore(finalScore);

      // Update student progress
      await updateStudentProgress(finalScore, accuracy);

      // Check for achievements
      await checkAchievements(finalScore, accuracy);

      toast.success(`Game hoàn thành! Điểm: ${finalScore}/${session.max_score}`);
    } catch (error) {
      console.error('Error completing game:', error);
      toast.error('Lỗi khi lưu kết quả game');
    }
  };

  const checkAchievements = async (finalScore: number, accuracy: number) => {
    try {
      const accountId = workspace.account.id;
      const userId = workspace.user?.id;

      if (!userId || !session || !game) return;

      // Call achievement check API
      const response = await fetch('/api/games/check-achievements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: session.id,
          accountId,
          studentId: userId,
          gameId: game.id,
          score: finalScore,
          maxScore: session.max_score,
          accuracy,
          duration: timeElapsed,
          gameType: game.game_type,
        }),
      });

      if (response.ok) {
        const achievements = await response.json();
        if (achievements.length > 0) {
          // Show achievement notifications
          achievements.forEach((achievement: any) => {
            toast.success(`🏆 Đạt thành tích: ${achievement.title}!`, {
              duration: 5000,
            });
          });
        }
      }
    } catch (error) {
      console.error('Error checking achievements:', error);
    }
  };

  const updateStudentProgress = async (score: number, accuracy: number) => {
    try {
      const accountId = workspace.account.id;
      const userId = workspace.user?.id;

      if (!userId) return;

      // Get current progress
      const { data: currentProgress } = await supabase
        .from('student_game_progress')
        .select('*')
        .eq('student_id', userId)
        .eq('game_id', gameId)
        .single();

      const newTotalSessions = (currentProgress?.total_sessions || 0) + 1;
      const newTotalTime = (currentProgress?.total_time_seconds || 0) + timeElapsed;
      const newBestScore = Math.max(currentProgress?.best_score || 0, score);
      const newAverageScore = ((currentProgress?.average_score || 0) * (newTotalSessions - 1) + score) / newTotalSessions;

      const progressData = {
        account_id: accountId,
        student_id: userId,
        game_id: gameId,
        total_sessions: newTotalSessions,
        total_time_seconds: newTotalTime,
        best_score: newBestScore,
        average_score: newAverageScore,
        completion_rate: accuracy,
        last_played_at: new Date().toISOString(),
      };

      if (currentProgress) {
        await supabase
          .from('student_game_progress')
          .update(progressData)
          .eq('id', currentProgress.id);
      } else {
        await supabase
          .from('student_game_progress')
          .insert(progressData);
      }
    } catch (error) {
      console.error('Error updating progress:', error);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getDifficultyColor = (level: number) => {
    switch (level) {
      case 1: return 'bg-green-100 text-green-800';
      case 2: return 'bg-blue-100 text-blue-800';
      case 3: return 'bg-yellow-100 text-yellow-800';
      case 4: return 'bg-orange-100 text-orange-800';
      case 5: return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifficultyText = (level: number) => {
    switch (level) {
      case 1: return 'Dễ';
      case 2: return 'Trung bình';
      case 3: return 'Khó';
      case 4: return 'Rất khó';
      case 5: return 'Chuyên gia';
      default: return 'Không xác định';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải game...</p>
        </div>
      </div>
    );
  }

  if (!game) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">Game không tồn tại</p>
        <Button onClick={() => router.push(`/home/<USER>/education/games`)} className="mt-4">
          Quay lại Games
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-6xl space-y-6 px-4 py-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.push(`/home/<USER>/education/games`)}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Quay lại Games
        </Button>

        {gameState === 'playing' && (
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm">
              <Clock className="h-4 w-4" />
              {formatTime(timeElapsed)}
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Star className="h-4 w-4" />
              {currentScore}/{session?.max_score || 100}
            </div>
            <div className="flex items-center gap-1">
              {Array.from({ length: lives }).map((_, i) => (
                <Heart key={i} className="h-4 w-4 text-red-500 fill-current" />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Game Info */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-2xl mb-2">{game.title}</CardTitle>
              <p className="text-gray-600 mb-4">{game.description}</p>
              <div className="flex items-center gap-4">
                <Badge className={getDifficultyColor(game.difficulty_level)}>
                  {getDifficultyText(game.difficulty_level)}
                </Badge>
                <div className="flex items-center gap-1 text-sm text-gray-600">
                  <Clock className="h-4 w-4" />
                  {Math.floor(game.estimated_duration / 60)} phút
                </div>
                <Badge variant="outline">{game.category?.name}</Badge>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Game Area */}
      <Card className="border-0 shadow-lg min-h-[500px]">
        <CardContent className="p-8">
          {gameState === 'ready' && (
            <div className="text-center space-y-6">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold">Hướng dẫn chơi</h3>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  {game.instructions}
                </p>
              </div>
              <Button onClick={startGame} size="lg" className="bg-purple-600 hover:bg-purple-700">
                <Play className="h-5 w-5 mr-2" />
                Bắt đầu chơi
              </Button>
            </div>
          )}

          {gameState === 'playing' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold">Đang chơi: {game.title}</h3>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={pauseGame}>
                    <Pause className="h-4 w-4 mr-2" />
                    Tạm dừng
                  </Button>
                  <Button variant="outline" onClick={restartGame}>
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Chơi lại
                  </Button>
                </div>
              </div>

              {/* Game Content Area */}
              <GameEngine
                game={game}
                onScoreUpdate={setCurrentScore}
                onLivesUpdate={setLives}
                onGameComplete={completeGame}
                currentScore={currentScore}
                lives={lives}
              />
            </div>
          )}

          {gameState === 'paused' && (
            <div className="text-center space-y-6">
              <div className="text-6xl">⏸️</div>
              <h3 className="text-xl font-semibold">Game đã tạm dừng</h3>
              <div className="flex gap-4 justify-center">
                <Button onClick={resumeGame} className="bg-green-600 hover:bg-green-700">
                  <Play className="h-4 w-4 mr-2" />
                  Tiếp tục
                </Button>
                <Button variant="outline" onClick={restartGame}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Chơi lại
                </Button>
              </div>
            </div>
          )}

          {gameState === 'completed' && (
            <div className="text-center space-y-6">
              <div className="text-6xl">🏆</div>
              <h3 className="text-2xl font-semibold">Chúc mừng!</h3>
              <div className="space-y-2">
                <p className="text-lg">Điểm số: <span className="font-bold text-purple-600">{currentScore}/{session?.max_score}</span></p>
                <p className="text-gray-600">Thời gian: {formatTime(timeElapsed)}</p>
              </div>
              <div className="flex gap-4 justify-center">
                <Button onClick={restartGame} className="bg-purple-600 hover:bg-purple-700">
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Chơi lại
                </Button>
                <Button variant="outline" onClick={() => router.push(`/home/<USER>/education/games`)}>
                  Quay lại Games
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export { GamePlayer };
