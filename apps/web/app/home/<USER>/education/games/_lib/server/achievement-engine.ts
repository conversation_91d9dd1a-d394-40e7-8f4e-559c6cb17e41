import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

interface AchievementCriteria {
  gamesCompleted?: number;
  gameType?: string;
  minScore?: number;
  consecutiveDays?: number;
  maxTime?: number;
  gameCompleted?: boolean;
  minAccuracy?: number;
}

interface GameSession {
  id: string;
  game_id: string;
  student_id: string;
  score: number;
  max_score: number;
  accuracy_percentage: number;
  duration_seconds: number;
  completed_at: string;
  game: {
    game_type: string;
  };
}

export class AchievementEngine {
  private supabase;

  constructor() {
    this.supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookies().get(name)?.value;
          },
        },
      }
    );
  }

  async checkAchievements(session: GameSession, accountId: string) {
    try {
      // Get all achievements for this account
      const { data: achievements, error: achievementsError } = await this.supabase
        .from('achievements')
        .select('*')
        .eq('account_id', accountId)
        .eq('is_active', true);

      if (achievementsError) throw achievementsError;

      // Get student's existing achievements
      const { data: existingAchievements, error: existingError } = await this.supabase
        .from('student_achievements')
        .select('achievement_id')
        .eq('student_id', session.student_id)
        .eq('account_id', accountId);

      if (existingError) throw existingError;

      const existingAchievementIds = new Set(
        existingAchievements?.map(a => a.achievement_id) || []
      );

      const newAchievements = [];

      for (const achievement of achievements || []) {
        // Skip if student already has this achievement
        if (existingAchievementIds.has(achievement.id)) {
          continue;
        }

        const criteria: AchievementCriteria = achievement.criteria;
        let earned = false;

        switch (achievement.achievement_type) {
          case 'completion':
            earned = await this.checkCompletionAchievement(
              session.student_id,
              accountId,
              criteria
            );
            break;

          case 'score':
            earned = this.checkScoreAchievement(session, criteria);
            break;

          case 'streak':
            earned = await this.checkStreakAchievement(
              session.student_id,
              accountId,
              criteria
            );
            break;

          case 'time':
            earned = this.checkTimeAchievement(session, criteria);
            break;

          case 'accuracy':
            earned = this.checkAccuracyAchievement(session, criteria);
            break;
        }

        if (earned) {
          newAchievements.push({
            account_id: accountId,
            student_id: session.student_id,
            achievement_id: achievement.id,
            session_id: session.id,
            earned_at: new Date().toISOString(),
          });
        }
      }

      // Save new achievements
      if (newAchievements.length > 0) {
        const { error: insertError } = await this.supabase
          .from('student_achievements')
          .insert(newAchievements);

        if (insertError) throw insertError;

        console.log(`Awarded ${newAchievements.length} achievements to student ${session.student_id}`);
      }

      return newAchievements;
    } catch (error) {
      console.error('Error checking achievements:', error);
      return [];
    }
  }

  private async checkCompletionAchievement(
    studentId: string,
    accountId: string,
    criteria: AchievementCriteria
  ): Promise<boolean> {
    const { count } = await this.supabase
      .from('game_sessions')
      .select('*', { count: 'exact', head: true })
      .eq('student_id', studentId)
      .eq('account_id', accountId)
      .eq('is_completed', true);

    return (count || 0) >= (criteria.gamesCompleted || 1);
  }

  private checkScoreAchievement(
    session: GameSession,
    criteria: AchievementCriteria
  ): boolean {
    const percentage = (session.score / session.max_score) * 100;
    
    // Check game type if specified
    if (criteria.gameType && session.game.game_type !== criteria.gameType) {
      return false;
    }

    return percentage >= (criteria.minScore || 80);
  }

  private async checkStreakAchievement(
    studentId: string,
    accountId: string,
    criteria: AchievementCriteria
  ): Promise<boolean> {
    // Get recent sessions to check for consecutive days
    const { data: recentSessions } = await this.supabase
      .from('game_sessions')
      .select('completed_at')
      .eq('student_id', studentId)
      .eq('account_id', accountId)
      .eq('is_completed', true)
      .order('completed_at', { ascending: false })
      .limit(30); // Check last 30 sessions

    if (!recentSessions || recentSessions.length === 0) {
      return false;
    }

    // Group sessions by date
    const sessionsByDate = new Map<string, number>();
    recentSessions.forEach(session => {
      const date = new Date(session.completed_at).toDateString();
      sessionsByDate.set(date, (sessionsByDate.get(date) || 0) + 1);
    });

    // Check for consecutive days
    const dates = Array.from(sessionsByDate.keys()).sort();
    let consecutiveDays = 1;
    let maxConsecutive = 1;

    for (let i = 1; i < dates.length; i++) {
      const prevDate = new Date(dates[i - 1]);
      const currentDate = new Date(dates[i]);
      const diffTime = Math.abs(currentDate.getTime() - prevDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 1) {
        consecutiveDays++;
        maxConsecutive = Math.max(maxConsecutive, consecutiveDays);
      } else {
        consecutiveDays = 1;
      }
    }

    return maxConsecutive >= (criteria.consecutiveDays || 7);
  }

  private checkTimeAchievement(
    session: GameSession,
    criteria: AchievementCriteria
  ): boolean {
    return (
      session.duration_seconds <= (criteria.maxTime || 60) &&
      (criteria.gameCompleted ? session.score > 0 : true)
    );
  }

  private checkAccuracyAchievement(
    session: GameSession,
    criteria: AchievementCriteria
  ): boolean {
    // Check game type if specified
    if (criteria.gameType && session.game.game_type !== criteria.gameType) {
      return false;
    }

    return (session.accuracy_percentage || 0) >= (criteria.minAccuracy || 100);
  }

  async getStudentAchievements(studentId: string, accountId: string) {
    try {
      const { data: studentAchievements, error } = await this.supabase
        .from('student_achievements')
        .select(`
          id,
          earned_at,
          achievements(
            id,
            title,
            description,
            icon,
            badge_color,
            achievement_type,
            points_reward
          )
        `)
        .eq('student_id', studentId)
        .eq('account_id', accountId)
        .order('earned_at', { ascending: false });

      if (error) throw error;

      return studentAchievements || [];
    } catch (error) {
      console.error('Error getting student achievements:', error);
      return [];
    }
  }

  async getAchievementLeaderboard(accountId: string, limit: number = 10) {
    try {
      const { data: leaderboard, error } = await this.supabase
        .from('student_achievements')
        .select(`
          student_id,
          achievements(points_reward)
        `)
        .eq('account_id', accountId);

      if (error) throw error;

      // Calculate total points per student
      const studentPoints = new Map<string, number>();
      leaderboard?.forEach(achievement => {
        const studentId = achievement.student_id;
        const points = achievement.achievements?.points_reward || 0;
        studentPoints.set(studentId, (studentPoints.get(studentId) || 0) + points);
      });

      // Sort by points and return top students
      const sortedStudents = Array.from(studentPoints.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, limit)
        .map(([studentId, points], index) => ({
          rank: index + 1,
          student_id: studentId,
          total_points: points,
          student_name: `Student ${studentId.slice(0, 8)}`, // Simplified
        }));

      return sortedStudents;
    } catch (error) {
      console.error('Error getting achievement leaderboard:', error);
      return [];
    }
  }
}
