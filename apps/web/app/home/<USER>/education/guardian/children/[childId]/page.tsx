import { Suspense } from 'react';

import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { ChildDetails } from './_components/child-details';
import { ChildDetailsSkeleton } from './_components/child-details-skeleton';

interface Props {
  params: Promise<{
    account: string;
    childId: string;
  }>;
}

async function ChildDetailsPage({ params }: Props) {
  const { account: accountSlug, childId } = await params;
  const i18n = await createI18nServerInstance();
  const { user, account } = await loadTeamWorkspace(accountSlug);

  return (
    <div className="flex flex-col space-y-6 p-4 md:p-6">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title={i18n.t('education:guardian.child.details')}
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education`}>
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education/guardian`}
            >
              {i18n.t('education:breadcrumbs.guardian')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>
              {i18n.t('education:guardian.child.details')}
            </AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-7xl">
        <Suspense fallback={<ChildDetailsSkeleton />}>
          <ChildDetails account={accountSlug} childId={childId} />
        </Suspense>
      </div>
    </div>
  );
}

export default withI18n(ChildDetailsPage);

export const metadata = {
  title: 'Child Details',
  description: 'Chi tiết thông tin con em',
};
