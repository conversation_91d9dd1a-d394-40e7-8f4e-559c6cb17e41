'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Textarea } from '@kit/ui/textarea';

interface Fee {
  id: string;
  learner_id: string;
  fee_category: string;
  amount: number;
  due_date: string;
  description: string;
  status: string;
  payment_date: string;
  payment_method: string;
  learners?: {
    id: string;
    full_name: string;
    learner_code: string;
  };
}

interface Props {
  account: string;
  feeId: string;
}

function EditFeeForm({ account, feeId }: Props) {
  const { t } = useTranslation('education');
  const [fee, setFee] = useState<Fee | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();
  const router = useRouter();

  // Form state
  const [formData, setFormData] = useState({
    fee_category: '',
    amount: 0,
    due_date: '',
    description: '',
    status: 'pending',
    payment_date: '',
    payment_method: '',
  });

  useEffect(() => {
    loadFee();
  }, [feeId]);

  const loadFee = async () => {
    try {
      setLoading(true);
      setError(null);

      const accountId = workspace.account.id;

      const { data: fee, error: feeError } = await supabase
        .from('fees')
        .select(
          `
          *,
          learners (
            id,
            full_name,
            learner_code
          )
        `,
        )
        .eq('id', feeId)
        .eq('account_id', accountId)
        .single();

      if (feeError) throw feeError;

      setFee(fee);

      // Populate form data
      setFormData({
        fee_category: fee.fee_category || '',
        amount: fee.amount || 0,
        due_date: fee.due_date || '',
        description: fee.description || '',
        status: fee.status || 'pending',
        payment_date: fee.payment_date || '',
        payment_method: fee.payment_method || '',
      });
    } catch (err: any) {
      console.error('Error loading fee:', err);
      setError(err.message || 'Failed to load fee');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError(null);

      const accountId = workspace.account.id;

      const updateData: any = {
        fee_type: formData.fee_category, // Map fee_category to fee_type
        fee_category: formData.fee_category,
        amount: formData.amount,
        due_date: formData.due_date,
        description: formData.description,
        status: formData.status,
        updated_at: new Date().toISOString(),
      };

      // Only include payment info if status is paid
      if (formData.status === 'paid') {
        updateData.payment_date =
          formData.payment_date || new Date().toISOString();
        updateData.payment_method = formData.payment_method;
      } else {
        updateData.payment_date = null;
        updateData.payment_method = null;
      }

      const { error: updateError } = await supabase
        .from('fees')
        .update(updateData)
        .eq('id', feeId)
        .eq('account_id', accountId);

      if (updateError) throw updateError;

      router.push(`/home/<USER>/education/fees/${feeId}`);
    } catch (err: any) {
      console.error('Error updating fee:', err);
      setError(err.message || 'Failed to update fee');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-b-2"></div>
          <p className="text-muted-foreground mt-2 text-sm">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (error && !fee) {
    return (
      <div className="py-8 text-center">
        <p className="text-red-600">{error}</p>
        <Button onClick={loadFee} className="mt-4">
          Thử lại
        </Button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Learner Info */}
      {fee?.learners && (
        <Card>
          <CardHeader>
            <CardTitle>Thông tin học viên</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-muted rounded-lg p-4">
              <h4 className="font-medium">{fee.learners.full_name}</h4>
              <p className="text-muted-foreground text-sm">
                Mã học viên: {fee.learners.learner_code}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Thông tin học phí</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="fee_category">Loại phí *</Label>
              <Select
                value={formData.fee_category}
                onValueChange={(value) =>
                  handleInputChange('fee_category', value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn loại phí" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="tuition">Học phí</SelectItem>
                  <SelectItem value="meal">Tiền ăn</SelectItem>
                  <SelectItem value="transport">Tiền xe</SelectItem>
                  <SelectItem value="activity">Hoạt động</SelectItem>
                  <SelectItem value="material">Học liệu</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="amount">Số tiền (VNĐ) *</Label>
              <Input
                id="amount"
                type="number"
                value={formData.amount}
                onChange={(e) =>
                  handleInputChange('amount', parseInt(e.target.value) || 0)
                }
                min="0"
                step="1000"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="description">Mô tả</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Mô tả chi tiết về khoản phí"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="due_date">Hạn thanh toán *</Label>
              <Input
                id="due_date"
                type="date"
                value={formData.due_date}
                onChange={(e) => handleInputChange('due_date', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="status">Trạng thái *</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Chờ thanh toán</SelectItem>
                  <SelectItem value="paid">Đã thanh toán</SelectItem>
                  <SelectItem value="overdue">Quá hạn</SelectItem>
                  <SelectItem value="cancelled">Đã hủy</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Info - Only show if status is paid */}
      {formData.status === 'paid' && (
        <Card>
          <CardHeader>
            <CardTitle>Thông tin thanh toán</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="payment_date">Ngày thanh toán</Label>
                <Input
                  id="payment_date"
                  type="date"
                  value={formData.payment_date}
                  onChange={(e) =>
                    handleInputChange('payment_date', e.target.value)
                  }
                />
              </div>
              <div>
                <Label htmlFor="payment_method">Phương thức thanh toán</Label>
                <Select
                  value={formData.payment_method}
                  onValueChange={(value) =>
                    handleInputChange('payment_method', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn phương thức" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cash">Tiền mặt</SelectItem>
                    <SelectItem value="bank_transfer">Chuyển khoản</SelectItem>
                    <SelectItem value="zalopay">ZaloPay</SelectItem>
                    <SelectItem value="momo">MoMo</SelectItem>
                    <SelectItem value="credit_card">Thẻ tín dụng</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {error && (
        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() =>
            router.push(`/home/<USER>/education/fees/${feeId}`)
          }
        >
          Hủy
        </Button>
        <Button type="submit" disabled={saving}>
          {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
        </Button>
      </div>
    </form>
  );
}

export default EditFeeForm;
