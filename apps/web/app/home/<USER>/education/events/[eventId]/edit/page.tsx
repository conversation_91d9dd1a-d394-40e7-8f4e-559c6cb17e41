import { redirect } from 'next/navigation';

import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';

import { TeamAccountLayoutPageHeader } from '../../../../_components/team-account-layout-page-header';
import EditEventForm from './_components/edit-event-form';

interface Props {
  params: Promise<{
    account: string;
    eventId: string;
  }>;
}

export const metadata = {
  title: 'Chỉnh Sửa Sự Kiện',
};

async function EditEventPage({ params }: Props) {
  const i18n = await createI18nServerInstance();
  const resolvedParams = await params;
  const { user, account } = await loadTeamWorkspace(resolvedParams.account);

  if (!user) {
    redirect('/auth/sign-in');
  }

  return (
    <div className="flex flex-1 flex-col space-y-4 pb-36">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title={i18n.t('education:events.edit.title')}
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education`}
            >
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education/events`}
            >
              {i18n.t('education:breadcrumbs.events')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education/events/${resolvedParams.eventId}`}
            >
              {i18n.t('education:events.details.title')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>
              {i18n.t('education:events.edit.title')}
            </AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-4xl">
        <EditEventForm
          account={resolvedParams.account}
          eventId={resolvedParams.eventId}
        />
      </div>
    </div>
  );
}

export default EditEventPage;
