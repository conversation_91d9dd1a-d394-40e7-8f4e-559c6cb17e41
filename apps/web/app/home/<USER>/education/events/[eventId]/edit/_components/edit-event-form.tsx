'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Checkbox } from '@kit/ui/checkbox';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Textarea } from '@kit/ui/textarea';

interface Event {
  id: string;
  title: string;
  description: string;
  event_type: string;
  start_datetime: string;
  end_datetime: string;
  location: string;
  registration_required: boolean;
  max_participants: number;
}

interface Props {
  account: string;
  eventId: string;
}

function EditEventForm({ account, eventId }: Props) {
  const { t } = useTranslation('education');
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();
  const router = useRouter();

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    event_type: '',
    start_datetime: '',
    end_datetime: '',
    location: '',
    registration_required: false,
    max_participants: 0,
  });

  useEffect(() => {
    loadEvent();
  }, [eventId]);

  const loadEvent = async () => {
    try {
      setLoading(true);
      setError(null);

      const accountId = workspace.account.id;

      const { data: event, error: eventError } = await supabase
        .from('events')
        .select('*')
        .eq('id', eventId)
        .eq('account_id', accountId)
        .single();

      if (eventError) throw eventError;

      setEvent(event);

      // Populate form data
      setFormData({
        title: event.title || '',
        description: event.description || '',
        event_type: event.event_type || '',
        start_datetime: event.start_datetime
          ? new Date(event.start_datetime).toISOString().slice(0, 16)
          : '',
        end_datetime: event.end_datetime
          ? new Date(event.end_datetime).toISOString().slice(0, 16)
          : '',
        location: event.location || '',
        registration_required: event.registration_required || false,
        max_participants: event.max_participants || 0,
      });
    } catch (err: any) {
      console.error('Error loading event:', err);
      setError(err.message || 'Failed to load event');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError(null);

      const accountId = workspace.account.id;

      const { error: updateError } = await supabase
        .from('events')
        .update({
          title: formData.title,
          description: formData.description,
          event_type: formData.event_type,
          start_datetime: formData.start_datetime
            ? new Date(formData.start_datetime).toISOString()
            : null,
          end_datetime: formData.end_datetime
            ? new Date(formData.end_datetime).toISOString()
            : null,
          location: formData.location,
          registration_required: formData.registration_required,
          max_participants: formData.max_participants,
        })
        .eq('id', eventId)
        .eq('account_id', accountId);

      if (updateError) throw updateError;

      router.push(`/home/<USER>/education/events/${eventId}`);
    } catch (err: any) {
      console.error('Error updating event:', err);
      setError(err.message || 'Failed to update event');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-b-2"></div>
          <p className="text-muted-foreground mt-2 text-sm">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (error && !event) {
    return (
      <div className="py-8 text-center">
        <p className="text-red-600">{error}</p>
        <Button onClick={loadEvent} className="mt-4">
          Thử lại
        </Button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Thông tin sự kiện</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="title">Tiêu đề sự kiện *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="Nhập tiêu đề sự kiện"
              required
            />
          </div>

          <div>
            <Label htmlFor="description">Mô tả</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Mô tả chi tiết về sự kiện"
              rows={4}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="event_type">Loại sự kiện *</Label>
              <Select
                value={formData.event_type}
                onValueChange={(value) =>
                  handleInputChange('event_type', value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn loại sự kiện" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="meeting">Họp phụ huynh</SelectItem>
                  <SelectItem value="performance">Biểu diễn</SelectItem>
                  <SelectItem value="field_trip">Dã ngoại</SelectItem>
                  <SelectItem value="workshop">Hội thảo</SelectItem>
                  <SelectItem value="celebration">Lễ kỷ niệm</SelectItem>
                  <SelectItem value="other">Khác</SelectItem>
                </SelectContent>
              </Select>
            </div>

          </div>

          <div>
            <Label htmlFor="location">Địa điểm</Label>
            <Input
              id="location"
              value={formData.location}
              onChange={(e) => handleInputChange('location', e.target.value)}
              placeholder="Địa điểm tổ chức sự kiện"
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Thời gian</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="start_datetime">Thời gian bắt đầu *</Label>
              <Input
                id="start_datetime"
                type="datetime-local"
                value={formData.start_datetime}
                onChange={(e) =>
                  handleInputChange('start_datetime', e.target.value)
                }
                required
              />
            </div>
            <div>
              <Label htmlFor="end_datetime">Thời gian kết thúc</Label>
              <Input
                id="end_datetime"
                type="datetime-local"
                value={formData.end_datetime}
                onChange={(e) =>
                  handleInputChange('end_datetime', e.target.value)
                }
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Cài đặt tham gia</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="registration_required"
              checked={formData.registration_required}
              onCheckedChange={(checked) =>
                handleInputChange('registration_required', checked)
              }
            />
            <Label htmlFor="registration_required">Yêu cầu đăng ký trước</Label>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="max_participants">Số lượng tối đa</Label>
              <Input
                id="max_participants"
                type="number"
                value={formData.max_participants}
                onChange={(e) =>
                  handleInputChange(
                    'max_participants',
                    parseInt(e.target.value) || 0,
                  )
                }
                min="0"
                placeholder="0 = không giới hạn"
              />
            </div>

          </div>
        </CardContent>
      </Card>

      {error && (
        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() =>
            router.push(`/home/<USER>/education/events/${eventId}`)
          }
        >
          Hủy
        </Button>
        <Button type="submit" disabled={saving}>
          {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
        </Button>
      </div>
    </form>
  );
}

export default EditEventForm;
