import { Suspense } from 'react';

import { TeamAccountLayoutPageHeader } from '../../../_components/team-account-layout-page-header';
import { withI18n } from '~/lib/i18n/with-i18n';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

import { NewMessageForm } from './_components/new-message-form';

interface Props {
  params: {
    account: string;
  };
}

async function NewMessagePage({ params }: Props) {
  const i18n = await createI18nServerInstance();
  const resolvedParams = await params;
  const { user, account } = await loadTeamWorkspace(resolvedParams.account);

  return (
    <div className="flex flex-col space-y-6 p-4 md:p-6">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title={i18n.t('education:messages.newMessage')}
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education`}>
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education/messages`}>
              {i18n.t('education:breadcrumbs.messages')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>
              {i18n.t('education:messages.newMessage')}
            </AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-4xl">
        <Suspense fallback={<div>Loading...</div>}>
          <NewMessageForm account={resolvedParams.account} />
        </Suspense>
      </div>
    </div>
  );
}

export default withI18n(NewMessagePage);

export const metadata = {
  title: 'New Message',
  description: 'Send a new message to parents and staff',
};
