'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Clock, Save, Send } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Checkbox } from '@kit/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { toast } from '@kit/ui/sonner';
import { Textarea } from '@kit/ui/textarea';

interface Props {
  account: string;
}

const messageSchema = z.object({
  title: z.string().min(1, 'Message title is required'),
  content: z.string().min(1, 'Message content is required'),
  message_type: z.enum([
    'announcement',
    'invitation',
    'fee_reminder',
    'emergency',
  ]),
  recipient_type: z.enum([
    'all_parents',
    'class_parents',
    'specific_parents',
    'instructors',
  ]),
  priority: z.enum(['low', 'normal', 'high', 'urgent']),
  schedule_send: z.boolean(),
  scheduled_at: z.string().optional(),
  send_sms: z.boolean(),
  send_email: z.boolean(),
  send_push: z.boolean(),
});

type MessageFormData = z.infer<typeof messageSchema>;

export function NewMessageForm({ account }: Props) {
  const { t } = useTranslation('education');
  const router = useRouter();
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();

  const [loading, setLoading] = useState(false);
  const [recipientCount, setRecipientCount] = useState(0);

  const currentAccount = accounts?.find((acc) => acc.slug === account);

  const form = useForm<MessageFormData>({
    resolver: zodResolver(messageSchema),
    defaultValues: {
      title: '',
      content: '',
      message_type: 'announcement',
      recipient_type: 'all_parents',
      priority: 'normal',
      schedule_send: false,
      scheduled_at: '',
      send_sms: true,
      send_email: true,
      send_push: true,
    },
  });

  const watchRecipientType = form.watch('recipient_type');
  const watchScheduleSend = form.watch('schedule_send');

  useEffect(() => {
    // Calculate recipient count based on type
    const counts = {
      all_parents: 120,
      class_parents: 25,
      specific_parents: 15,
      instructors: 8,
    };
    setRecipientCount(counts[watchRecipientType] || 0);
  }, [watchRecipientType]);

  const onSubmit = async (data: MessageFormData, action: 'save' | 'send') => {
    if (!currentAccount?.id) {
      toast.error('Account not found');
      return;
    }

    try {
      setLoading(true);

      // Use account directly
      const accountId = currentAccount.id;

      // Determine status based on action
      let status = 'draft';
      let sentAt = null;
      let scheduledAt = null;

      if (action === 'send') {
        if (data.schedule_send && data.scheduled_at) {
          status = 'scheduled';
          scheduledAt = data.scheduled_at;
        } else {
          status = 'sent';
          sentAt = new Date().toISOString();
        }
      }

      // Create message in database
      const { error: messageError } = await supabase
        .from('messages')
        .insert({
          title: data.title,
          content: data.content,
          message_type: data.message_type,
          recipient_type: data.recipient_type,
          priority: data.priority,
          status: status,
          account_id: accountId,
          sent_at: sentAt,
          scheduled_at: scheduledAt,
          delivery_channels: {
            sms: data.send_sms,
            email: data.send_email,
            push: data.send_push,
          },
        });

      if (messageError) {
        throw new Error(messageError.message);
      }
      const actionText =
        action === 'save'
          ? t('messages.savedSuccess', 'Message saved as draft')
          : status === 'scheduled'
            ? t('messages.scheduledSuccess', 'Message scheduled successfully')
            : t('messages.sentSuccess', 'Message sent successfully');

      toast.success(actionText);

      // Navigate back and refresh the page to show new message
      router.push(`/home/<USER>/education/messages`);
      router.refresh();
    } catch (error: any) {
      console.error('Error with message:', error);
      toast.error(error.message || 'Failed to process message');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="relative space-y-6">
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>{t('common.back')}</span>
        </Button>
      </div>

      <Form {...form}>
        <form className="space-y-6">
          {/* Message Content */}
          <Card>
            <CardHeader>
              <CardTitle>
                {t('messages.form.messageContent', 'Message Content')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('messages.form.title', 'Title')}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Thông báo nghỉ lễ 30/4 - 1/5"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="message_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t('messages.form.messageType', 'Message Type')}
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select message type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="announcement">
                            {t('messages.messageType.announcement')}
                          </SelectItem>
                          <SelectItem value="invitation">
                            {t('messages.messageType.invitation')}
                          </SelectItem>
                          <SelectItem value="fee_reminder">
                            {t('messages.messageType.fee_reminder')}
                          </SelectItem>
                          <SelectItem value="emergency">
                            {t('messages.messageType.emergency')}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t('messages.form.priority', 'Priority')}
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="low">
                            {t('messages.priority.low', 'Low')}
                          </SelectItem>
                          <SelectItem value="normal">
                            {t('messages.priority.normal', 'Normal')}
                          </SelectItem>
                          <SelectItem value="high">
                            {t('messages.priority.high', 'High')}
                          </SelectItem>
                          <SelectItem value="urgent">
                            {t('messages.priority.urgent', 'Urgent')}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t('messages.form.content', 'Content')}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Nội dung tin nhắn..."
                        className="min-h-32"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Recipients */}
          <Card>
            <CardHeader>
              <CardTitle>
                {t('messages.form.recipients', 'Recipients')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="recipient_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t('messages.form.recipientType', 'Recipient Type')}
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select recipients" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="all_parents">
                          {t('messages.recipientType.all_parents')}
                        </SelectItem>
                        <SelectItem value="class_parents">
                          {t('messages.recipientType.class_parents')}
                        </SelectItem>
                        <SelectItem value="specific_parents">
                          {t('messages.recipientType.specific_parents')}
                        </SelectItem>
                        <SelectItem value="instructors">
                          {t(
                            'messages.recipientType.instructors',
                            'Instructors',
                          )}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="rounded-lg bg-blue-50 p-3">
                <p className="text-sm text-blue-700">
                  {t(
                    'messages.form.recipientCount',
                    'This message will be sent to {{count}} recipients',
                    { count: recipientCount },
                  )}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Delivery Options */}
          <Card>
            <CardHeader>
              <CardTitle>
                {t('messages.form.deliveryOptions', 'Delivery Options')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <FormField
                  control={form.control}
                  name="send_sms"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-y-0 space-x-3 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          {t('messages.form.sendSMS', 'Send SMS')}
                        </FormLabel>
                        <p className="text-muted-foreground text-sm">
                          {t(
                            'messages.form.sendSMSDesc',
                            'Send via text message',
                          )}
                        </p>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="send_email"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-y-0 space-x-3 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          {t('messages.form.sendEmail', 'Send Email')}
                        </FormLabel>
                        <p className="text-muted-foreground text-sm">
                          {t('messages.form.sendEmailDesc', 'Send via email')}
                        </p>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="send_push"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-y-0 space-x-3 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          {t('messages.form.sendPush', 'Push Notification')}
                        </FormLabel>
                        <p className="text-muted-foreground text-sm">
                          {t(
                            'messages.form.sendPushDesc',
                            'Send push notification',
                          )}
                        </p>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Scheduling */}
          <Card>
            <CardHeader>
              <CardTitle>
                {t('messages.form.scheduling', 'Scheduling')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="schedule_send"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        {t('messages.form.scheduleSend', 'Schedule Send')}
                      </FormLabel>
                      <div className="text-muted-foreground text-sm">
                        {t(
                          'messages.form.scheduleSendDesc',
                          'Send this message at a specific time',
                        )}
                      </div>
                    </div>
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {watchScheduleSend && (
                <FormField
                  control={form.control}
                  name="scheduled_at"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t(
                          'messages.form.scheduledAt',
                          'Scheduled Date & Time',
                        )}
                      </FormLabel>
                      <FormControl>
                        <Input type="datetime-local" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </CardContent>
          </Card>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              {t('common.cancel')}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={form.handleSubmit((data) => onSubmit(data, 'save'))}
              disabled={loading}
            >
              <Save className="mr-2 h-4 w-4" />
              {t('messages.form.saveDraft', 'Save Draft')}
            </Button>
            <Button
              type="button"
              onClick={form.handleSubmit((data) => onSubmit(data, 'send'))}
              disabled={loading}
            >
              {watchScheduleSend ? (
                <>
                  <Clock className="mr-2 h-4 w-4" />
                  {t('messages.form.schedule', 'Schedule')}
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  {t('messages.form.sendNow', 'Send Now')}
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
