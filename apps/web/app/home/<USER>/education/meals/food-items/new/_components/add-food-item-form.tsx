'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { Plus, X } from 'lucide-react';
import { toast } from 'sonner';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Textarea } from '@kit/ui/textarea';

interface MealCategory {
  id: string;
  name: string;
}

interface Props {
  account: string;
}

function AddFoodItemForm({ account }: Props) {
  const [loading, setLoading] = useState(false);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<MealCategory[]>([]);
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();
  const router = useRouter();

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    category_id: '',
    description: '',
    allergens: [] as string[],
    nutritional_info: {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0,
      fiber: 0,
      sugar: 0,
    },
    ingredients: [] as string[],
    preparation_time_minutes: 0,
    serving_size: '',
    cost_per_serving: 0,
    supplier: '',
    storage_requirements: '',
    shelf_life_days: 0,
    status: 'active',
  });

  const [newAllergen, setNewAllergen] = useState('');
  const [newIngredient, setNewIngredient] = useState('');

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoadingCategories(true);

      // Use static categories since meal_categories table doesn't exist
      const staticCategories = [
        { id: '1', name: 'Mon chinh' },
        { id: '2', name: 'Mon phu' },
        { id: '3', name: 'Do uong' },
        { id: '4', name: 'Trang mieng' },
        { id: '5', name: 'Snack' },
        { id: '6', name: 'Trai cay' },
      ];

      setCategories(staticCategories);
    } catch (err: any) {
      console.error('Error loading categories:', err);
      setCategories([]);
    } finally {
      setLoadingCategories(false);
    }
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      const accountId = workspace.account.id;
      const userId = workspace.user?.id;

      if (!accountId || !userId) {
        throw new Error('Account or user not found');
      }

      // Since we don't have food_items table, create a meal plan with this food item
      const foodItem = {
        id: Date.now().toString(), // Simple ID generation
        name: formData.name,
        category: formData.category_id || 'other',
        description: formData.description,
        allergens: formData.allergens,
        nutritional_info: formData.nutritional_info,
        ingredients: formData.ingredients,
        preparation_time_minutes: formData.preparation_time_minutes,
        serving_size: formData.serving_size,
        cost_per_serving: formData.cost_per_serving,
        supplier: formData.supplier,
        storage_requirements: formData.storage_requirements,
        shelf_life_days: formData.shelf_life_days,
        status: formData.status,
      };

      const today = new Date().toISOString().split('T')[0];

      // First, check if meal plan already exists for today
      const { data: existingPlan, error: checkError } = await supabase
        .from('meal_plans')
        .select('id, menu_items')
        .eq('account_id', accountId)
        .eq('date', today)
        .eq('meal_type', 'lunch')
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }

      let insertError;

      if (existingPlan) {
        // Update existing meal plan by adding the food item
        const updatedMenuItems = [...(existingPlan.menu_items || []), foodItem];

        const { error } = await supabase
          .from('meal_plans')
          .update({
            menu_items: updatedMenuItems,
            special_notes: `Updated with food item: ${formData.name}`,
          })
          .eq('id', existingPlan.id);

        insertError = error;
      } else {
        // Create new meal plan
        const { error } = await supabase.from('meal_plans').insert({
          account_id: accountId,
          date: today,
          meal_type: 'lunch',
          menu_items: [foodItem],
          special_notes: `Food item: ${formData.name} - ${formData.description}`,
          dietary_accommodations: formData.allergens || [],
          cost_per_serving: formData.cost_per_serving || null,
          created_by: userId,
        });

        insertError = error;
      }

      if (insertError) throw insertError;

      if (existingPlan) {
        toast.success('Mon an da duoc them vao menu hom nay');
      } else {
        toast.success('Mon an da duoc them thanh cong');
      }
      router.push(`/home/<USER>/education/meals`);
    } catch (err: any) {
      console.error('Error creating food item:', err);
      setError(err.message || 'Co loi xay ra khi them mon an');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleNutritionChange = (field: string, value: number) => {
    setFormData((prev) => ({
      ...prev,
      nutritional_info: {
        ...prev.nutritional_info,
        [field]: value,
      },
    }));
  };

  const addAllergen = () => {
    if (newAllergen.trim()) {
      setFormData((prev) => ({
        ...prev,
        allergens: [...prev.allergens, newAllergen.trim()],
      }));
      setNewAllergen('');
    }
  };

  const removeAllergen = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      allergens: prev.allergens.filter((_, i) => i !== index),
    }));
  };

  const addIngredient = () => {
    if (newIngredient.trim()) {
      setFormData((prev) => ({
        ...prev,
        ingredients: [...prev.ingredients, newIngredient.trim()],
      }));
      setNewIngredient('');
    }
  };

  const removeIngredient = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      ingredients: prev.ingredients.filter((_, i) => i !== index),
    }));
  };

  if (loadingCategories) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center py-8">
          <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Thông tin cơ bản</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">Tên món ăn *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Nhập tên món ăn"
                required
              />
            </div>

            <div>
              <Label htmlFor="category_id">Danh mục</Label>
              <Select
                value={formData.category_id}
                onValueChange={(value) =>
                  handleInputChange('category_id', value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn danh mục" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="description">Mô tả</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  handleInputChange('description', e.target.value)
                }
                placeholder="Mô tả chi tiết về món ăn"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="serving_size">Khẩu phần</Label>
                <Input
                  id="serving_size"
                  value={formData.serving_size}
                  onChange={(e) =>
                    handleInputChange('serving_size', e.target.value)
                  }
                  placeholder="VD: 1 bát, 100g"
                />
              </div>
              <div>
                <Label htmlFor="preparation_time_minutes">
                  Thời gian chuẩn bị (phút)
                </Label>
                <Input
                  id="preparation_time_minutes"
                  type="number"
                  value={formData.preparation_time_minutes}
                  onChange={(e) =>
                    handleInputChange(
                      'preparation_time_minutes',
                      parseInt(e.target.value) || 0,
                    )
                  }
                  min="0"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="cost_per_serving">
                  Giá thành/khẩu phần (VNĐ)
                </Label>
                <Input
                  id="cost_per_serving"
                  type="number"
                  value={formData.cost_per_serving}
                  onChange={(e) =>
                    handleInputChange(
                      'cost_per_serving',
                      parseFloat(e.target.value) || 0,
                    )
                  }
                  min="0"
                  step="0.01"
                />
              </div>
              <div>
                <Label htmlFor="status">Trạng thái</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleInputChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Đang phục vụ</SelectItem>
                    <SelectItem value="inactive">Ngừng phục vụ</SelectItem>
                    <SelectItem value="seasonal">Theo mùa</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Thông tin dinh dưỡng (trên 100g)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="calories">Calories (kcal)</Label>
                <Input
                  id="calories"
                  type="number"
                  value={formData.nutritional_info.calories}
                  onChange={(e) =>
                    handleNutritionChange(
                      'calories',
                      parseFloat(e.target.value) || 0,
                    )
                  }
                  min="0"
                  step="0.1"
                />
              </div>
              <div>
                <Label htmlFor="protein">Protein (g)</Label>
                <Input
                  id="protein"
                  type="number"
                  value={formData.nutritional_info.protein}
                  onChange={(e) =>
                    handleNutritionChange(
                      'protein',
                      parseFloat(e.target.value) || 0,
                    )
                  }
                  min="0"
                  step="0.1"
                />
              </div>
              <div>
                <Label htmlFor="carbs">Carbs (g)</Label>
                <Input
                  id="carbs"
                  type="number"
                  value={formData.nutritional_info.carbs}
                  onChange={(e) =>
                    handleNutritionChange(
                      'carbs',
                      parseFloat(e.target.value) || 0,
                    )
                  }
                  min="0"
                  step="0.1"
                />
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="fat">Chất béo (g)</Label>
                <Input
                  id="fat"
                  type="number"
                  value={formData.nutritional_info.fat}
                  onChange={(e) =>
                    handleNutritionChange(
                      'fat',
                      parseFloat(e.target.value) || 0,
                    )
                  }
                  min="0"
                  step="0.1"
                />
              </div>
              <div>
                <Label htmlFor="fiber">Chất xơ (g)</Label>
                <Input
                  id="fiber"
                  type="number"
                  value={formData.nutritional_info.fiber}
                  onChange={(e) =>
                    handleNutritionChange(
                      'fiber',
                      parseFloat(e.target.value) || 0,
                    )
                  }
                  min="0"
                  step="0.1"
                />
              </div>
              <div>
                <Label htmlFor="sugar">Đường (g)</Label>
                <Input
                  id="sugar"
                  type="number"
                  value={formData.nutritional_info.sugar}
                  onChange={(e) =>
                    handleNutritionChange(
                      'sugar',
                      parseFloat(e.target.value) || 0,
                    )
                  }
                  min="0"
                  step="0.1"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Chất gây dị ứng</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-2">
              <Input
                value={newAllergen}
                onChange={(e) => setNewAllergen(e.target.value)}
                placeholder="Thêm chất gây dị ứng (VD: sữa, trứng, đậu phộng)"
                onKeyPress={(e) =>
                  e.key === 'Enter' && (e.preventDefault(), addAllergen())
                }
              />
              <Button type="button" onClick={addAllergen} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {formData.allergens.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {formData.allergens.map((allergen, index) => (
                  <div
                    key={index}
                    className="flex items-center rounded-md bg-red-100 px-2 py-1 text-sm text-red-800"
                  >
                    <span>{allergen}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeAllergen(index)}
                      className="ml-1 h-4 w-4 p-0"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Nguyên liệu</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-2">
              <Input
                value={newIngredient}
                onChange={(e) => setNewIngredient(e.target.value)}
                placeholder="Thêm nguyên liệu"
                onKeyPress={(e) =>
                  e.key === 'Enter' && (e.preventDefault(), addIngredient())
                }
              />
              <Button type="button" onClick={addIngredient} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {formData.ingredients.length > 0 && (
              <div className="space-y-2">
                {formData.ingredients.map((ingredient, index) => (
                  <div
                    key={index}
                    className="bg-muted flex items-center justify-between rounded-lg p-2"
                  >
                    <span className="text-sm">{ingredient}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeIngredient(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Thông tin bổ sung</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="supplier">Nhà cung cấp</Label>
              <Input
                id="supplier"
                value={formData.supplier}
                onChange={(e) => handleInputChange('supplier', e.target.value)}
                placeholder="Tên nhà cung cấp nguyên liệu"
              />
            </div>

            <div>
              <Label htmlFor="storage_requirements">Yêu cầu bảo quản</Label>
              <Textarea
                id="storage_requirements"
                value={formData.storage_requirements}
                onChange={(e) =>
                  handleInputChange('storage_requirements', e.target.value)
                }
                placeholder="Cách bảo quản món ăn"
                rows={2}
              />
            </div>

            <div>
              <Label htmlFor="shelf_life_days">Thời hạn sử dụng (ngày)</Label>
              <Input
                id="shelf_life_days"
                type="number"
                value={formData.shelf_life_days}
                onChange={(e) =>
                  handleInputChange(
                    'shelf_life_days',
                    parseInt(e.target.value) || 0,
                  )
                }
                min="0"
              />
            </div>
          </CardContent>
        </Card>

        {error && (
          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              router.push(`/home/<USER>/education/meals/food-items`)
            }
          >
            Hủy
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? 'Đang thêm...' : 'Thêm món ăn'}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default AddFoodItemForm;
