'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';

import {
  Apple,
  Calendar,
  ChefHat,
  ClipboardList,
  Package,
  Plus,
  Users,
  UtensilsCrossed,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

interface Props {
  account: string;
}

function MealManagementDashboard({ account }: Props) {
  const { t } = useTranslation('education');
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalMenus: 0,
    foodItems: 0,
    studentsServed: 0,
    inventoryItems: 0,
  });
  const [todayMenu, setTodayMenu] = useState<any[]>([]);
  const [inventoryAlerts, setInventoryAlerts] = useState<any[]>([]);
  const [allMenus, setAllMenus] = useState<any[]>([]);
  const [allFoodItems, setAllFoodItems] = useState<any[]>([]);
  const [allInventoryItems, setAllInventoryItems] = useState<any[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const accountId = workspace.account.id;

      // Load meal plans count (using existing table)
      const { data: menusData, error: menusError } = await supabase
        .from('meal_plans')
        .select('id, meal_type, menu_items')
        .eq('account_id', accountId);

      if (menusError) throw menusError;

      // Count food items from meal_plans (exclude inventory)
      const foodItemsCount = menusData
        ?.filter(plan => plan.meal_type !== 'inventory')
        ?.reduce((total, plan) => {
          return total + (plan.menu_items?.length || 0);
        }, 0) || 0;

      // Count inventory items from meal_plans (meal_type = 'inventory')
      const inventoryCount = menusData
        ?.filter(plan => plan.meal_type === 'inventory')
        ?.reduce((total, plan) => {
          return total + (plan.menu_items?.length || 0);
        }, 0) || 0;

      // Load learners count (students served)
      const { data: learnersData, error: learnersError } = await supabase
        .from('learners')
        .select('id')
        .eq('account_id', accountId);

      if (learnersError) throw learnersError;

      // Load today's meal plans (using existing table)
      const today = new Date().toISOString().split('T')[0];
      const { data: todayMenuData, error: todayMenuError } = await supabase
        .from('meal_plans')
        .select('id, meal_type, menu_items, date')
        .eq('account_id', accountId)
        .eq('date', today);

      if (todayMenuError) throw todayMenuError;

      // No inventory alerts since table doesn't exist
      const alertsData = [];

      // Update stats
      setStats({
        totalMenus: menusData?.filter(plan => plan.meal_type !== 'inventory')?.length || 0,
        foodItems: foodItemsCount,
        studentsServed: learnersData?.length || 0,
        inventoryItems: inventoryCount,
      });

      // Format today's menu from meal_plans
      const formattedMenu = todayMenuData?.map((item: any) => ({
        id: item.id,
        mealType: item.meal_type,
        items: item.menu_items?.map((menuItem: any) => menuItem.name) || [],
        time: getMealTime(item.meal_type),
      })) || [];

      setTodayMenu(formattedMenu);

      // Format all menus (exclude inventory)
      const allMenusFormatted = menusData
        ?.filter(plan => plan.meal_type !== 'inventory')
        ?.map((plan: any) => ({
          id: plan.id,
          date: plan.date,
          mealType: plan.meal_type,
          items: plan.menu_items?.map((item: any) => item.name) || [],
        })) || [];
      setAllMenus(allMenusFormatted);

      // Format all food items from menus
      const allFoodItemsFormatted = menusData
        ?.filter(plan => plan.meal_type !== 'inventory')
        ?.flatMap((plan: any) =>
          plan.menu_items?.map((item: any) => ({
            ...item,
            planId: plan.id,
            date: plan.date,
            mealType: plan.meal_type,
          })) || []
        ) || [];
      setAllFoodItems(allFoodItemsFormatted);

      // Format inventory items
      const allInventoryFormatted = menusData
        ?.filter(plan => plan.meal_type === 'inventory')
        ?.flatMap((plan: any) =>
          plan.menu_items?.map((item: any) => ({
            ...item,
            planId: plan.id,
            dateAdded: plan.date,
          })) || []
        ) || [];
      setAllInventoryItems(allInventoryFormatted);

      // Set empty inventory alerts since table doesn't exist
      setInventoryAlerts([]);
    } catch (error) {
      console.error('Error loading meal dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getMealTime = (mealType: string) => {
    switch (mealType) {
      case 'breakfast':
        return '08:00';
      case 'morning_snack':
        return '10:00';
      case 'lunch':
        return '11:30';
      case 'afternoon_snack':
        return '15:00';
      case 'dinner':
        return '17:30';
      default:
        return '12:00';
    }
  };

  const getMealTypeText = (type: string) => {
    switch (type) {
      case 'breakfast':
        return 'Bữa sáng';
      case 'lunch':
        return 'Bữa trưa';
      case 'snack':
        return 'Bữa phụ';
      case 'dinner':
        return 'Bữa tối';
      default:
        return type;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto max-w-7xl space-y-6 px-4 py-6">
        <div className="flex items-center justify-center py-12">
          <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-7xl space-y-6 px-4 py-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">{t('meals.title')}</h1>
          <p className="text-muted-foreground">{t('meals.description')}</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" asChild>
            <Link href={`/home/<USER>/education/meals/menus/new`}>
              <Calendar className="mr-2 h-4 w-4" />
              Tạo thực đơn
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/home/<USER>/education/meals/food-items/new`}>
              <Plus className="mr-2 h-4 w-4" />
              Thêm món ăn
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Thực đơn
                </p>
                <p className="text-2xl font-bold">{stats.totalMenus}</p>
              </div>
              <Calendar className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Món ăn
                </p>
                <p className="text-2xl font-bold">{stats.foodItems}</p>
              </div>
              <UtensilsCrossed className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Học viên phục vụ
                </p>
                <p className="text-2xl font-bold">{stats.studentsServed}</p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Nguyên liệu
                </p>
                <p className="text-2xl font-bold">{stats.inventoryItems}</p>
              </div>
              <Package className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Tổng quan</TabsTrigger>
          <TabsTrigger value="menus">Thực đơn</TabsTrigger>
          <TabsTrigger value="food-items">Món ăn</TabsTrigger>
          <TabsTrigger value="inventory">Kho</TabsTrigger>
          <TabsTrigger value="nutrition">Dinh dưỡng</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Today's Menu */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <span>Thực đơn hôm nay</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {todayMenu.map((meal) => (
                    <div
                      key={meal.id}
                      className="flex items-start space-x-3 rounded-lg border p-3"
                    >
                      <div className="flex-shrink-0">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-orange-100">
                          <UtensilsCrossed className="h-5 w-5 text-orange-600" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">
                            {getMealTypeText(meal.mealType)}
                          </h4>
                          <span className="text-muted-foreground text-sm">
                            {meal.time}
                          </span>
                        </div>
                        <div className="mt-2 flex flex-wrap gap-1">
                          {meal.items.map((item, index) => (
                            <Badge
                              key={index}
                              variant="outline"
                              className="text-xs"
                            >
                              {item}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Inventory Alerts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Package className="h-5 w-5" />
                  <span>Cảnh báo kho</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {inventoryAlerts.map((alert) => (
                    <div
                      key={alert.id}
                      className="flex items-center justify-between rounded-lg border p-3"
                    >
                      <div className="flex items-center space-x-3">
                        <div
                          className={`h-3 w-3 rounded-full ${
                            alert.status === 'critical'
                              ? 'bg-red-500'
                              : 'bg-yellow-500'
                          }`}
                        ></div>
                        <div>
                          <h4 className="font-medium">{alert.item}</h4>
                          <p className="text-muted-foreground text-sm">
                            Còn {alert.currentStock} {alert.unit} / Tối thiểu{' '}
                            {alert.minStock} {alert.unit}
                          </p>
                        </div>
                      </div>
                      <Badge
                        className={
                          alert.status === 'critical'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }
                      >
                        {alert.status === 'critical'
                          ? 'Cần bổ sung'
                          : 'Sắp hết'}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="menus">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Thuc don ({allMenus.length})</CardTitle>
                <Button asChild>
                  <Link href={`/home/<USER>/education/meals/menus/new`}>
                    <Plus className="mr-2 h-4 w-4" />
                    Tao thuc don
                  </Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {allMenus.length === 0 ? (
                <div className="py-12 text-center">
                  <Calendar className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                  <h3 className="mb-2 text-lg font-medium text-gray-900">
                    Chua co thuc don nao
                  </h3>
                  <p className="mb-6 text-gray-500">
                    Tao thuc don dau tien cho hoc vien.
                  </p>
                  <Button asChild>
                    <Link href={`/home/<USER>/education/meals/menus/new`}>
                      <Plus className="mr-2 h-4 w-4" />
                      Tao thuc don
                    </Link>
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {allMenus.slice(0, 5).map((menu) => (
                    <div key={menu.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">
                            {new Date(menu.date).toLocaleDateString('vi-VN')}
                          </span>
                          <Badge className={
                            menu.mealType === 'breakfast' ? 'bg-yellow-100 text-yellow-800' :
                            menu.mealType === 'lunch' ? 'bg-green-100 text-green-800' :
                            menu.mealType === 'dinner' ? 'bg-blue-100 text-blue-800' :
                            'bg-purple-100 text-purple-800'
                          }>
                            {menu.mealType === 'breakfast' ? 'Sang' :
                             menu.mealType === 'lunch' ? 'Trua' :
                             menu.mealType === 'dinner' ? 'Toi' : 'Snack'}
                          </Badge>
                        </div>
                      </div>
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Mon an:</span> {menu.items.join(', ') || 'Chua co mon an'}
                      </div>
                    </div>
                  ))}
                  {allMenus.length > 5 && (
                    <div className="text-center pt-4">
                      <Button variant="outline" asChild>
                        <Link href={`/home/<USER>/education/meals/menus`}>
                          Xem tat ca {allMenus.length} thuc don
                        </Link>
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="food-items">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Mon an ({allFoodItems.length})</CardTitle>
                <Button asChild>
                  <Link href={`/home/<USER>/education/meals/food-items/new`}>
                    <Plus className="mr-2 h-4 w-4" />
                    Them mon an
                  </Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {allFoodItems.length === 0 ? (
                <div className="py-12 text-center">
                  <ChefHat className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                  <h3 className="mb-2 text-lg font-medium text-gray-900">
                    Chua co mon an nao
                  </h3>
                  <p className="mb-6 text-gray-500">
                    Them mon an dau tien vao he thong.
                  </p>
                  <Button asChild>
                    <Link href={`/home/<USER>/education/meals/food-items/new`}>
                      <Plus className="mr-2 h-4 w-4" />
                      Them mon an
                    </Link>
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {allFoodItems.slice(0, 8).map((item, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{item.name}</span>
                          {item.category && (
                            <Badge variant="outline">{item.category}</Badge>
                          )}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(item.date).toLocaleDateString('vi-VN')}
                        </div>
                      </div>
                      {item.description && (
                        <p className="text-sm text-gray-600 mb-2">{item.description}</p>
                      )}
                      {item.nutritional_info && (
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          {item.nutritional_info.calories && (
                            <span>{item.nutritional_info.calories} kcal</span>
                          )}
                          {item.cost_per_serving && (
                            <span>{item.cost_per_serving.toLocaleString()} VND</span>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                  {allFoodItems.length > 8 && (
                    <div className="text-center pt-4">
                      <Button variant="outline" asChild>
                        <Link href={`/home/<USER>/education/meals/food-items`}>
                          Xem tat ca {allFoodItems.length} mon an
                        </Link>
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="inventory">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Kho hang ({allInventoryItems.length})</CardTitle>
                <Button asChild>
                  <Link href={`/home/<USER>/education/meals/inventory/new`}>
                    <Plus className="mr-2 h-4 w-4" />
                    Them vat pham
                  </Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {allInventoryItems.length === 0 ? (
                <div className="py-12 text-center">
                  <Package className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                  <h3 className="mb-2 text-lg font-medium text-gray-900">
                    Chua co vat pham nao
                  </h3>
                  <p className="mb-6 text-gray-500">
                    Them vat pham dau tien vao kho.
                  </p>
                  <Button asChild>
                    <Link href={`/home/<USER>/education/meals/inventory/new`}>
                      <Plus className="mr-2 h-4 w-4" />
                      Them vat pham
                    </Link>
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {allInventoryItems.slice(0, 6).map((item, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{item.name}</span>
                          {item.category && (
                            <Badge variant="outline">{item.category}</Badge>
                          )}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(item.dateAdded).toLocaleDateString('vi-VN')}
                        </div>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        {item.current_quantity && (
                          <div>
                            <span className="text-gray-600">So luong:</span>
                            <span className="ml-1 font-medium">{item.current_quantity} {item.unit}</span>
                          </div>
                        )}
                        {item.cost_per_unit && (
                          <div>
                            <span className="text-gray-600">Gia:</span>
                            <span className="ml-1 font-medium">{item.cost_per_unit.toLocaleString()} VND</span>
                          </div>
                        )}
                        {item.supplier && (
                          <div>
                            <span className="text-gray-600">NCC:</span>
                            <span className="ml-1">{item.supplier}</span>
                          </div>
                        )}
                        {item.expiry_date && (
                          <div>
                            <span className="text-gray-600">Het han:</span>
                            <span className="ml-1">{new Date(item.expiry_date).toLocaleDateString('vi-VN')}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                  {allInventoryItems.length > 6 && (
                    <div className="text-center pt-4">
                      <Button variant="outline" asChild>
                        <Link href={`/home/<USER>/education/meals/inventory`}>
                          Xem tat ca {allInventoryItems.length} vat pham
                        </Link>
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="nutrition">
          <Card>
            <CardContent className="p-6">
              <div className="py-12 text-center">
                <Apple className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Theo dõi dinh dưỡng
                </h3>
                <p className="mb-6 text-gray-500">
                  Phân tích dinh dưỡng và chế độ ăn của học viên.
                </p>
                <Button asChild>
                  <Link href={`/home/<USER>/education/meals/nutrition`}>
                    <ClipboardList className="mr-2 h-4 w-4" />
                    Xem báo cáo dinh dưỡng
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default MealManagementDashboard;
