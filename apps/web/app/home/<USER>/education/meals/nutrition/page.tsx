import { Suspense } from 'react';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { withI18n } from '~/lib/i18n/with-i18n';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

import { NutritionDashboard } from './_components/nutrition-dashboard';

interface Props {
  params: Promise<{
    account: string;
  }>;
}

async function NutritionPage({ params }: Props) {
  const i18n = await createI18nServerInstance();
  const resolvedParams = await params;
  const { user, account } = await loadTeamWorkspace(resolvedParams.account);

  return (
    <div className="flex flex-1 flex-col space-y-4 pb-8">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title={i18n.t('education:meals.nutrition.title', 'Nutrition Analysis')}
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education`}
            >
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item
              href={`/home/<USER>/education/meals`}
            >
              {i18n.t('education:breadcrumbs.meals')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>
              {i18n.t('education:meals.nutrition.title')}
            </AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-7xl px-4">
        <Suspense fallback={<div>Loading...</div>}>
          <NutritionDashboard account={resolvedParams.account} />
        </Suspense>
      </div>
    </div>
  );
}

export default withI18n(NutritionPage);

export const metadata = {
  title: 'Nutrition Analysis',
  description: 'Analyze nutritional content of meals and food items',
};
