'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from '@kit/ui/sonner';

import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Textarea } from '@kit/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { ArrowLeft } from 'lucide-react';

interface Props {
  account: string;
}

export function AddInventoryForm({ account }: Props) {
  const { t } = useTranslation('education');
  const router = useRouter();
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    category: 'thuc_pham',
    description: '',
    unit: 'kg',
    current_quantity: 0,
    minimum_quantity: 0,
    cost_per_unit: 0,
    supplier: '',
    expiry_date: '',
    storage_location: '',
    notes: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      const accountId = workspace.account.id;
      const userId = workspace.user?.id;

      if (!accountId || !userId) {
        throw new Error('Account or user not found');
      }

      // Create inventory item as meal plan with special notes
      const inventoryItem = {
        id: Date.now().toString(),
        name: formData.name,
        category: formData.category,
        description: formData.description,
        unit: formData.unit,
        current_quantity: formData.current_quantity,
        minimum_quantity: formData.minimum_quantity,
        cost_per_unit: formData.cost_per_unit,
        supplier: formData.supplier,
        expiry_date: formData.expiry_date,
        storage_location: formData.storage_location,
        notes: formData.notes,
      };

      const { error: insertError } = await supabase.from('meal_plans').insert({
        account_id: accountId,
        date: new Date().toISOString().split('T')[0],
        meal_type: 'inventory',
        menu_items: [inventoryItem],
        special_notes: `Inventory: ${formData.name} - ${formData.description}`,
        dietary_accommodations: [formData.category],
        cost_per_serving: formData.cost_per_unit,
        created_by: userId,
      });

      if (insertError) throw insertError;

      toast.success('Vat pham da duoc them thanh cong');
      router.push(`/home/<USER>/education/meals`);
    } catch (err: any) {
      console.error('Error creating inventory item:', err);
      setError(err.message || 'Co loi xay ra khi them vat pham');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Quay lai</span>
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Thong tin co ban</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">Ten vat pham *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Vi du: Gao trang"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Loai vat pham</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="thuc_pham">Thuc pham</SelectItem>
                    <SelectItem value="gia_vi">Gia vi</SelectItem>
                    <SelectItem value="do_uong">Do uong</SelectItem>
                    <SelectItem value="dung_cu">Dung cu</SelectItem>
                    <SelectItem value="ve_sinh">Ve sinh</SelectItem>
                    <SelectItem value="khac">Khac</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="unit">Don vi tinh</Label>
                <Select value={formData.unit} onValueChange={(value) => handleInputChange('unit', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="kg">Kilogram (kg)</SelectItem>
                    <SelectItem value="g">Gram (g)</SelectItem>
                    <SelectItem value="lit">Lit</SelectItem>
                    <SelectItem value="ml">Mililit (ml)</SelectItem>
                    <SelectItem value="cai">Cai</SelectItem>
                    <SelectItem value="hop">Hop</SelectItem>
                    <SelectItem value="goi">Goi</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Mo ta</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Mo ta chi tiet ve vat pham"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>So luong va gia ca</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="current_quantity">So luong hien tai *</Label>
                <Input
                  id="current_quantity"
                  type="number"
                  value={formData.current_quantity}
                  onChange={(e) => handleInputChange('current_quantity', parseFloat(e.target.value) || 0)}
                  min="0"
                  step="0.1"
                  required
                />
              </div>
              <div>
                <Label htmlFor="minimum_quantity">So luong toi thieu</Label>
                <Input
                  id="minimum_quantity"
                  type="number"
                  value={formData.minimum_quantity}
                  onChange={(e) => handleInputChange('minimum_quantity', parseFloat(e.target.value) || 0)}
                  min="0"
                  step="0.1"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="cost_per_unit">Gia moi don vi (VND)</Label>
              <Input
                id="cost_per_unit"
                type="number"
                value={formData.cost_per_unit}
                onChange={(e) => handleInputChange('cost_per_unit', parseFloat(e.target.value) || 0)}
                min="0"
                step="100"
                placeholder="Gia tien moi don vi"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Thong tin bo sung</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="supplier">Nha cung cap</Label>
              <Input
                id="supplier"
                value={formData.supplier}
                onChange={(e) => handleInputChange('supplier', e.target.value)}
                placeholder="Ten nha cung cap"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="expiry_date">Ngay het han</Label>
                <Input
                  id="expiry_date"
                  type="date"
                  value={formData.expiry_date}
                  onChange={(e) => handleInputChange('expiry_date', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="storage_location">Vi tri luu tru</Label>
                <Input
                  id="storage_location"
                  value={formData.storage_location}
                  onChange={(e) => handleInputChange('storage_location', e.target.value)}
                  placeholder="Vi du: Kho lanh, Ke A1"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="notes">Ghi chu</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Ghi chu them ve vat pham"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {error && (
          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push(`/home/<USER>/education/meals`)}
          >
            Huy
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? 'Dang them...' : 'Them vat pham'}
          </Button>
        </div>
      </form>
    </div>
  );
}
