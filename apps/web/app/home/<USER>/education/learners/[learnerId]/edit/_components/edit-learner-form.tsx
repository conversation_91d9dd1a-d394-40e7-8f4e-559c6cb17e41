'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Textarea } from '@kit/ui/textarea';

interface Learner {
  id: string;
  learner_code: string;
  full_name: string;
  nickname: string;
  date_of_birth: string;
  gender: string;
  address: string;
  medical_info: any;
  emergency_contact: any;
  enrollment_date: string;
  status: string;
}

interface Props {
  account: string;
  learnerId: string;
}

function EditLearnerForm({ account, learnerId }: Props) {
  const { t } = useTranslation('education');
  const [learner, setLearner] = useState<Learner | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();
  const router = useRouter();

  // Form state
  const [formData, setFormData] = useState({
    full_name: '',
    nickname: '',
    date_of_birth: '',
    gender: '',
    address: '',
    status: 'active',
    // Emergency contact
    emergency_name: '',
    emergency_phone: '',
    emergency_relationship: '',
    emergency_email: '',
    // Health info
    allergies: '',
    medical_conditions: '',
    special_needs: '',
  });

  useEffect(() => {
    loadLearner();
  }, [learnerId]);

  const loadLearner = async () => {
    try {
      setLoading(true);
      setError(null);

      const accountId = workspace.account.id;

      const { data: learner, error: learnerError } = await supabase
        .from('learners')
        .select('*')
        .eq('id', learnerId)
        .eq('account_id', accountId)
        .single();

      if (learnerError) throw learnerError;

      setLearner(learner);

      // Populate form data
      const emergencyContact = learner.emergency_contact || {};
      const medicalInfo = learner.medical_info || {};

      setFormData({
        full_name: learner.full_name || '',
        nickname: learner.nickname || '',
        date_of_birth: learner.date_of_birth || '',
        gender: learner.gender || '',
        address: learner.address || '',
        status: learner.status || 'active',
        emergency_name: emergencyContact.name || '',
        emergency_phone: emergencyContact.phone || '',
        emergency_relationship: emergencyContact.relationship || '',
        emergency_email: emergencyContact.email || '',
        allergies: Array.isArray(medicalInfo.allergies)
          ? medicalInfo.allergies.join(', ')
          : '',
        medical_conditions: Array.isArray(medicalInfo.medical_conditions)
          ? medicalInfo.medical_conditions.join(', ')
          : '',
        special_needs: medicalInfo.special_needs || '',
      });
    } catch (err: any) {
      console.error('Error loading learner:', err);
      setError(err.message || 'Failed to load learner');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError(null);

      const accountId = workspace.account.id;

      // Prepare data
      const emergencyContact = {
        name: formData.emergency_name,
        phone: formData.emergency_phone,
        relationship: formData.emergency_relationship,
        email: formData.emergency_email,
      };

      const medicalInfo = {
        allergies: formData.allergies
          ? formData.allergies
              .split(',')
              .map((s) => s.trim())
              .filter((s) => s)
          : [],
        medical_conditions: formData.medical_conditions
          ? formData.medical_conditions
              .split(',')
              .map((s) => s.trim())
              .filter((s) => s)
          : [],
        special_needs: formData.special_needs,
      };

      const { error: updateError } = await supabase
        .from('learners')
        .update({
          full_name: formData.full_name,
          nickname: formData.nickname,
          date_of_birth: formData.date_of_birth,
          gender: formData.gender,
          address: formData.address,
          status: formData.status,
          emergency_contact: emergencyContact,
          medical_info: medicalInfo,
          updated_at: new Date().toISOString(),
        })
        .eq('id', learnerId)
        .eq('account_id', accountId);

      if (updateError) throw updateError;

      router.push(`/home/<USER>/education/learners/${learnerId}`);
    } catch (err: any) {
      console.error('Error updating learner:', err);
      setError(err.message || 'Failed to update learner');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-b-2"></div>
          <p className="text-muted-foreground mt-2 text-sm">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (error && !learner) {
    return (
      <div className="py-8 text-center">
        <p className="text-red-600">{error}</p>
        <Button onClick={loadLearner} className="mt-4">
          Thử lại
        </Button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Thông tin cá nhân</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="full_name">Họ và tên *</Label>
              <Input
                id="full_name"
                value={formData.full_name}
                onChange={(e) => handleInputChange('full_name', e.target.value)}
                placeholder="Nhập họ và tên"
                required
              />
            </div>
            <div>
              <Label htmlFor="nickname">Biệt danh</Label>
              <Input
                id="nickname"
                value={formData.nickname}
                onChange={(e) => handleInputChange('nickname', e.target.value)}
                placeholder="Biệt danh (tùy chọn)"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="date_of_birth">Ngày sinh *</Label>
              <Input
                id="date_of_birth"
                type="date"
                value={formData.date_of_birth}
                onChange={(e) =>
                  handleInputChange('date_of_birth', e.target.value)
                }
                required
              />
            </div>
            <div>
              <Label htmlFor="gender">Giới tính *</Label>
              <Select
                value={formData.gender}
                onValueChange={(value) => handleInputChange('gender', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn giới tính" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Nam</SelectItem>
                  <SelectItem value="female">Nữ</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="address">Địa chỉ</Label>
            <Textarea
              id="address"
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              placeholder="Địa chỉ nơi ở"
              rows={2}
            />
          </div>

          <div>
            <Label htmlFor="status">Trạng thái</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleInputChange('status', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Đang học</SelectItem>
                <SelectItem value="inactive">Tạm nghỉ</SelectItem>
                <SelectItem value="graduated">Đã tốt nghiệp</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Thông tin liên hệ khẩn cấp</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="emergency_name">Họ và tên</Label>
              <Input
                id="emergency_name"
                value={formData.emergency_name}
                onChange={(e) =>
                  handleInputChange('emergency_name', e.target.value)
                }
                placeholder="Họ tên người liên hệ"
              />
            </div>
            <div>
              <Label htmlFor="emergency_relationship">Mối quan hệ</Label>
              <Select
                value={formData.emergency_relationship}
                onValueChange={(value) =>
                  handleInputChange('emergency_relationship', value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn mối quan hệ" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="father">Bố</SelectItem>
                  <SelectItem value="mother">Mẹ</SelectItem>
                  <SelectItem value="guardian">Người giám hộ</SelectItem>
                  <SelectItem value="grandparent">Ông/Bà</SelectItem>
                  <SelectItem value="other">Khác</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="emergency_phone">Số điện thoại</Label>
              <Input
                id="emergency_phone"
                value={formData.emergency_phone}
                onChange={(e) =>
                  handleInputChange('emergency_phone', e.target.value)
                }
                placeholder="Số điện thoại liên hệ"
              />
            </div>
            <div>
              <Label htmlFor="emergency_email">Email</Label>
              <Input
                id="emergency_email"
                type="email"
                value={formData.emergency_email}
                onChange={(e) =>
                  handleInputChange('emergency_email', e.target.value)
                }
                placeholder="Email liên hệ"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Thông tin sức khỏe</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="allergies">Dị ứng</Label>
            <Input
              id="allergies"
              value={formData.allergies}
              onChange={(e) => handleInputChange('allergies', e.target.value)}
              placeholder="Các loại dị ứng (phân cách bằng dấu phẩy)"
            />
            <p className="text-muted-foreground mt-1 text-xs">
              Ví dụ: Đậu phộng, Tôm cua, Bụi nhà
            </p>
          </div>

          <div>
            <Label htmlFor="medical_conditions">Tình trạng y tế</Label>
            <Input
              id="medical_conditions"
              value={formData.medical_conditions}
              onChange={(e) =>
                handleInputChange('medical_conditions', e.target.value)
              }
              placeholder="Các tình trạng y tế (phân cách bằng dấu phẩy)"
            />
            <p className="text-muted-foreground mt-1 text-xs">
              Ví dụ: Hen suyễn, Tiểu đường, Tim mạch
            </p>
          </div>

          <div>
            <Label htmlFor="special_needs">Nhu cầu đặc biệt</Label>
            <Textarea
              id="special_needs"
              value={formData.special_needs}
              onChange={(e) =>
                handleInputChange('special_needs', e.target.value)
              }
              placeholder="Mô tả các nhu cầu đặc biệt của trẻ"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {error && (
        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() =>
            router.push(`/home/<USER>/education/learners/${learnerId}`)
          }
        >
          Hủy
        </Button>
        <Button type="submit" disabled={saving}>
          {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
        </Button>
      </div>
    </form>
  );
}

export default EditLearnerForm;
