'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Textarea } from '@kit/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { toast } from '@kit/ui/sonner';
import { Save, ArrowLeft } from 'lucide-react';

interface Props {
  account: string;
}

const learnerSchema = z.object({
  learner_code: z.string().min(1, 'Learner code is required'),
  full_name: z.string().min(1, 'Full name is required'),
  nickname: z.string().optional(),
  date_of_birth: z.string().min(1, 'Date of birth is required'),
  gender: z.enum(['male', 'female', 'other']),
  address: z.string().min(1, 'Address is required'),
  guardian_name: z.string().min(1, 'Guardian name is required'),
  guardian_phone: z.string().min(1, 'Guardian phone is required'),
  guardian_email: z.string().email('Invalid email').optional(),
  guardian_relationship: z.string().min(1, 'Guardian relationship is required'),
  medical_conditions: z.string().optional(),
  allergies: z.string().optional(),
  emergency_contact_name: z.string().min(1, 'Emergency contact name is required'),
  emergency_contact_phone: z.string().min(1, 'Emergency contact phone is required'),
});

type LearnerFormData = z.infer<typeof learnerSchema>;

export function AddLearnerForm({ account }: Props) {
  const { t } = useTranslation('education');
  const router = useRouter();
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();
  
  const [loading, setLoading] = useState(false);

  const currentAccount = accounts?.find(acc => acc.slug === account);

  const form = useForm<LearnerFormData>({
    resolver: zodResolver(learnerSchema),
    defaultValues: {
      learner_code: '',
      full_name: '',
      nickname: '',
      date_of_birth: '',
      gender: 'male',
      address: '',
      guardian_name: '',
      guardian_phone: '',
      guardian_email: '',
      guardian_relationship: 'father',
      medical_conditions: '',
      allergies: '',
      emergency_contact_name: '',
      emergency_contact_phone: '',
    },
  });

  const onSubmit = async (data: LearnerFormData) => {
    if (!currentAccount?.id) {
      toast.error('Account not found');
      return;
    }

    try {
      setLoading(true);

      // Use account directly
      const accountId = currentAccount.id;

      // Generate unique learner code if not provided
      let learnerCode = data.learner_code;
      if (!learnerCode) {
        // Generate code using account prefix + timestamp
        const prefix = currentAccount.name ? currentAccount.name.substring(0, 3).toUpperCase() : 'EDU';
        const timestamp = Date.now().toString().slice(-6);
        learnerCode = `${prefix}${timestamp}`;
      }

      // Create learner
      const { data: learner, error: learnerError } = await supabase
        .from('learners')
        .insert({
          learner_code: learnerCode,
          full_name: data.full_name,
          nickname: data.nickname || null,
          date_of_birth: data.date_of_birth,
          gender: data.gender,
          address: data.address,
          status: 'active',
          account_id: accountId,
          medical_info: {
            medical_conditions: data.medical_conditions ? [data.medical_conditions] : [],
            allergies: data.allergies ? [data.allergies] : [],
            emergency_contact: {
              name: data.emergency_contact_name,
              phone: data.emergency_contact_phone,
              relationship: 'emergency',
            },
          },
        })
        .select()
        .single();

      if (learnerError) throw learnerError;

      // Note: Guardian information is stored in learner's emergency_contact field
      // Separate guardian management will be handled in dedicated guardian module

      toast.success(t('learners.addSuccess', 'Learner added successfully'));

      // Navigate back and refresh the page to show new learner
      router.push(`/home/<USER>/education/learners`);
      router.refresh();
    } catch (error: any) {
      console.error('Error adding learner:', error);
      toast.error(error.message || 'Failed to add learner');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>{t('common.back')}</span>
        </Button>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t('learners.form.basicInfo', 'Basic Information')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="learner_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('learners.form.learnerCode', 'Learner Code')}</FormLabel>
                      <FormControl>
                        <Input placeholder="Auto-generated if empty" {...field} />
                      </FormControl>
                      <FormDescription>
                        Leave empty to auto-generate unique code
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="full_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('learners.form.fullName', 'Full Name')}</FormLabel>
                      <FormControl>
                        <Input placeholder="Nguyễn Văn An" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="nickname"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('learners.form.nickname', 'Nickname')}</FormLabel>
                      <FormControl>
                        <Input placeholder="Bé An" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="date_of_birth"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('learners.form.dateOfBirth', 'Date of Birth')}</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('learners.form.gender', 'Gender')}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select gender" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="male">{t('learners.gender.male')}</SelectItem>
                          <SelectItem value="female">{t('learners.gender.female')}</SelectItem>
                          <SelectItem value="other">{t('learners.gender.other')}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('learners.form.address', 'Address')}</FormLabel>
                    <FormControl>
                      <Textarea placeholder="123 Đường ABC, Quận 1, TP.HCM" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Guardian Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t('learners.form.guardianInfo', 'Guardian Information')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="guardian_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('learners.form.guardianName', 'Guardian Name')}</FormLabel>
                      <FormControl>
                        <Input placeholder="Nguyễn Văn Bình" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="guardian_relationship"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('learners.form.relationship', 'Relationship')}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select relationship" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="father">{t('learners.relationship.father', 'Father')}</SelectItem>
                          <SelectItem value="mother">{t('learners.relationship.mother', 'Mother')}</SelectItem>
                          <SelectItem value="guardian">{t('learners.relationship.guardian', 'Guardian')}</SelectItem>
                          <SelectItem value="other">{t('learners.relationship.other', 'Other')}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="guardian_phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('learners.form.guardianPhone', 'Guardian Phone')}</FormLabel>
                      <FormControl>
                        <Input placeholder="0901234567" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="guardian_email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('learners.form.guardianEmail', 'Guardian Email')}</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Health Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t('learners.form.healthInfo', 'Health Information')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="medical_conditions"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('learners.form.medicalConditions', 'Medical Conditions')}</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Any medical conditions..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="allergies"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('learners.form.allergies', 'Allergies')}</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Any allergies..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="emergency_contact_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('learners.form.emergencyContactName', 'Emergency Contact Name')}</FormLabel>
                      <FormControl>
                        <Input placeholder="Nguyễn Thị Cúc" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="emergency_contact_phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('learners.form.emergencyContactPhone', 'Emergency Contact Phone')}</FormLabel>
                      <FormControl>
                        <Input placeholder="0901234568" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              {t('common.cancel')}
            </Button>
            <Button type="submit" disabled={loading}>
              <Save className="h-4 w-4 mr-2" />
              {loading ? t('common.saving', 'Saving...') : t('common.save')}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
