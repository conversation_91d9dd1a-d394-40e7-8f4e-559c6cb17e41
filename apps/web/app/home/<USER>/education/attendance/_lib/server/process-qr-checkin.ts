'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export interface ProcessQRCheckinParams {
  qrCode: string;
  learnerId: string;
  checkinLocation?: {
    latitude: number;
    longitude: number;
    accuracy?: number;
  };
  deviceInfo?: {
    userAgent: string;
    platform: string;
    language: string;
  };
  guardianInfo?: {
    name: string;
    phone?: string;
    relationship: string;
  };
}

export interface ProcessQRCheckinResult {
  checkinId: string;
  status: 'present' | 'late';
  checkinTime: string;
  withinTimeWindow: boolean;
  message: string;
}

export async function processQRCheckin(
  params: ProcessQRCheckinParams
): Promise<ProcessQRCheckinResult> {
  const client = getSupabaseServerClient();

  try {
    const { data, error } = await client.rpc('process_qr_checkin', {
      p_qr_code: params.qrCode,
      p_learner_id: params.learnerId,
      p_checkin_location: params.checkinLocation || null,
      p_device_info: params.deviceInfo || null,
      p_guardian_info: params.guardianInfo || null,
    });

    if (error) {
      console.error('Supabase RPC error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        function: 'process_qr_checkin',
        params: {
          qrCode: params.qrCode.substring(0, 10) + '...',
          learnerId: params.learnerId,
        },
      });
      throw error;
    }

    if (!data.success) {
      // Handle specific error cases
      const errorCode = data.error;
      let userMessage = data.message;

      switch (errorCode) {
        case 'INVALID_QR_CODE':
          userMessage = 'Mã QR không hợp lệ hoặc đã hết hạn';
          break;
        case 'MAX_CHECKINS_REACHED':
          userMessage = 'Đã đạt số lượng điểm danh tối đa cho phiên này';
          break;
        case 'NOT_ENROLLED':
          userMessage = 'Học sinh chưa đăng ký chương trình này';
          break;
        case 'ALREADY_CHECKED_IN':
          userMessage = 'Học sinh đã điểm danh hôm nay rồi';
          break;
        default:
          userMessage = data.message || 'Không thể điểm danh';
      }

      throw new Error(userMessage);
    }

    return {
      checkinId: data.checkin_id,
      status: data.status,
      checkinTime: data.checkin_time,
      withinTimeWindow: data.within_time_window,
      message: data.message,
    };
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to process QR check-in:', {
      error: message,
      params: {
        qrCode: params.qrCode.substring(0, 10) + '...',
        learnerId: params.learnerId,
      },
      context: 'process-qr-checkin',
    });
    throw new Error(message);
  }
}

export async function verifyCheckinRecord(
  checkinId: string,
  status: 'verified' | 'rejected',
  notes?: string
): Promise<void> {
  const client = getSupabaseServerClient();

  try {
    const { error } = await client
      .from('self_checkin_records')
      .update({
        verification_status: status,
        verification_notes: notes,
        verified_by: (await client.auth.getUser()).data.user?.id,
        verified_at: new Date().toISOString(),
      })
      .eq('id', checkinId);

    if (error) {
      console.error('Supabase update error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        table: 'self_checkin_records',
        checkinId,
        status,
      });
      throw error;
    }

    // If verified, update corresponding attendance record
    if (status === 'verified') {
      await client
        .from('attendance')
        .update({
          status: 'present',
          updated_at: new Date().toISOString(),
        })
        .eq('metadata->checkin_record_id', checkinId);
    }
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to verify check-in record:', {
      error: message,
      checkinId,
      status,
      context: 'process-qr-checkin',
    });
    throw new Error(`Failed to verify check-in record: ${message}`);
  }
}
