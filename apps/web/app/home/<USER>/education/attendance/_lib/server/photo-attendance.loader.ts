import { SupabaseClient } from '@supabase/supabase-js';

import { Database } from '~/lib/database.types';

export type LearnerPhoto = {
  id: string;
  learner_id: string;
  photo_url: string;
  photo_type: 'profile' | 'verification' | 'attendance';
  face_encoding?: Record<string, any>;
  is_primary: boolean;
  quality_score?: number;
  learner?: {
    id: string;
    full_name: string;
    nickname?: string;
    learner_code?: string;
  };
};

export type PhotoAttendanceSession = {
  id: string;
  account_id: string;
  program_id: string;
  session_date: string;
  session_time?: string;
  instructor_id?: string;
  batch_photo_url?: string;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  total_faces_detected: number;
  total_students_recognized: number;
  confidence_threshold: number;
  processing_metadata?: Record<string, any>;
  created_at: string;
  program?: {
    id: string;
    name: string;
    program_type: string;
  };
  instructor?: {
    id: string;
    full_name: string;
    employee_code?: string;
  };
};

export type PhotoRecognitionResult = {
  id: string;
  photo_session_id: string;
  learner_id?: string;
  detected_face_box?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  confidence_score?: number;
  recognition_status: 'detected' | 'recognized' | 'verified' | 'rejected';
  manual_verification?: boolean;
  verification_notes?: string;
  learner?: {
    id: string;
    full_name: string;
    nickname?: string;
    learner_code?: string;
  };
};

export async function loadLearnerPhotos(
  client: SupabaseClient<Database>,
  accountId: string,
  programId?: string
): Promise<LearnerPhoto[]> {
  if (!accountId) {
    throw new Error('Account ID is required');
  }

  try {
    let query = client
      .from('learner_photos')
      .select(`
        *,
        learners!learner_id (
          id,
          full_name,
          nickname,
          learner_code
        )
      `)
      .eq('account_id', accountId)
      .order('created_at', { ascending: false });

    if (programId) {
      // Filter by program through enrollments
      query = query.in('learner_id', 
        client
          .from('enrollments')
          .select('learner_id')
          .eq('program_id', programId)
          .eq('status', 'active')
      );
    }

    const { data, error } = await query;

    if (error) {
      console.error('Supabase query error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        query: 'learner_photos.select',
        accountId,
        programId,
      });
      throw error;
    }

    return (data || []).map((photo) => ({
      id: photo.id,
      learner_id: photo.learner_id,
      photo_url: photo.photo_url,
      photo_type: photo.photo_type as 'profile' | 'verification' | 'attendance',
      face_encoding: photo.face_encoding,
      is_primary: photo.is_primary,
      quality_score: photo.quality_score,
      learner: photo.learners ? {
        id: photo.learners.id,
        full_name: photo.learners.full_name,
        nickname: photo.learners.nickname,
        learner_code: photo.learners.learner_code,
      } : undefined,
    }));
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to load learner photos:', {
      error: message,
      accountId,
      programId,
      context: 'photo-attendance.loader',
    });
    throw new Error(`Failed to load learner photos: ${message}`);
  }
}

export async function loadPhotoAttendanceSessions(
  client: SupabaseClient<Database>,
  accountId: string,
  programId?: string,
  limit = 20
): Promise<PhotoAttendanceSession[]> {
  if (!accountId) {
    throw new Error('Account ID is required');
  }

  try {
    let query = client
      .from('photo_attendance_sessions')
      .select(`
        *,
        programs!program_id (
          id,
          name,
          program_type
        ),
        instructors!instructor_id (
          id,
          full_name,
          employee_code
        )
      `)
      .eq('account_id', accountId)
      .order('session_date', { ascending: false })
      .limit(limit);

    if (programId) {
      query = query.eq('program_id', programId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Supabase query error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        query: 'photo_attendance_sessions.select',
        accountId,
        programId,
      });
      throw error;
    }

    return (data || []).map((session) => ({
      id: session.id,
      account_id: session.account_id,
      program_id: session.program_id,
      session_date: session.session_date,
      session_time: session.session_time,
      instructor_id: session.instructor_id,
      batch_photo_url: session.batch_photo_url,
      processing_status: session.processing_status as 'pending' | 'processing' | 'completed' | 'failed',
      total_faces_detected: session.total_faces_detected,
      total_students_recognized: session.total_students_recognized,
      confidence_threshold: session.confidence_threshold,
      processing_metadata: session.processing_metadata,
      created_at: session.created_at,
      program: session.programs ? {
        id: session.programs.id,
        name: session.programs.name,
        program_type: session.programs.program_type,
      } : undefined,
      instructor: session.instructors ? {
        id: session.instructors.id,
        full_name: session.instructors.full_name,
        employee_code: session.instructors.employee_code,
      } : undefined,
    }));
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to load photo attendance sessions:', {
      error: message,
      accountId,
      programId,
      context: 'photo-attendance.loader',
    });
    throw new Error(`Failed to load photo attendance sessions: ${message}`);
  }
}

export async function loadPhotoRecognitionResults(
  client: SupabaseClient<Database>,
  sessionId: string
): Promise<PhotoRecognitionResult[]> {
  if (!sessionId) {
    throw new Error('Session ID is required');
  }

  try {
    const { data, error } = await client
      .from('photo_recognition_results')
      .select(`
        *,
        learners!learner_id (
          id,
          full_name,
          nickname,
          learner_code
        )
      `)
      .eq('photo_session_id', sessionId)
      .order('confidence_score', { ascending: false });

    if (error) {
      console.error('Supabase query error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        query: 'photo_recognition_results.select',
        sessionId,
      });
      throw error;
    }

    return (data || []).map((result) => ({
      id: result.id,
      photo_session_id: result.photo_session_id,
      learner_id: result.learner_id,
      detected_face_box: result.detected_face_box,
      confidence_score: result.confidence_score,
      recognition_status: result.recognition_status as 'detected' | 'recognized' | 'verified' | 'rejected',
      manual_verification: result.manual_verification,
      verification_notes: result.verification_notes,
      learner: result.learners ? {
        id: result.learners.id,
        full_name: result.learners.full_name,
        nickname: result.learners.nickname,
        learner_code: result.learners.learner_code,
      } : undefined,
    }));
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to load photo recognition results:', {
      error: message,
      sessionId,
      context: 'photo-attendance.loader',
    });
    throw new Error(`Failed to load photo recognition results: ${message}`);
  }
}
