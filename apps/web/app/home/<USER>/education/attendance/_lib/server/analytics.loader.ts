import { SupabaseClient } from '@supabase/supabase-js';

import { Database } from '~/lib/database.types';

export type AttendanceAnalytics = {
  id: string;
  account_id: string;
  program_id: string;
  learner_id?: string;
  analysis_date: string;
  analysis_type: 'daily' | 'weekly' | 'monthly' | 'term';
  attendance_rate: number;
  total_sessions: number;
  present_sessions: number;
  absent_sessions: number;
  late_sessions: number;
  excused_sessions: number;
  streak_present: number;
  streak_absent: number;
  patterns?: Record<string, any>;
  risk_score?: number;
  recommendations?: Record<string, any>;
  metadata?: Record<string, any>;
  created_at: string;
  program?: {
    id: string;
    name: string;
    program_type: string;
  };
  learner?: {
    id: string;
    full_name: string;
    nickname?: string;
    learner_code?: string;
  };
};

export type AttendanceReport = {
  id: string;
  account_id: string;
  report_name: string;
  report_type: 'standard' | 'custom' | 'automated' | 'scheduled';
  report_scope: 'account' | 'program' | 'learner' | 'instructor';
  scope_id?: string;
  date_range: {
    start_date: string;
    end_date: string;
    period?: string;
  };
  filters?: Record<string, any>;
  report_data?: Record<string, any>;
  charts_config?: Record<string, any>;
  status: 'pending' | 'generating' | 'completed' | 'failed' | 'expired';
  generated_at?: string;
  expires_at?: string;
  file_url?: string;
  share_token?: string;
  is_public: boolean;
  created_by?: string;
  created_at: string;
};

export type AutomationRule = {
  id: string;
  account_id: string;
  rule_name: string;
  rule_type: 'attendance' | 'behavior' | 'performance' | 'communication';
  trigger_conditions: Record<string, any>;
  trigger_frequency: 'immediate' | 'daily' | 'weekly' | 'monthly';
  actions: Record<string, any>;
  target_audience?: Record<string, any>;
  is_active: boolean;
  priority: number;
  last_triggered_at?: string;
  trigger_count: number;
  success_count: number;
  failure_count: number;
  metadata?: Record<string, any>;
  created_by?: string;
  created_at: string;
};

export type PredictiveInsight = {
  id: string;
  account_id: string;
  insight_type: 'attendance_risk' | 'performance_trend' | 'behavior_pattern' | 'engagement_level';
  target_type: 'learner' | 'program' | 'instructor' | 'account';
  target_id: string;
  prediction_data: Record<string, any>;
  confidence_score?: number;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  recommendations?: Record<string, any>;
  valid_until?: string;
  is_acknowledged: boolean;
  acknowledged_by?: string;
  acknowledged_at?: string;
  metadata?: Record<string, any>;
  created_at: string;
  target_learner?: {
    id: string;
    full_name: string;
    nickname?: string;
    learner_code?: string;
  };
  target_program?: {
    id: string;
    name: string;
    program_type: string;
  };
};

export async function loadAttendanceAnalytics(
  client: SupabaseClient<Database>,
  accountId: string,
  programId?: string,
  learnerId?: string,
  analysisType: 'daily' | 'weekly' | 'monthly' | 'term' = 'daily',
  limit = 30
): Promise<AttendanceAnalytics[]> {
  if (!accountId) {
    throw new Error('Account ID is required');
  }

  try {
    let query = client
      .from('attendance_analytics')
      .select(`
        *,
        programs!program_id (
          id,
          name,
          program_type
        ),
        learners!learner_id (
          id,
          full_name,
          nickname,
          learner_code
        )
      `)
      .eq('account_id', accountId)
      .eq('analysis_type', analysisType)
      .order('analysis_date', { ascending: false })
      .limit(limit);

    if (programId) {
      query = query.eq('program_id', programId);
    }

    if (learnerId) {
      query = query.eq('learner_id', learnerId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Supabase query error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        query: 'attendance_analytics.select',
        accountId,
        programId,
        learnerId,
      });
      throw error;
    }

    return (data || []).map((analytics) => ({
      id: analytics.id,
      account_id: analytics.account_id,
      program_id: analytics.program_id,
      learner_id: analytics.learner_id,
      analysis_date: analytics.analysis_date,
      analysis_type: analytics.analysis_type as 'daily' | 'weekly' | 'monthly' | 'term',
      attendance_rate: analytics.attendance_rate,
      total_sessions: analytics.total_sessions,
      present_sessions: analytics.present_sessions,
      absent_sessions: analytics.absent_sessions,
      late_sessions: analytics.late_sessions,
      excused_sessions: analytics.excused_sessions,
      streak_present: analytics.streak_present,
      streak_absent: analytics.streak_absent,
      patterns: analytics.patterns,
      risk_score: analytics.risk_score,
      recommendations: analytics.recommendations,
      metadata: analytics.metadata,
      created_at: analytics.created_at,
      program: analytics.programs ? {
        id: analytics.programs.id,
        name: analytics.programs.name,
        program_type: analytics.programs.program_type,
      } : undefined,
      learner: analytics.learners ? {
        id: analytics.learners.id,
        full_name: analytics.learners.full_name,
        nickname: analytics.learners.nickname,
        learner_code: analytics.learners.learner_code,
      } : undefined,
    }));
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to load attendance analytics:', {
      error: message,
      accountId,
      programId,
      learnerId,
      context: 'analytics.loader',
    });
    throw new Error(`Failed to load attendance analytics: ${message}`);
  }
}

export async function loadAttendanceReports(
  client: SupabaseClient<Database>,
  accountId: string,
  reportType?: 'standard' | 'custom' | 'automated' | 'scheduled',
  limit = 20
): Promise<AttendanceReport[]> {
  if (!accountId) {
    throw new Error('Account ID is required');
  }

  try {
    let query = client
      .from('attendance_reports')
      .select('*')
      .eq('account_id', accountId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (reportType) {
      query = query.eq('report_type', reportType);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Supabase query error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        query: 'attendance_reports.select',
        accountId,
        reportType,
      });
      throw error;
    }

    return (data || []).map((report) => ({
      id: report.id,
      account_id: report.account_id,
      report_name: report.report_name,
      report_type: report.report_type as 'standard' | 'custom' | 'automated' | 'scheduled',
      report_scope: report.report_scope as 'account' | 'program' | 'learner' | 'instructor',
      scope_id: report.scope_id,
      date_range: report.date_range,
      filters: report.filters,
      report_data: report.report_data,
      charts_config: report.charts_config,
      status: report.status as 'pending' | 'generating' | 'completed' | 'failed' | 'expired',
      generated_at: report.generated_at,
      expires_at: report.expires_at,
      file_url: report.file_url,
      share_token: report.share_token,
      is_public: report.is_public,
      created_by: report.created_by,
      created_at: report.created_at,
    }));
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to load attendance reports:', {
      error: message,
      accountId,
      reportType,
      context: 'analytics.loader',
    });
    throw new Error(`Failed to load attendance reports: ${message}`);
  }
}

export async function loadPredictiveInsights(
  client: SupabaseClient<Database>,
  accountId: string,
  targetType?: 'learner' | 'program' | 'instructor' | 'account',
  riskLevel?: 'low' | 'medium' | 'high' | 'critical',
  limit = 20
): Promise<PredictiveInsight[]> {
  if (!accountId) {
    throw new Error('Account ID is required');
  }

  try {
    let query = client
      .from('predictive_insights')
      .select(`
        *,
        learners!target_id (
          id,
          full_name,
          nickname,
          learner_code
        ),
        programs!target_id (
          id,
          name,
          program_type
        )
      `)
      .eq('account_id', accountId)
      .gte('valid_until', new Date().toISOString().split('T')[0])
      .order('created_at', { ascending: false })
      .limit(limit);

    if (targetType) {
      query = query.eq('target_type', targetType);
    }

    if (riskLevel) {
      query = query.eq('risk_level', riskLevel);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Supabase query error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        query: 'predictive_insights.select',
        accountId,
        targetType,
        riskLevel,
      });
      throw error;
    }

    return (data || []).map((insight) => ({
      id: insight.id,
      account_id: insight.account_id,
      insight_type: insight.insight_type as 'attendance_risk' | 'performance_trend' | 'behavior_pattern' | 'engagement_level',
      target_type: insight.target_type as 'learner' | 'program' | 'instructor' | 'account',
      target_id: insight.target_id,
      prediction_data: insight.prediction_data,
      confidence_score: insight.confidence_score,
      risk_level: insight.risk_level as 'low' | 'medium' | 'high' | 'critical',
      recommendations: insight.recommendations,
      valid_until: insight.valid_until,
      is_acknowledged: insight.is_acknowledged,
      acknowledged_by: insight.acknowledged_by,
      acknowledged_at: insight.acknowledged_at,
      metadata: insight.metadata,
      created_at: insight.created_at,
      target_learner: insight.target_type === 'learner' && insight.learners ? {
        id: insight.learners.id,
        full_name: insight.learners.full_name,
        nickname: insight.learners.nickname,
        learner_code: insight.learners.learner_code,
      } : undefined,
      target_program: insight.target_type === 'program' && insight.programs ? {
        id: insight.programs.id,
        name: insight.programs.name,
        program_type: insight.programs.program_type,
      } : undefined,
    }));
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to load predictive insights:', {
      error: message,
      accountId,
      targetType,
      riskLevel,
      context: 'analytics.loader',
    });
    throw new Error(`Failed to load predictive insights: ${message}`);
  }
}
