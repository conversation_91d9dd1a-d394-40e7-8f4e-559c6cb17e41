'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export interface BulkAttendanceUpdate {
  learner_id: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  check_in_time?: string;
  check_out_time?: string;
  notes?: string;
  metadata?: Record<string, any>;
}

export interface BulkUpdateResult {
  updatedCount: number;
  presentCount: number;
  absentCount: number;
  lateCount: number;
}

export async function bulkUpdateAttendance(
  sessionId: string,
  attendanceData: BulkAttendanceUpdate[]
): Promise<BulkUpdateResult> {
  const client = getSupabaseServerClient();

  try {
    const { data, error } = await client.rpc('bulk_update_attendance', {
      p_session_id: sessionId,
      p_attendance_data: attendanceData,
    });

    if (error) {
      console.error('Supabase RPC error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        function: 'bulk_update_attendance',
        sessionId,
        recordCount: attendanceData.length,
      });
      throw error;
    }

    if (!data.success) {
      throw new Error('Failed to update attendance');
    }

    return {
      updatedCount: data.updated_count,
      presentCount: data.present_count,
      absentCount: data.absent_count,
      lateCount: data.late_count,
    };
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to bulk update attendance:', {
      error: message,
      sessionId,
      recordCount: attendanceData.length,
      context: 'update-attendance',
    });
    throw new Error(`Failed to update attendance: ${message}`);
  }
}

export async function updateSingleAttendance(
  attendanceId: string,
  updates: {
    status?: 'present' | 'absent' | 'late' | 'excused';
    check_in_time?: string;
    check_out_time?: string;
    notes?: string;
    metadata?: Record<string, any>;
  }
): Promise<void> {
  const client = getSupabaseServerClient();

  try {
    const { error } = await client
      .from('attendance')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', attendanceId);

    if (error) {
      console.error('Supabase update error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        table: 'attendance',
        attendanceId,
        updates,
      });
      throw error;
    }
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to update attendance record:', {
      error: message,
      attendanceId,
      updates,
      context: 'update-attendance',
    });
    throw new Error(`Failed to update attendance record: ${message}`);
  }
}
