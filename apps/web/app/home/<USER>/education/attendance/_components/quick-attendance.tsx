'use client';

import {
  useCallback,
  useEffect,
  useMemo,
  useState,
  useTransition,
} from 'react';

import {
  AlertCircle,
  CheckCircle2,
  Clock,
  RotateCcw,
  Save,
  Search,
  UserCheck,
  UserX,
  Users,
  XCircle,
} from 'lucide-react';
import { toast } from 'sonner';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Separator } from '@kit/ui/separator';

import {
  type AttendanceRecord,
  type BulkAttendanceUpdate,
  bulkUpdateAttendance,
  createAttendanceSession,
  getAttendanceRecords,
} from '../_lib/server';

interface QuickAttendanceProps {
  accountId: string;
  programId: string;
  programName: string;
  sessionDate: string;
  onSave?: (stats: { present: number; absent: number; late: number }) => void;
}

export function QuickAttendance({
  accountId,
  programId,
  programName,
  sessionDate,
  onSave,
}: QuickAttendanceProps) {
  const [isPending, startTransition] = useTransition();

  const [attendanceRecords, setAttendanceRecords] = useState<
    AttendanceRecord[]
  >([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);

  // Auto-save timer
  const [autoSaveTimer, setAutoSaveTimer] = useState<NodeJS.Timeout | null>(
    null,
  );

  // Load attendance data
  useEffect(() => {
    if (!accountId || !programId) return;

    loadAttendanceData();
  }, [accountId, programId, sessionDate]);

  const loadAttendanceData = useCallback(async () => {
    try {
      setIsLoading(true);

      // Try to get existing attendance records
      let records = await getAttendanceRecords(
        accountId,
        programId,
        sessionDate,
      );

      // If no records exist, create a new session
      if (records.length === 0) {
        const sessionResult = await createAttendanceSession({
          accountId,
          programId,
          sessionDate,
          sessionType: 'regular',
        });

        setSessionId(sessionResult.sessionId);

        // Reload records after creating session
        records = await getAttendanceRecords(accountId, programId, sessionDate);
      }

      setAttendanceRecords(records);
    } catch (error) {
      console.error('Failed to load attendance data:', error);
      toast.error('Không thể tải dữ liệu điểm danh');
    } finally {
      setIsLoading(false);
    }
  }, [accountId, programId, sessionDate]);

  // Filter records based on search
  const filteredRecords = useMemo(() => {
    if (!searchQuery.trim()) return attendanceRecords;
    const query = searchQuery.toLowerCase();
    return attendanceRecords.filter(
      (record) =>
        record.learner?.full_name.toLowerCase().includes(query) ||
        record.learner?.nickname?.toLowerCase().includes(query) ||
        record.learner?.learner_code?.toLowerCase().includes(query),
    );
  }, [attendanceRecords, searchQuery]);

  // Calculate statistics
  const stats = useMemo(() => {
    const present = attendanceRecords.filter(
      (r) => r.status === 'present',
    ).length;
    const absent = attendanceRecords.filter(
      (r) => r.status === 'absent',
    ).length;
    const late = attendanceRecords.filter((r) => r.status === 'late').length;
    const excused = attendanceRecords.filter(
      (r) => r.status === 'excused',
    ).length;

    return { present, absent, late, excused, total: attendanceRecords.length };
  }, [attendanceRecords]);

  // Toggle attendance status
  const toggleAttendanceStatus = (recordId: string, currentStatus: string) => {
    const statusCycle = ['absent', 'present', 'late', 'excused'];
    const currentIndex = statusCycle.indexOf(currentStatus);
    const nextStatus = statusCycle[(currentIndex + 1) % statusCycle.length];

    updateAttendanceStatus(recordId, nextStatus as any);
  };

  // Update attendance status
  const updateAttendanceStatus = (
    recordId: string,
    status: 'present' | 'absent' | 'late' | 'excused',
  ) => {
    setAttendanceRecords((prev) =>
      prev.map((record) =>
        record.id === recordId
          ? {
              ...record,
              status,
              check_in_time:
                status === 'present' || status === 'late'
                  ? new Date().toISOString()
                  : undefined,
            }
          : record,
      ),
    );

    setHasChanges(true);
    scheduleAutoSave();
  };

  // Schedule auto-save
  const scheduleAutoSave = () => {
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer);
    }

    const timer = setTimeout(() => {
      saveAttendance(false);
    }, 3000); // Auto-save after 3 seconds of inactivity

    setAutoSaveTimer(timer);
  };

  // Save attendance
  const saveAttendance = async (showToast = true) => {
    if (!sessionId && attendanceRecords.length === 0) return;

    try {
      setIsSaving(true);

      const attendanceData: BulkAttendanceUpdate[] = attendanceRecords.map(
        (record) => ({
          learner_id: record.learner_id,
          status: record.status,
          check_in_time: record.check_in_time,
          check_out_time: record.check_out_time,
          notes: record.notes,
          metadata: record.metadata,
        }),
      );

      if (sessionId) {
        const result = await bulkUpdateAttendance(sessionId, attendanceData);

        if (showToast) {
          toast.success(
            `Đã lưu điểm danh: ${result.presentCount} có mặt, ${result.absentCount} vắng`,
          );
        }

        onSave?.(result);
      }

      setHasChanges(false);
    } catch (error) {
      console.error('Failed to save attendance:', error);
      if (showToast) {
        toast.error('Không thể lưu điểm danh');
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Bulk actions
  const markAllPresent = () => {
    setAttendanceRecords((prev) =>
      prev.map((record) => ({
        ...record,
        status: 'present' as const,
        check_in_time: new Date().toISOString(),
      })),
    );
    setHasChanges(true);
    scheduleAutoSave();
  };

  const markAllAbsent = () => {
    setAttendanceRecords((prev) =>
      prev.map((record) => ({
        ...record,
        status: 'absent' as const,
        check_in_time: undefined,
      })),
    );
    setHasChanges(true);
    scheduleAutoSave();
  };

  const resetAttendance = () => {
    setAttendanceRecords((prev) =>
      prev.map((record) => ({
        ...record,
        status: 'absent' as const,
        check_in_time: undefined,
        notes: undefined,
      })),
    );
    setHasChanges(true);
  };

  // Status styling
  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200';
      case 'late':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200';
      case 'excused':
        return 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200';
      default:
        return 'bg-red-100 text-red-800 border-red-200 hover:bg-red-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <CheckCircle2 className="h-4 w-4" />;
      case 'late':
        return <Clock className="h-4 w-4" />;
      case 'excused':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <XCircle className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'present':
        return 'Có mặt';
      case 'late':
        return 'Muộn';
      case 'excused':
        return 'Có phép';
      default:
        return 'Vắng';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
            <span className="ml-2">Đang tải dữ liệu điểm danh...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>{programName}</span>
            </div>
            <div className="text-muted-foreground text-sm">
              {new Date(sessionDate).toLocaleDateString('vi-VN')}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Statistics */}
          <div className="mb-6 grid grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <UserCheck className="h-4 w-4 text-green-600" />
              <span className="text-sm">Có mặt: </span>
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-800"
              >
                {stats.present}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <UserX className="h-4 w-4 text-red-600" />
              <span className="text-sm">Vắng: </span>
              <Badge variant="secondary" className="bg-red-100 text-red-800">
                {stats.absent}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-yellow-600" />
              <span className="text-sm">Muộn: </span>
              <Badge
                variant="secondary"
                className="bg-yellow-100 text-yellow-800"
              >
                {stats.late}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-4 w-4 text-blue-600" />
              <span className="text-sm">Có phép: </span>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                {stats.excused}
              </Badge>
            </div>
          </div>

          {/* Search and Actions */}
          <div className="mb-6 flex flex-col gap-4 sm:flex-row">
            <div className="relative flex-1">
              <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
              <Input
                placeholder="Tìm học sinh..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={markAllPresent}
                className="border-green-200 text-green-700 hover:bg-green-50"
              >
                Tất cả có mặt
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={markAllAbsent}
                className="border-red-200 text-red-700 hover:bg-red-50"
              >
                Tất cả vắng
              </Button>
              <Button variant="outline" size="sm" onClick={resetAttendance}>
                <RotateCcw className="mr-1 h-4 w-4" />
                Reset
              </Button>
            </div>
          </div>

          <Separator />
        </CardContent>
      </Card>

      {/* Student List */}
      <Card>
        <CardContent className="p-0">
          <div className="space-y-1">
            {filteredRecords.map((record, index) => (
              <div
                key={record.id}
                className="hover:bg-muted/50 flex items-center justify-between border-b p-4 last:border-b-0"
              >
                <div className="flex items-center space-x-3">
                  <div className="bg-muted flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <div className="font-medium">
                      {record.learner?.full_name}
                    </div>
                    <div className="text-muted-foreground text-sm">
                      {record.learner?.nickname &&
                        `${record.learner.nickname} • `}
                      {record.learner?.learner_code}
                    </div>
                  </div>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    toggleAttendanceStatus(record.id, record.status)
                  }
                  className={`min-w-[100px] ${getStatusStyle(record.status)}`}
                >
                  {getStatusIcon(record.status)}
                  <span className="ml-1">{getStatusText(record.status)}</span>
                </Button>
              </div>
            ))}
          </div>

          {filteredRecords.length === 0 && (
            <div className="text-muted-foreground p-8 text-center">
              {searchQuery
                ? 'Không tìm thấy học sinh nào'
                : 'Chưa có học sinh nào'}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex items-center justify-between">
        <div className="text-muted-foreground text-sm">
          {hasChanges && (
            <span className="text-orange-600">
              Có thay đổi chưa lưu • Tự động lưu sau 3 giây
            </span>
          )}
        </div>
        <Button
          onClick={() => saveAttendance(true)}
          disabled={isSaving || !hasChanges}
          className="min-w-[120px]"
        >
          {isSaving ? (
            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
          ) : (
            <Save className="mr-2 h-4 w-4" />
          )}
          {isSaving ? 'Đang lưu...' : 'Lưu điểm danh'}
        </Button>
      </div>
    </div>
  );
}
