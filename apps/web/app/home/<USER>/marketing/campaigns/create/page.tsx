import { notFound } from 'next/navigation';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';

import { CreateCampaignPage } from './_components/create-campaign-page';

interface PageProps {
  params: Promise<{
    account: string;
  }>;
  searchParams: {
    segment?: string;
    type?: string;
  };
}

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('marketing:campaigns.create.title');

  return {
    title,
  };
};

export default async function CreateCampaignRoute({
  params,
  searchParams,
}: PageProps) {
  const client = getSupabaseServerClient();

  // Await params first
  const { account: accountSlug } = await params;

  // Load team workspace data
  const { user, account } = await loadTeamWorkspace(accountSlug);

  // Get segments for selection
  const { data: segments } = await client
    .from('customer_segments')
    .select('*')
    .eq('account_id', account.id)
    .eq('is_active', true)
    .order('created_at', { ascending: false });

  // Get pre-selected segment if provided
  let preSelectedSegment = null;
  if (searchParams.segment) {
    const { data: segment } = await client
      .from('customer_segments')
      .select('*')
      .eq('id', searchParams.segment)
      .eq('account_id', account.id)
      .single();

    preSelectedSegment = segment;
  }

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={accountSlug}
        title={<Trans i18nKey="marketing:campaigns.create.title" />}
        description={
          <AppBreadcrumbs
            items={[
              {
                title: <Trans i18nKey="common:routes.home" />,
                url: `/home/<USER>
              },
              {
                title: <Trans i18nKey="marketing:title" />,
                url: `/home/<USER>/marketing`,
              },
              {
                title: <Trans i18nKey="marketing:campaigns.title" />,
                url: `/home/<USER>/marketing/campaigns`,
              },
              {
                title: <Trans i18nKey="marketing:campaigns.create.title" />,
              },
            ]}
          />
        }
      />

      <CreateCampaignPage
        account={account}
        accountSlug={accountSlug}
        segments={segments || []}
        preSelectedSegment={preSelectedSegment}
        preSelectedType={searchParams.type}
      />
    </>
  );
}
