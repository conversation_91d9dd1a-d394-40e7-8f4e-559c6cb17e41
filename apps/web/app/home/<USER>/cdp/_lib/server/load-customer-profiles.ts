import { SupabaseClient } from '@supabase/supabase-js';

import { Database } from '@kit/supabase/database.types';

export interface CustomerProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  avatar_url?: string;
  created_at: string;
  last_active_at?: string;
  total_orders: number;
  total_spent: number;
  avg_order_value: number;
  engagement_score: number;
  churn_risk_score: number;
  value_tier: 'high' | 'medium' | 'low';
  tags: string[];
  metadata: Record<string, any>;
}

export interface CustomerProfilesResult {
  profiles: CustomerProfile[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export async function loadCustomerProfiles(
  client: SupabaseClient<Database>,
  accountId: string,
  page: number = 1,
  searchQuery: string = '',
  filter: string = 'all',
  limit: number = 50,
): Promise<CustomerProfilesResult> {
  try {
    // Use the optimized database function for better performance
    // This leverages the customer_profiles_view and optimized indexes
    const { data: profilesData, error: profilesError } = await client
      .rpc('load_customer_profiles_v2', {
        p_team_account_id: accountId,
        p_search_query: searchQuery,
        p_filter: filter,
        p_page: page,
        p_limit: limit,
      });

    if (profilesError) {
      console.error('Error loading customer profiles:', profilesError);
      throw profilesError;
    }

    if (!profilesData || profilesData.length === 0) {
      return {
        profiles: [],
        total: 0,
        page,
        limit,
        totalPages: 0,
      };
    }

    // Get total count from first row (all rows have the same total_count)
    const totalCount = profilesData[0]?.total_count || 0;

    // Transform database results to CustomerProfile format
    let allProfiles: CustomerProfile[] = profilesData.map((row) => {
      const totalSpent = parseFloat(row.total_spent || '0');
      const totalOrders = row.total_orders || 0;

      return {
        id: row.id,
        email: row.email || '',
        first_name: row.first_name || '',
        last_name: row.last_name || '',
        phone: row.phone || null,
        avatar_url: row.avatar_url || null,
        created_at: row.created_at || new Date().toISOString(),
        last_active_at: row.last_active_at || null,
        total_orders: totalOrders,
        total_spent: totalSpent,
        avg_order_value: totalOrders > 0 ? totalSpent / totalOrders : 0,
        engagement_score: parseFloat(row.engagement_score || '0.5'),
        churn_risk_score: parseFloat(row.churn_risk_score || '0.1'),
        value_tier: row.value_tier || 'low',
        tags: ['customer'],
        metadata: {},
      };
    });



    return {
      profiles: allProfiles,
      total: totalCount,
      page,
      limit,
      totalPages: Math.ceil(totalCount / limit),
    };
  } catch (error) {
    console.error('Error loading customer profiles:', error);
    throw new Error(
      `Failed to load customer profiles: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}

function calculateValueTier(totalSpent: number): 'high' | 'medium' | 'low' {
  if (totalSpent > 5000000) return 'high'; // 5M VND
  if (totalSpent > 1000000) return 'medium'; // 1M VND
  return 'low';
}
