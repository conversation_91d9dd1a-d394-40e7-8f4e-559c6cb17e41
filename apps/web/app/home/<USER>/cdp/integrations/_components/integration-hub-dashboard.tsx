'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import {
  Plug,
  Mail,
  Users,
  BarChart3,
  ShoppingCart,
  Share2,
  Megaphone,
  RefreshCw,
  Plus,
  Settings,
  Activity,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Download
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

interface IntegrationHubDashboardProps {
  accountId: string;
}

interface Integration {
  config: {
    id: string;
    name: string;
    type: string;
    provider: string;
    enabled: boolean;
    created_at: string;
  };
  status: {
    connected: boolean;
    last_sync: string | null;
    sync_status: string;
    records_synced: number;
    health_score: number;
  };
}

export function IntegrationHubDashboard({ accountId }: IntegrationHubDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const supabase = useSupabase();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [availableIntegrations, setAvailableIntegrations] = useState<any>({});
  const [healthData, setHealthData] = useState<any[]>([]);

  // Load integration data
  const loadIntegrationData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load existing integrations from database
      const { data: integrationsData, error: integrationsError } = await supabase
        .from('integration_statuses')
        .select('*')
        .eq('account_id', accountId);

      if (integrationsError) {
        throw integrationsError;
      }

      // Transform data to match expected format
      const transformedIntegrations = (integrationsData || []).map(integration => ({
        config: {
          id: integration.id,
          name: integration.name,
          type: integration.category,
          provider: integration.provider,
          enabled: integration.status === 'connected',
          created_at: integration.created_at
        },
        status: {
          connected: integration.status === 'connected',
          last_sync: integration.last_sync,
          sync_status: integration.status,
          records_synced: integration.records_synced || 0,
          health_score: Math.round((integration.health_score || 0) * 100)
        }
      }));

      setIntegrations(transformedIntegrations);

      // Set mock available integrations (this would come from a config or API)
      setAvailableIntegrations({
        mailchimp: { name: 'Mailchimp', type: 'email', description: 'Email marketing platform' },
        salesforce: { name: 'Salesforce', type: 'crm', description: 'Customer relationship management' },
        shopify: { name: 'Shopify', type: 'ecommerce', description: 'E-commerce platform' }
      });

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load integration data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadIntegrationData();
  }, []);

  const getIntegrationIcon = (type: string) => {
    switch (type) {
      case 'email': return <Mail className="h-5 w-5" />;
      case 'crm': return <Users className="h-5 w-5" />;
      case 'analytics': return <BarChart3 className="h-5 w-5" />;
      case 'ecommerce': return <ShoppingCart className="h-5 w-5" />;
      case 'social': return <Share2 className="h-5 w-5" />;
      case 'advertising': return <Megaphone className="h-5 w-5" />;
      default: return <Plug className="h-5 w-5" />;
    }
  };

  const getStatusIcon = (status: string, connected: boolean) => {
    if (!connected) return <XCircle className="h-4 w-4 text-red-500" />;

    switch (status) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'syncing': return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'idle': return <Clock className="h-4 w-4 text-gray-500" />;
      default: return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getHealthBadge = (healthScore: number) => {
    if (healthScore >= 80) return <Badge variant="default" className="bg-green-500">Healthy</Badge>;
    if (healthScore >= 60) return <Badge variant="secondary">Warning</Badge>;
    return <Badge variant="destructive">Error</Badge>;
  };

  const handleSync = async (integrationId: string) => {
    try {
      // Update last_sync timestamp in database
      const { error } = await supabase
        .from('integration_statuses')
        .update({
          last_sync: new Date().toISOString(),
          status: 'connected'
        })
        .eq('id', integrationId)
        .eq('account_id', accountId);

      if (error) {
        throw error;
      }

      await loadIntegrationData(); // Refresh data
    } catch (error) {
      console.error('Sync failed:', error);
      setError('Failed to sync integration');
    }
  };

  return (
    <div className="space-y-6">
      {/* Hero Header */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-teal-600 via-blue-600 to-purple-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">Integration Hub</h1>
              <p className="text-teal-100 text-lg">
                Connect and manage third-party integrations for your CDP
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="secondary" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export Config
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={loadIntegrationData}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button variant="secondary" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Integration
              </Button>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-4 right-4 opacity-20">
          <Plug className="h-32 w-32" />
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Overview Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Active Integrations</p>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                  {integrations.filter(i => i.config.enabled).length}
                </p>
              </div>
              <div className="p-3 bg-blue-500 rounded-xl">
                <Plug className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <span className="text-blue-600">
                {integrations.length} total configured
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Connected</p>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                  {integrations.filter(i => i.status.connected).length}
                </p>
              </div>
              <div className="p-3 bg-green-500 rounded-xl">
                <Activity className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <span className="text-green-600">
                Successfully connected
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Records Synced</p>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                  {integrations.reduce((sum, i) => sum + i.status.records_synced, 0).toLocaleString()}
                </p>
              </div>
              <div className="p-3 bg-purple-500 rounded-xl">
                <RefreshCw className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <span className="text-purple-600">
                Total records processed
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 dark:text-orange-400">Health Score</p>
                <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">
                  {integrations.length > 0
                    ? Math.round(integrations.reduce((sum, i) => sum + i.status.health_score, 0) / integrations.length)
                    : 0
                  }%
                </p>
              </div>
              <div className="p-3 bg-orange-500 rounded-xl">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <span className="text-orange-600">
                Average health score
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Integration Hub Tabs */}
      <Tabs defaultValue="active" className="space-y-4">
        <TabsList>
          <TabsTrigger value="active">Active Integrations</TabsTrigger>
          <TabsTrigger value="available">Available</TabsTrigger>
          <TabsTrigger value="health">Health Monitor</TabsTrigger>
          <TabsTrigger value="schedules">Sync Schedules</TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plug className="h-5 w-5" />
                Active Integrations
              </CardTitle>
              <CardDescription>
                Manage your connected third-party integrations
              </CardDescription>
            </CardHeader>
            <CardContent>
              {integrations.length > 0 ? (
                <div className="space-y-4">
                  {integrations.map((integration) => (
                    <div key={integration.config.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {getIntegrationIcon(integration.config.type)}
                          <div>
                            <h4 className="font-medium">{integration.config.name}</h4>
                            <p className="text-sm text-muted-foreground capitalize">
                              {integration.config.provider} • {integration.config.type}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(integration.status.sync_status, integration.status.connected)}
                            <span className="text-sm capitalize">{integration.status.sync_status}</span>
                          </div>

                          {getHealthBadge(integration.status.health_score)}

                          <div className="flex items-center gap-1">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSync(integration.config.id)}
                            >
                              <RefreshCw className="h-3 w-3 mr-1" />
                              Sync
                            </Button>
                            <Button variant="outline" size="sm">
                              <Settings className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>

                      <div className="mt-3 grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Records Synced:</span>
                          <div className="font-medium">{integration.status.records_synced.toLocaleString()}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Last Sync:</span>
                          <div className="font-medium">
                            {integration.status.last_sync
                              ? new Date(integration.status.last_sync).toLocaleDateString()
                              : 'Never'
                            }
                          </div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Status:</span>
                          <div className="font-medium">
                            {integration.config.enabled ? 'Enabled' : 'Disabled'}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Plug className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>No integrations configured</p>
                  <Button className="mt-4">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Your First Integration
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="available" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Available Integrations
              </CardTitle>
              <CardDescription>
                Connect new third-party services to your CDP
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {Object.entries(availableIntegrations).map(([key, integration]: [string, any]) => (
                  <div key={key} className="border rounded-lg p-4">
                    <div className="flex items-center gap-3 mb-3">
                      {getIntegrationIcon(integration.type)}
                      <div>
                        <h4 className="font-medium">{integration.name}</h4>
                        <p className="text-sm text-muted-foreground capitalize">
                          {integration.type}
                        </p>
                      </div>
                    </div>

                    <p className="text-sm text-muted-foreground mb-4">
                      {integration.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {integration.webhook_supported && (
                          <Badge variant="outline" className="text-xs">Webhooks</Badge>
                        )}
                        <Badge variant="outline" className="text-xs">
                          {integration.supported_sync_directions.join(', ')}
                        </Badge>
                      </div>

                      <Button size="sm">
                        Connect
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="health" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Health Monitor
              </CardTitle>
              <CardDescription>
                Monitor integration health and performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {integrations.map((integration) => (
                  <div key={integration.config.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        {getIntegrationIcon(integration.config.type)}
                        <span className="font-medium">{integration.config.name}</span>
                      </div>
                      {getHealthBadge(integration.status.health_score)}
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Health Score</span>
                        <div className="font-medium">{integration.status.health_score}%</div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Connection</span>
                        <div className="font-medium">
                          {integration.status.connected ? 'Connected' : 'Disconnected'}
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Sync Status</span>
                        <div className="font-medium capitalize">{integration.status.sync_status}</div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Records</span>
                        <div className="font-medium">{integration.status.records_synced.toLocaleString()}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedules" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Sync Schedules
              </CardTitle>
              <CardDescription>
                Manage automatic synchronization schedules
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {integrations.map((integration) => (
                  <div key={integration.config.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getIntegrationIcon(integration.config.type)}
                        <div>
                          <h4 className="font-medium">{integration.config.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            Frequency: Daily • Direction: Import
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="outline">Enabled</Badge>
                        <Button variant="outline" size="sm">
                          <Settings className="h-3 w-3 mr-1" />
                          Configure
                        </Button>
                      </div>
                    </div>

                    <div className="mt-3 grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Last Run:</span>
                        <div className="font-medium">
                          {integration.status.last_sync
                            ? new Date(integration.status.last_sync).toLocaleString()
                            : 'Never'
                          }
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Next Run:</span>
                        <div className="font-medium">In 2 hours</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
