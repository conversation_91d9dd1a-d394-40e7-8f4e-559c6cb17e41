'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@kit/ui/dialog';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Textarea } from '@kit/ui/textarea';
import { Badge } from '@kit/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import {
  GitBranch,
  Users,
  Calendar,
  Zap,
  Mail,
  MessageSquare,
  Bell,
  Loader2,
} from 'lucide-react';

const createJourneySchema = z.object({
  name: z.string().min(1, 'Journey name is required'),
  description: z.string().min(1, 'Description is required'),
  trigger_type: z.enum(['segment_entry', 'event', 'date', 'manual']),
  trigger_config: z.object({
    segment_id: z.string().optional(),
    event_name: z.string().optional(),
    date_time: z.string().optional(),
  }),
  steps: z.array(z.object({
    type: z.enum(['email', 'sms', 'push', 'wait', 'condition']),
    name: z.string(),
    config: z.record(z.any()),
    delay_hours: z.number().optional(),
  })).min(1, 'At least one step is required'),
});

type CreateJourneyFormData = z.infer<typeof createJourneySchema>;

interface Journey {
  id: string;
  name: string;
  description: string;
  status: string;
  trigger: string;
  trigger_type?: string;
  steps: any[] | number; // Can be array of steps or just count
  participants: number;
  completion_rate: number;
  created_at: string;
  updated_at: string;
  tags: string[];
}

interface CreateJourneyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateJourneyFormData) => Promise<void>;
  journey?: Journey | null; // For editing
}

export function CreateJourneyModal({
  isOpen,
  onClose,
  onSubmit,
  journey,
}: CreateJourneyModalProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditing = !!journey;

  const form = useForm<CreateJourneyFormData>({
    resolver: zodResolver(createJourneySchema),
    defaultValues: {
      name: '',
      description: '',
      trigger_type: 'segment_entry',
      trigger_config: {},
      steps: [
        {
          type: 'email',
          name: 'Welcome Email',
          config: {
            subject: 'Welcome to our platform!',
            template: 'welcome_email'
          },
          delay_hours: 0
        }
      ],
    },
  });

  // Reset form when journey data changes
  useEffect(() => {
    if (journey) {
      // Editing mode - populate form with journey data
      const existingSteps = Array.isArray((journey as any).steps) && (journey as any).steps.length > 0
        ? (journey as any).steps
        : [
            {
              type: 'email',
              name: 'Welcome Email',
              config: {
                subject: 'Welcome to our platform!',
                template: 'welcome_email'
              },
              delay_hours: 0
            }
          ];

      form.reset({
        name: journey.name || '',
        description: journey.description || '',
        trigger_type: (journey.trigger_type || journey.trigger || 'segment_entry') as any,
        trigger_config: {},
        steps: existingSteps,
      });
    } else {
      // Creating mode - reset to default values
      form.reset({
        name: '',
        description: '',
        trigger_type: 'segment_entry',
        trigger_config: {},
        steps: [
          {
            type: 'email',
            name: 'Welcome Email',
            config: {
              subject: 'Welcome to our platform!',
              template: 'welcome_email'
            },
            delay_hours: 0
          }
        ],
      });
    }
  }, [journey, form]);

  const handleSubmit = async (data: CreateJourneyFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
      form.reset();
      onClose();
    } catch (error) {
      console.error('Error creating journey:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const triggerTypes = [
    {
      value: 'segment_entry',
      label: t('cdp:journeys.triggers.segmentEntry'),
      description: t('cdp:journeys.triggers.segmentEntryDesc'),
      icon: <Users className="h-5 w-5" />,
    },
    {
      value: 'event',
      label: t('cdp:journeys.triggers.event'),
      description: t('cdp:journeys.triggers.eventDesc'),
      icon: <Zap className="h-5 w-5" />,
    },
    {
      value: 'date',
      label: t('cdp:journeys.triggers.date'),
      description: t('cdp:journeys.triggers.dateDesc'),
      icon: <Calendar className="h-5 w-5" />,
    },
    {
      value: 'manual',
      label: t('cdp:journeys.triggers.manual'),
      description: t('cdp:journeys.triggers.manualDesc'),
      icon: <GitBranch className="h-5 w-5" />,
    },
  ];

  const stepTypes = [
    {
      value: 'email',
      label: t('cdp:journeys.stepTypes.email'),
      icon: <Mail className="h-4 w-4" />,
    },
    {
      value: 'sms',
      label: t('cdp:journeys.stepTypes.sms'),
      icon: <MessageSquare className="h-4 w-4" />,
    },
    {
      value: 'push',
      label: t('cdp:journeys.stepTypes.push'),
      icon: <Bell className="h-4 w-4" />,
    },
  ];

  const selectedTrigger = form.watch('trigger_type');

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            {isEditing
              ? t('cdp:journeys.editJourney', 'Edit Journey')
              : t('cdp:journeys.createJourney.title')
            }
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? t('cdp:journeys.editDescription', 'Update your customer journey details')
              : t('cdp:journeys.createJourney.description')
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t('cdp:journeys.createJourney.basicInfo')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('cdp:journeys.name')}</FormLabel>
                      <FormControl>
                        <Input placeholder={t('cdp:journeys.namePlaceholder')} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('cdp:journeys.description')}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t('cdp:journeys.descriptionPlaceholder')}
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Trigger Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t('cdp:journeys.createJourney.triggerConfig')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="trigger_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('cdp:journeys.triggerType')}</FormLabel>
                      <FormControl>
                        <div className="grid gap-3 md:grid-cols-2">
                          {triggerTypes.map((trigger) => (
                            <div
                              key={trigger.value}
                              className={`cursor-pointer rounded-lg border p-4 transition-all hover:border-primary ${
                                field.value === trigger.value
                                  ? 'border-primary bg-primary/5'
                                  : 'border-border'
                              }`}
                              onClick={() => field.onChange(trigger.value)}
                            >
                              <div className="flex items-start gap-3">
                                <div className={`p-2 rounded-lg ${
                                  field.value === trigger.value
                                    ? 'bg-primary text-primary-foreground'
                                    : 'bg-muted'
                                }`}>
                                  {trigger.icon}
                                </div>
                                <div className="flex-1">
                                  <h4 className="font-medium">{trigger.label}</h4>
                                  <p className="text-sm text-muted-foreground mt-1">
                                    {trigger.description}
                                  </p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Trigger-specific configuration */}
                {selectedTrigger === 'segment_entry' && (
                  <div className="mt-4">
                    <FormLabel>{t('cdp:journeys.selectSegment')}</FormLabel>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder={t('cdp:journeys.selectSegmentPlaceholder')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high_value">{t('cdp:journeys.segments.highValue')}</SelectItem>
                        <SelectItem value="new_customers">{t('cdp:journeys.segments.newCustomers')}</SelectItem>
                        <SelectItem value="churn_risk">{t('cdp:journeys.segments.churnRisk')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {selectedTrigger === 'event' && (
                  <div className="mt-4">
                    <FormLabel>{t('cdp:journeys.eventName')}</FormLabel>
                    <Input placeholder={t('cdp:journeys.eventNamePlaceholder')} />
                  </div>
                )}

                {selectedTrigger === 'date' && (
                  <div className="mt-4">
                    <FormLabel>{t('cdp:journeys.scheduleDate')}</FormLabel>
                    <Input type="datetime-local" />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Journey Steps Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {isEditing
                    ? t('cdp:journeys.editJourneyStepsPreview', 'Journey Steps')
                    : t('cdp:journeys.createJourney.stepsPreview')
                  }
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {form.watch('steps').map((step, index) => (
                    <div key={index} className="flex items-center gap-4 p-4 border rounded-lg">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500 text-white text-sm font-medium">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          {stepTypes.find(t => t.value === step.type)?.icon}
                          <span className="font-medium">{step.name}</span>
                          <Badge variant="outline">
                            {stepTypes.find(t => t.value === step.type)?.label}
                          </Badge>
                        </div>
                        {step.delay_hours && step.delay_hours > 0 && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {t('cdp:journeys.delayHours', { hours: step.delay_hours })}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                {!isEditing && (
                  <div className="mt-4 p-4 bg-muted rounded-lg">
                    <p className="text-sm text-muted-foreground">
                      {t('cdp:journeys.createJourney.stepsNote')}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                {t('common:cancel')}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing
                  ? t('common:edit', 'Update')
                  : t('cdp:journeys.createJourney.submit')
                }
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
