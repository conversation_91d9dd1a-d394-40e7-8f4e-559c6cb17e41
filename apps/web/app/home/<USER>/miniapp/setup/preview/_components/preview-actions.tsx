'use client';

import { useCallback, useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

import { publishTheme } from '~/home/<USER>/miniapp/setup/_lib/server/publish.actions';

interface PreviewActionsProps {
  account: string;
  themeId: string;
  tempThemeId?: string;
  qrCodeUrl?: string;
  shortLink?: string;
}

export function PreviewActions({
  account,
  themeId,
  tempThemeId,
  qrCodeUrl,
  shortLink,
}: PreviewActionsProps) {
  const router = useRouter();
  const [currentDate, setCurrentDate] = useState<string>('');

  // Set date on client side to avoid hydration mismatch
  useEffect(() => {
    setCurrentDate(
      new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      }),
    );
  }, []);

  const handlePublish = useCallback(async () => {
    try {
      await publishTheme(themeId, account);
      toast.success('MiniApp published successfully');
      router.push(`/home/<USER>/miniapp`);
    } catch (error) {
      console.error('Error publishing theme:', error);
      toast.error('Failed to publish MiniApp');
    }
  }, [account, themeId, router]);

  const handleBack = useCallback(() => {
    const backUrl = tempThemeId
      ? `/home/<USER>/miniapp/setup/customization?editThemeId=${themeId}&tempThemeId=${tempThemeId}`
      : `/home/<USER>/miniapp/setup/customization?editThemeId=${themeId}`;
    router.push(backUrl);
  }, [account, themeId, tempThemeId, router]);

  return (
    <div className="space-y-4">
      {/* Back to Edit */}
      <Button
        variant="outline"
        onClick={handleBack}
        className="w-full"
        size="lg"
      >
        <Trans i18nKey="common:back">Back to Edit</Trans>
      </Button>

      {/* Publish Button */}
      <Button onClick={handlePublish} className="w-full" size="lg">
        <Trans i18nKey="miniapp:setup:publish">Publish MiniApp</Trans>
      </Button>

      {/* Info Section */}
      <div className="mt-6 border-t border-gray-200 pt-6">
        <h3 className="mb-3 text-sm font-medium text-gray-900">Preview Info</h3>
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex justify-between">
            <span>Status:</span>
            <span className="font-medium text-yellow-600">Draft</span>
          </div>
          <div className="flex justify-between">
            <span>Last updated:</span>
            <span>{currentDate || 'Loading...'}</span>
          </div>
        </div>
      </div>

      {/* QR Code Section */}
      <div className="mt-6 border-t border-gray-200 pt-6">
        <h3 className="mb-3 text-sm font-medium text-gray-900">
          📱 Test on Zalo
        </h3>
        {qrCodeUrl ? (
          <div className="space-y-3 text-center">
            <div className="mx-auto h-32 w-32 rounded-lg border bg-white p-2">
              <img
                src={qrCodeUrl}
                alt="Preview QR Code"
                className="h-full w-full"
                style={{ imageRendering: 'pixelated' }}
              />
            </div>
            <div>
              <p className="mb-1 text-xs font-medium text-gray-900">
                Scan with Zalo app
              </p>
              <p className="text-xs text-gray-500">
                Open Zalo and scan to test your Mini App
              </p>
            </div>
            {shortLink && (
              <div className="mt-2">
                <a
                  href={shortLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-blue-600 underline hover:text-blue-800"
                >
                  Open direct link
                </a>
              </div>
            )}
          </div>
        ) : (
          <div className="py-4 text-center">
            <div className="text-xs text-gray-500">QR code not available</div>
          </div>
        )}
      </div>

      {/* Tips */}
      <div className="mt-6 rounded-lg bg-blue-50 p-4">
        <h4 className="mb-2 text-sm font-medium text-blue-900">💡 Tips</h4>
        <ul className="space-y-1 text-xs text-blue-800">
          <li>• Test your Mini App on different devices</li>
          <li>• Check all interactive elements work properly</li>
          <li>• Verify the theme looks good in Zalo app</li>
        </ul>
      </div>
    </div>
  );
}
