# Theme Templates Feature

T<PERSON><PERSON> năng Template Marketing cho Education SaaS - gi<PERSON><PERSON> thu hút phụ huynh đăng ký cho con em.

## 🎯 Mục tiêu

Tạo ra các template JSON seed data dựa trên ZMP_PUCK_CONFIG components để:
- Thu hút phụ huynh quan tâm đến giáo dục con em
- Tăng tỷ lệ chuyển đổi từ visitor thành khách hàng
- Cung cấp giao diện đẹp và chuyên nghiệp
- D<PERSON> dàng tùy chỉnh theo nhu cầu từng trung tâm

## 🏗️ Kiến trúc

### 1. Marketing Templates (`marketing-templates.ts`)
```typescript
export const MARKETING_TEMPLATES = [
  {
    id: 'education-hero-landing',
    name: 'Hero Landing - Giáo Dục', 
    description: 'Trang chủ thu hút phụ huynh...',
    category: 'landing',
    data: {
      content: [...], // Puck data structure
      root: {...},    // Theme configuration
      zones: {}       // Puck zones
    }
  }
]
```

### 2. Theme Selector Modal (`theme-selector-modal.tsx`)
- Modal hiển thị danh sách templates
- Preview template trước khi apply
- Phân trang và filter theo category
- Tích hợp với database và fallback

### 3. Server Actions (`theme-templates.actions.ts`)
- `loadThemeTemplates()` - Load từ database với fallback
- `seedMarketingTemplates()` - Seed templates vào DB
- `getTemplateCategories()` - Lấy danh sách categories

### 4. Database Integration
- Migration seed templates vào `themes` table
- RLS policies cho security
- Indexes cho performance

## 📱 Components Sử Dụng

Chỉ sử dụng components có sẵn trong ZMP_PUCK_CONFIG (không bao gồm templates category):

### Layout Components
- `Grid` - Layout dạng lưới
- `Flex` - Layout flexbox

### Display Components  
- `Avatar` - Hình đại diện
- `Badge` - Nhãn/tag
- `Card` - Thẻ nội dung
- `List` - Danh sách
- `Swiper` - Slider/carousel
- `Progress` - Thanh tiến độ

### Typography
- `Text` - Văn bản với nhiều style

### Form Components
- `Button` - Nút bấm
- `Input` - Ô nhập liệu
- `Select` - Dropdown
- `TextArea` - Ô nhập nhiều dòng
- `Checkbox`, `Radio`, `Switch` - Controls

### Overlay
- `Modal`, `Sheet` - Popup/overlay

## 🎨 Template Categories

### 1. Landing (`landing`)
- Hero sections thu hút
- Giới thiệu trung tâm
- Call-to-action mạnh mẽ

### 2. Programs (`programs`) 
- Showcase chương trình học
- Thông tin chi tiết khóa học
- Giá cả và ưu đãi

### 3. Testimonials (`testimonials`)
- Phản hồi phụ huynh
- Social proof
- Thống kê thành tích

### 4. Contact (`contact`)
- Form đăng ký tư vấn
- Thu thập lead
- Thông tin liên hệ

## 🚀 Cách sử dụng

### 1. Seed Templates (Admin)
```bash
# Truy cập admin page
/home/<USER>/miniapp/setup/customization/admin

# Click "Seed Templates" để thêm vào database
```

### 2. Sử dụng trong Theme Builder
```typescript
// Trong customization page
const headerActions = [
  {
    type: 'button',
    label: 'Templates', 
    icon: '🎨',
    onClick: () => setIsThemeSelectorOpen(true)
  }
]

// Modal selector
<ThemeSelectorModal
  isOpen={isThemeSelectorOpen}
  onClose={() => setIsThemeSelectorOpen(false)}
  onSelectTemplate={handleSelectTemplate}
/>
```

### 3. Apply Template
- Click nút "Templates" trong ThemeBuilder header
- Browse và preview templates
- Click "Chọn" để apply template
- Template data sẽ replace current puckData

## 🔧 Technical Details

### Puck Data Structure
```json
{
  "content": [
    {
      "type": "ComponentName",
      "props": {
        "id": "unique-id",
        "...": "component props"
      },
      "slots": {
        "items": [...]  // Nested components
      }
    }
  ],
  "root": {
    "props": {
      "title": "Page title",
      "backgroundColor": "#ffffff",
      "primaryColor": "#2563eb"
    }
  },
  "zones": {}
}
```

### Database Schema
```sql
-- themes table
id uuid PRIMARY KEY
name text NOT NULL
description text
category text
config jsonb NOT NULL  -- Puck data
type theme_type NOT NULL
thumbnail_url text
```

## 🎯 Marketing Strategy

### Target Audience: Phụ huynh có con từ 3-15 tuổi

### Key Messages:
- **Chất lượng giáo dục**: Đội ngũ giáo viên chuyên nghiệp
- **Phát triển toàn diện**: Không chỉ học thuật mà còn kỹ năng sống
- **Môi trường an toàn**: Cơ sở vật chất hiện đại, an toàn
- **Theo dõi tiến độ**: Báo cáo chi tiết cho phụ huynh
- **Ưu đãi hấp dẫn**: Giảm giá, học thử miễn phí

### Conversion Funnel:
1. **Awareness**: Hero landing với thông điệp mạnh
2. **Interest**: Showcase chương trình học
3. **Consideration**: Testimonials và social proof  
4. **Action**: Form đăng ký tư vấn

## 📊 Metrics & Analytics

### Tracking Points:
- Template usage frequency
- Conversion rate by template
- User engagement with different components
- A/B testing results

### Success Metrics:
- Increased lead generation
- Higher conversion rates
- Better user engagement
- Reduced bounce rate

## 🔮 Future Enhancements

1. **AI-powered templates**: Tự động tạo template dựa trên industry
2. **A/B testing**: So sánh hiệu quả các templates
3. **Analytics dashboard**: Theo dõi performance
4. **Custom template builder**: Cho phép user tạo template riêng
5. **Template marketplace**: Chia sẻ và bán templates

## 🛠️ Development Notes

- Templates follow Puck data model strictly
- All components must exist in ZMP_PUCK_CONFIG
- Fallback to static data if database unavailable
- Responsive design for mobile-first
- Optimized for education sector specifically
- Vietnamese content for local market
