'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';

import { useRouter } from 'next/navigation';

import { debounce } from 'lodash';
import { X } from 'lucide-react';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { HeaderAction, ThemeBuilder } from '@kit/theme-builder';
import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Input } from '@kit/ui/input';
import { toast } from '@kit/ui/sonner';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@kit/ui/tabs';

import { ThemeSelectorModal } from './_components/theme-selector-modal';
import { EMPTY_PUCK_DATA } from './_lib/constants/theme-defaults';
import {
  createOrUpdateTempTheme,
  getInitialTheme,
} from './_lib/server/temp-theme.actions';
import { PuckData, ThemeConfig } from './_lib/types/theme-config.types';

interface CustomizationPageProps {
  params: Promise<{ account: string }>;
  searchParams: Promise<{
    themeId?: string;
    editThemeId?: string;
    tempThemeId?: string;
  }>;
}

// Simple error boundary component
class ThemeBuilderErrorBoundary extends React.Component<
  { children: React.ReactNode; onError?: (error: Error) => void },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('ThemeBuilder error:', error, errorInfo);
    this.props.onError?.(error);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex h-full items-center justify-center">
          <div className="text-center">
            <p className="mb-4 text-red-500">Theme editor failed to load</p>
            <button
              onClick={() => {
                this.setState({ hasError: false, error: undefined });
                window.location.reload();
              }}
              className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default function CustomizationPage({
  params,
  searchParams,
}: CustomizationPageProps) {
  const router = useRouter();
  const { account } = useTeamAccountWorkspace();

  // State
  const [puckData, setPuckData] = useState<PuckData>(EMPTY_PUCK_DATA);
  const [tempTheme, setTempTheme] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // QR Code state
  const [previewQrCode, setPreviewQrCode] = useState<string | null>(null);
  const [devQrCode, setDevQrCode] = useState<string | null>(null);
  const [devUrl, setDevUrl] = useState<string>('');
  const [isQrDialogOpen, setIsQrDialogOpen] = useState(false);
  const [activeQrTab, setActiveQrTab] = useState('preview');
  const [isThemeAuthor, setIsThemeAuthor] = useState(false);

  // Theme selector state
  const [isThemeSelectorOpen, setIsThemeSelectorOpen] = useState(false);
  const [themeBuilderKey, setThemeBuilderKey] = useState(0);

  // Refs
  const pageParamsRef = useRef<{
    themeId?: string;
    editThemeId?: string;
    tempThemeId?: string;
  }>({});
  const isInitialized = useRef(false);

  // Generate preview QR code
  const generatePreviewQrCode = useCallback(() => {
    const themeId =
      pageParamsRef.current.editThemeId || pageParamsRef.current.themeId;
    const previewUrl = `https://zalo.me/s/${themeId}/?tempThemeId=${tempTheme.id}`;
    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(previewUrl)}`;
    setPreviewQrCode(qrCodeUrl);
  }, [tempTheme?.id]);
  // Generate developer QR code
  const generateDevQrCode = useCallback(() => {
    const separator = devUrl.includes('?') ? '&' : '?';
    const fullUrl = `${devUrl}${separator}tempThemeId=${tempTheme.id}`;
    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(fullUrl)}`;

    setDevQrCode(qrCodeUrl);
  }, [devUrl, tempTheme?.id]);

  // Generate QR code when tempTheme is available
  useEffect(() => {
    if (tempTheme?.id) {
      generatePreviewQrCode();
    }
  }, [tempTheme?.id, generatePreviewQrCode]);
  // Initialize theme data
  useEffect(() => {
    const initTheme = async () => {
      if (isInitialized.current) return;

      try {
        const [resolvedParams, resolvedSearchParams] = await Promise.all([
          params,
          searchParams,
        ]);

        if (
          !resolvedSearchParams.themeId &&
          !resolvedSearchParams.editThemeId
        ) {
          router.push(`/home/<USER>/miniapp/setup?step=1`);
          return;
        }

        pageParamsRef.current = {
          themeId: resolvedSearchParams.themeId,
          editThemeId: resolvedSearchParams.editThemeId,
          tempThemeId: resolvedSearchParams.tempThemeId,
        };

        if (!account?.id) return;

        const themeData = await getInitialTheme(
          account.id,
          pageParamsRef.current.themeId,
          pageParamsRef.current.editThemeId,
          pageParamsRef.current.tempThemeId,
        );

        setPuckData(themeData.puckData || EMPTY_PUCK_DATA);

        // Create temp theme if needed
        if (!pageParamsRef.current.tempThemeId) {
          const tempTheme = await createOrUpdateTempTheme(
            account.id,
            themeData,
            pageParamsRef.current.themeId,
            pageParamsRef.current.editThemeId,
          );
          setTempTheme(tempTheme);
        } else {
          setTempTheme({ id: pageParamsRef.current.tempThemeId });
        }

        isInitialized.current = true;
      } catch (error) {
        console.error('Init error:', error);
        toast.error('Failed to load theme');
      } finally {
        setLoading(false);
      }
    };

    initTheme();
  }, [account?.id, params, searchParams, router]);

  // Auto-save changes
  const saveChanges = useCallback(
    debounce(async (data: Partial<ThemeConfig>) => {
      if (!account?.id || !tempTheme?.id) return;

      try {
        // For auto-save, just save the puckData directly without mapping
        // Mapping will be done during final save/publish
        const config = { puckData, ...data };
        await createOrUpdateTempTheme(
          account.id,
          config,
          pageParamsRef.current.themeId,
          pageParamsRef.current.editThemeId,
        );
      } catch (error) {
        console.error('Save error:', error);
        toast.error('Auto-save failed');
      }
    }, 1000),
    [account?.id, tempTheme?.id, puckData],
  );

  // Publish to preview
  const handlePublish = useCallback(
    async (data: PuckData) => {
      if (!account?.id) {
        toast.error('Account not found');
        return;
      }

      try {
        // For publish to preview, save the puckData directly
        // The mapping will be done when final save happens in saveFinalTheme
        const config = {
          puckData: data,
        };

        const savedTheme = await createOrUpdateTempTheme(
          account.id,
          config,
          pageParamsRef.current.themeId,
          pageParamsRef.current.editThemeId,
        );

        const themeId =
          pageParamsRef.current.editThemeId || pageParamsRef.current.themeId;
        router.push(
          `/home/<USER>/miniapp/setup/preview?themeId=${themeId}&tempThemeId=${savedTheme.id}`,
        );
      } catch (error) {
        console.error('Publish error:', error);
        toast.error('Failed to save');
      }
    },
    [account, router],
  );

  const handlePuckChange = useCallback(
    (data: PuckData) => {
      setPuckData(data);
      saveChanges({ puckData: data });
    },
    [saveChanges],
  );

  // Handle template selection from theme selector
  const handleSelectTemplate = useCallback(
    async (templateData: any) => {
      console.log('Applying template data:', templateData);

      // Update puckData immediately
      setPuckData(templateData);

      // Force ThemeBuilder to re-render with new key
      setThemeBuilderKey((prev) => prev + 1);

      // Force save to temp theme immediately (not debounced)
      if (account?.id && tempTheme?.id) {
        try {
          await createOrUpdateTempTheme(
            account.id,
            { puckData: templateData },
            pageParamsRef.current.themeId,
            pageParamsRef.current.editThemeId,
          );
          console.log('Template saved to temp theme');
        } catch (error) {
          console.error('Failed to save template:', error);
        }
      }

      toast.success('Template applied successfully!');
    },
    [account?.id, tempTheme?.id, setThemeBuilderKey],
  );

  // Header actions for ThemeBuilder (using HeaderAction config for Puck buttons)
  const headerActions: HeaderAction[] = [
    {
      type: 'button',
      label: 'Back',
      icon: '←',
      variant: 'secondary',
      onClick: () =>
        router.push(
          pageParamsRef.current.editThemeId
            ? `/home/<USER>/miniapp`
            : `/home/<USER>/miniapp/setup?step=1`,
        ),
    },
    {
      type: 'button',
      label: 'Templates',
      icon: '🎨',
      variant: 'secondary',
      onClick: () => setIsThemeSelectorOpen(true),
    },
    ...(previewQrCode
      ? [
          {
            type: 'button' as const,
            label: 'QR Preview',
            icon: '📱',
            variant: 'secondary' as const,
            onClick: () => setIsQrDialogOpen(true),
          },
        ]
      : []),
  ];

  if (loading) return <div>Loading...</div>;

  return (
    <>
      <div className="flex h-screen">
        <div className="flex-1">
          <ThemeBuilderErrorBoundary
            onError={(error) => {
              console.error('ThemeBuilder error:', error);
              // toast.error('Theme editor encountered an error');
            }}
          >
            <ThemeBuilder
              key={`theme-builder-${pageParamsRef.current.themeId}-${pageParamsRef.current.editThemeId}-${pageParamsRef.current.tempThemeId}-${themeBuilderKey}`}
              data={puckData}
              onChange={handlePuckChange}
              onPreview={handlePuckChange}
              onPublish={handlePublish}
              headerActions={headerActions}
              viewports={[
                { width: 800, height: 1000, label: 'Mobile', icon: '📱' },
              ]}
              iframe={{ enabled: true }}
            />
          </ThemeBuilderErrorBoundary>
        </div>
      </div>

      {/* QR Code Dialog - Outside of ThemeBuilder */}
      <Dialog open={isQrDialogOpen} onOpenChange={setIsQrDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Scan to Preview</DialogTitle>
            <DialogClose className="ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:pointer-events-none">
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </DialogClose>
          </DialogHeader>

          <Tabs
            value={activeQrTab}
            onValueChange={setActiveQrTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="preview">Preview</TabsTrigger>
              {isThemeAuthor && (
                <TabsTrigger value="developer">Developer</TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="preview" className="mt-4">
              <div className="flex flex-col items-center gap-4">
                <img
                  src={previewQrCode}
                  alt="Preview QR Code"
                  className="h-48 w-48"
                />
                <p className="text-center text-sm text-gray-500">
                  Scan this QR code with your mobile device to preview the
                  customized mini app
                </p>
              </div>
            </TabsContent>

            {isThemeAuthor && (
              <TabsContent value="developer" className="mt-4">
                <div className="flex flex-col gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Development URL
                    </label>
                    <div className="flex gap-2">
                      <Input
                        value={devUrl}
                        onChange={(e) => setDevUrl(e.target.value)}
                        placeholder="https://zalo.me/app/link/zapps/..."
                        className="flex-1"
                      />
                      <Button
                        onClick={() => {
                          console.log('Generate button clicked:', {
                            devUrl,
                            tempThemeId: tempTheme?.id,
                          });
                          generateDevQrCode();
                        }}
                        disabled={!devUrl || !tempTheme?.id}
                        type="button"
                      >
                        Generate
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500">
                      Enter your development URL to generate a QR code with the
                      tempThemeId parameter
                    </p>
                  </div>

                  {(() => {
                    console.log('Rendering devQrCode section:', {
                      devQrCode,
                      hasDevQrCode: !!devQrCode,
                    });
                    return (
                      devQrCode && (
                        <div className="mt-4 flex flex-col items-center gap-4">
                          <img
                            src={devQrCode}
                            alt="Developer QR Code"
                            className="h-48 w-48"
                          />
                          <p className="text-center text-sm text-gray-500">
                            Scan this QR code to test your theme in the
                            development environment
                          </p>
                        </div>
                      )
                    );
                  })()}
                </div>
              </TabsContent>
            )}
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* Theme Selector Modal */}
      <ThemeSelectorModal
        isOpen={isThemeSelectorOpen}
        onClose={() => setIsThemeSelectorOpen(false)}
        onSelectTemplate={handleSelectTemplate}
        currentData={puckData}
      />
    </>
  );
}
