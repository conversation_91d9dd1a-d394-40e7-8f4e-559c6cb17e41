'use client';

import React from 'react';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Separator } from '@kit/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@kit/ui/tooltip';
import { 
  RefreshCw, 
  Trash2, 
  CheckCircle, 
  AlertTriangle, 
  Clock, 
  FileText,
  Zap,
  TrendingDown
} from 'lucide-react';

import { useCSSCache } from '../_lib/hooks/use-css-cache';

interface CSSCacheStatusProps {
  themeId?: string;
  tempThemeId?: string;
  accountThemeId?: string;
  showActions?: boolean;
  compact?: boolean;
}

export function CSSCacheStatus({
  themeId,
  tempThemeId,
  accountThemeId,
  showActions = true,
  compact = false,
}: CSSCacheStatusProps) {
  const {
    cacheInfo,
    isLoading,
    error,
    isConfigChanged,
    needsRegeneration,
    hasValidCache,
    regenerateCSS,
    clearCache,
    isRegenerating,
    isClearing,
    cacheAge,
    cssSize,
    compressionPercent,
  } = useCSSCache({ themeId, tempThemeId, accountThemeId });

  if (isLoading) {
    return (
      <Card className={compact ? 'p-3' : ''}>
        <CardContent className={compact ? 'p-0' : ''}>
          <div className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span className="text-sm text-muted-foreground">Loading CSS cache info...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={compact ? 'p-3' : ''}>
        <CardContent className={compact ? 'p-0' : ''}>
          <div className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm">Failed to load CSS cache info</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!cacheInfo) {
    return null;
  }

  const StatusBadge = () => {
    if (!cacheInfo.cacheStats.hasCachedCSS) {
      return <Badge variant="secondary">No Cache</Badge>;
    }
    
    if (isConfigChanged) {
      return <Badge variant="destructive">Outdated</Badge>;
    }
    
    if (!hasValidCache) {
      return <Badge variant="destructive">Invalid</Badge>;
    }
    
    return <Badge variant="default" className="bg-green-500">Cached</Badge>;
  };

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        <StatusBadge />
        {cacheInfo.cacheStats.hasCachedCSS && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <FileText className="h-3 w-3" />
                  {cssSize}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-xs">
                  <div>{cacheInfo.cacheStats.classCount} classes</div>
                  {compressionPercent && <div>{compressionPercent}% compressed</div>}
                  {cacheAge && <div>Generated {cacheAge}</div>}
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
        {showActions && needsRegeneration && (
          <Button
            size="sm"
            variant="outline"
            onClick={() => regenerateCSS(false)}
            disabled={isRegenerating}
            className="h-6 px-2 text-xs"
          >
            {isRegenerating ? (
              <RefreshCw className="h-3 w-3 animate-spin" />
            ) : (
              <Zap className="h-3 w-3" />
            )}
          </Button>
        )}
      </div>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base">CSS Cache Status</CardTitle>
            <CardDescription>
              Optimized CSS generation and caching
            </CardDescription>
          </div>
          <StatusBadge />
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Cache Statistics */}
        {cacheInfo.cacheStats.hasCachedCSS ? (
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="text-sm font-medium">CSS Size</div>
              <div className="text-2xl font-bold">{cssSize}</div>
              {compressionPercent && (
                <div className="flex items-center gap-1 text-xs text-green-600">
                  <TrendingDown className="h-3 w-3" />
                  {compressionPercent}% compressed
                </div>
              )}
            </div>
            
            <div className="space-y-1">
              <div className="text-sm font-medium">Classes</div>
              <div className="text-2xl font-bold">{cacheInfo.cacheStats.classCount}</div>
              {cacheAge && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  {cacheAge}
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <div className="text-sm">No cached CSS available</div>
            <div className="text-xs">CSS will be generated on first publish</div>
          </div>
        )}

        {/* Validation Issues */}
        {!cacheInfo.validation.isValid && cacheInfo.validation.issues.length > 0 && (
          <>
            <Separator />
            <div className="space-y-2">
              <div className="text-sm font-medium text-destructive">Issues Found:</div>
              <ul className="text-xs space-y-1">
                {cacheInfo.validation.issues.map((issue, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <AlertTriangle className="h-3 w-3 text-destructive" />
                    {issue}
                  </li>
                ))}
              </ul>
            </div>
          </>
        )}

        {/* Config Status */}
        {isConfigChanged && (
          <>
            <Separator />
            <div className="flex items-center gap-2 text-amber-600">
              <AlertTriangle className="h-4 w-4" />
              <div className="text-sm">
                Configuration has changed. CSS cache needs regeneration.
              </div>
            </div>
          </>
        )}

        {/* Actions */}
        {showActions && (
          <>
            <Separator />
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={() => regenerateCSS(false)}
                disabled={isRegenerating}
                className="flex-1"
              >
                {isRegenerating ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Regenerating...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    {needsRegeneration ? 'Generate CSS' : 'Regenerate CSS'}
                  </>
                )}
              </Button>
              
              {cacheInfo.cacheStats.hasCachedCSS && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => clearCache()}
                  disabled={isClearing}
                >
                  {isClearing ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                </Button>
              )}
            </div>
          </>
        )}

        {/* Debug Info */}
        {process.env.NODE_ENV === 'development' && (
          <>
            <Separator />
            <details className="text-xs">
              <summary className="cursor-pointer text-muted-foreground">Debug Info</summary>
              <div className="mt-2 space-y-1 font-mono">
                <div>Table: {cacheInfo.tableName}</div>
                <div>Theme ID: {cacheInfo.themeId}</div>
                <div>Current MD5: {cacheInfo.configInfo.currentMD5.slice(0, 8)}...</div>
                <div>Stored MD5: {cacheInfo.configInfo.storedMD5?.slice(0, 8) || 'None'}...</div>
                <div>CSS Hash: {cacheInfo.cacheStats.hasCachedCSS ? 'Present' : 'None'}</div>
              </div>
            </details>
          </>
        )}
      </CardContent>
    </Card>
  );
}

export default CSSCacheStatus;
