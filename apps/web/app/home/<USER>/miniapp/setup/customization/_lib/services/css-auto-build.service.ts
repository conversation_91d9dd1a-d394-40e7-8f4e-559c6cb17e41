/**
 * CSS Auto-Build Service
 *
 * Automatically detects Tailwind classes from theme config and builds optimized CSS
 */
import { createHash } from 'crypto';

import { buildOptimizedCSS } from '@kit/theme-builder';

import type {
  EnhancedThemeConfig,
  ThemeConfig,
} from '../types/theme-config.types';





/**
 * Generate MD5 hash for config
 */
export function generateConfigMD5(config: ThemeConfig): string {
  // Create a stable string representation of config for hashing
  const configForHash = {
    puckData: config.puckData,
    colors: config.colors,
    typography: config.typography,
    // Exclude CSS-related fields from hash calculation
  };

  const configString = JSON.stringify(configForHash, Object.keys(configForHash).sort());
  return createHash('md5').update(configString).digest('hex');
}

/**
 * Check if CSS needs to be regenerated
 */
export function needsCSSRegeneration(
  currentConfig: ThemeConfig,
  storedConfig?: EnhancedThemeConfig
): boolean {
  if (!storedConfig) return true;

  const currentMD5 = generateConfigMD5(currentConfig);
  const storedMD5 = storedConfig.configMd5;

  // Need regeneration if:
  // 1. Config MD5 has changed
  // 2. No CSS hash exists
  // 3. No stored MD5 (first time)
  return (
    currentMD5 !== storedMD5 ||
    !storedConfig.cssHash ||
    !storedMD5
  );
}

/**
 * Auto-build CSS from theme config
 */
export async function autoBuildCSS(config: ThemeConfig): Promise<{
  optimizedCSS: string;
  cssHash: string;
  cssMetadata: any;
  configMd5: string;
}> {
  try {
    console.log('🔧 Auto-building CSS from theme config...');

    // Generate config MD5
    const configMd5 = generateConfigMD5(config);

    // Extract Tailwind classes from puckData
    if (!config.puckData) {
      console.warn('No puckData found in config, generating empty CSS');
      return {
        optimizedCSS: '',
        cssHash: '',
        cssMetadata: {
          extractedAt: new Date().toISOString(),
          classes: [],
          stats: {
            totalClasses: 0,
            uniqueClasses: 0,
            cssSize: 0,
            originalSize: 0,
            minifiedSize: 0,
            compressionRatio: 0,
          },
        },
        configMd5,
      };
    }

    // Build optimized CSS
    const result = await buildOptimizedCSS(config.puckData);

    console.log('✅ CSS auto-build completed:', {
      configMd5,
      cssHash: result.hash,
      classes: result.stats.uniqueClasses,
      size: result.stats.minifiedSize,
    });

    return {
      optimizedCSS: result.minified,
      cssHash: result.hash,
      cssMetadata: {
        extractedAt: new Date().toISOString(),
        classes: result.classes,
        stats: result.stats,
      },
      configMd5,
    };

  } catch (error) {
    console.error('❌ CSS auto-build failed:', error);
    throw new Error(`CSS auto-build failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create enhanced theme config with auto-built CSS
 */
export async function createEnhancedThemeConfig(
  baseConfig: ThemeConfig,
  existingConfig?: EnhancedThemeConfig
): Promise<EnhancedThemeConfig> {
  // Check if CSS regeneration is needed
  if (!needsCSSRegeneration(baseConfig, existingConfig)) {
    console.log('🔄 CSS regeneration not needed, using existing CSS');
    return {
      ...baseConfig,
      optimizedCSS: existingConfig!.optimizedCSS,
      cssHash: existingConfig!.cssHash,
      cssMetadata: existingConfig!.cssMetadata,
      configMd5: existingConfig!.configMd5,
    };
  }

  console.log('🔄 CSS regeneration needed, building new CSS...');

  // Auto-build CSS
  const cssResult = await autoBuildCSS(baseConfig);

  // Create enhanced config
  const enhancedConfig: EnhancedThemeConfig = {
    ...baseConfig,
    ...cssResult,
  };

  return enhancedConfig;
}

/**
 * Validate CSS cache
 */
export function validateCSSCache(config: EnhancedThemeConfig): {
  isValid: boolean;
  issues: string[];
} {
  const issues: string[] = [];

  // Check required fields
  if (!config.configMd5) {
    issues.push('Missing config MD5 hash');
  }

  if (!config.cssHash && config.optimizedCSS) {
    issues.push('CSS exists but no CSS hash');
  }

  if (config.cssHash && !config.optimizedCSS) {
    issues.push('CSS hash exists but no CSS content');
  }

  // Check metadata consistency
  if (config.optimizedCSS && !config.cssMetadata) {
    issues.push('CSS exists but no metadata');
  }

  // Validate MD5 consistency
  if (config.configMd5) {
    const expectedMD5 = generateConfigMD5(config);
    if (config.configMd5 !== expectedMD5) {
      issues.push('Config MD5 mismatch - CSS may be outdated');
    }
  }

  return {
    isValid: issues.length === 0,
    issues,
  };
}

/**
 * Get CSS cache stats
 */
export function getCSSCacheStats(config: EnhancedThemeConfig): {
  hasCachedCSS: boolean;
  cssSize: number;
  classCount: number;
  compressionRatio: number;
  lastGenerated?: string;
  cacheAge?: number; // in milliseconds
} {
  if (!config.optimizedCSS || !config.cssMetadata) {
    return {
      hasCachedCSS: false,
      cssSize: 0,
      classCount: 0,
      compressionRatio: 0,
    };
  }

  const lastGenerated = config.cssMetadata.extractedAt;
  const cacheAge = lastGenerated
    ? Date.now() - new Date(lastGenerated).getTime()
    : undefined;

  return {
    hasCachedCSS: true,
    cssSize: config.cssMetadata.stats.minifiedSize,
    classCount: config.cssMetadata.stats.uniqueClasses,
    compressionRatio: config.cssMetadata.stats.compressionRatio,
    lastGenerated,
    cacheAge,
  };
}

/**
 * Force CSS regeneration
 */
export async function forceCSSRegeneration(config: ThemeConfig): Promise<EnhancedThemeConfig> {
  console.log('🔄 Forcing CSS regeneration...');

  const cssResult = await autoBuildCSS(config);

  return {
    ...config,
    ...cssResult,
  };
}

/**
 * Compare two configs for CSS cache invalidation
 */
export function compareConfigsForCacheInvalidation(
  config1: ThemeConfig,
  config2: ThemeConfig
): {
  needsInvalidation: boolean;
  changedFields: string[];
} {
  const changedFields: string[] = [];

  // Compare puckData
  if (JSON.stringify(config1.puckData) !== JSON.stringify(config2.puckData)) {
    changedFields.push('puckData');
  }

  // Compare colors
  if (JSON.stringify(config1.colors) !== JSON.stringify(config2.colors)) {
    changedFields.push('colors');
  }

  // Compare typography
  if (JSON.stringify(config1.typography) !== JSON.stringify(config2.typography)) {
    changedFields.push('typography');
  }

  return {
    needsInvalidation: changedFields.length > 0,
    changedFields,
  };
}

export default {
  generateConfigMD5,
  needsCSSRegeneration,
  autoBuildCSS,
  createEnhancedThemeConfig,
  validateCSSCache,
  getCSSCacheStats,
  forceCSSRegeneration,
  compareConfigsForCacheInvalidation,
};
