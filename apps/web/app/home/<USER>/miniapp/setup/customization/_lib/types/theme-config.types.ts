// Puck Data Types based on API Reference
export interface ComponentData {
  type: string;
  props: {
    id: string;
    [key: string]: any;
  };
  readOnly?: {
    [key: string]: boolean;
  };
}

export interface RootData {
  props: {
    [key: string]: any;
  };
}

export interface PuckData {
  content: ComponentData[];
  root: RootData;
  zones?: {
    [zoneKey: string]: ComponentData[];
  };
}

export interface ThemeConfig {
  name: string;
  author_id?: string;
  template_id?: string;
  template?: any; // Template object from the database
  puckData?: PuckData; // Puck page builder data - primary configuration mechanism
  colors: {
    primary: { main: string; light: string; dark: string };
    secondary: { main: string; light: string; dark: string };
    accent: { main: string; light: string; dark: string };
    background: { default: string; paper: string };
    text: { primary: string; secondary: string };
  };
  typography: {
    fontFamily: string;
    headings: { fontFamily: string; fontWeight: string };
    body: { fontFamily: string; fontWeight: string };
  };
}
