// Puck Data Types based on API Reference
export interface ComponentData {
  type: string;
  props: {
    id: string;
    [key: string]: any;
  };
  readOnly?: {
    [key: string]: boolean;
  };
}

export interface RootData {
  props: {
    [key: string]: any;
  };
}

export interface PuckData {
  content: ComponentData[];
  root: RootData;
  zones?: {
    [zoneKey: string]: ComponentData[];
  };
}

export interface ThemeConfig {
  name: string;
  author_id?: string;
  template_id?: string;
  template?: any; // Template object from the database
  puckData?: PuckData; // Puck page builder data - primary configuration mechanism
  colors: {
    primary: { main: string; light: string; dark: string };
    secondary: { main: string; light: string; dark: string };
    accent: { main: string; light: string; dark: string };
    background: { default: string; paper: string };
    text: { primary: string; secondary: string };
  };
  typography: {
    fontFamily: string;
    headings: { fontFamily: string; fontWeight: string };
    body: { fontFamily: string; fontWeight: string };
  };

  // CSS Optimization fields
  optimizedCSS?: string;
  cssHash?: string;
  cssMetadata?: {
    extractedAt: string;
    classes: string[];
    stats: {
      totalClasses: number;
      uniqueClasses: number;
      cssSize: number;
      originalSize: number;
      minifiedSize: number;
      compressionRatio: number;
    };
  };
  configMd5?: string;
}

// Enhanced interface for database operations
export interface EnhancedThemeConfig extends ThemeConfig {
  id?: string;
  account_id?: string;
  is_active?: boolean;
  version?: string;
  oa_config_id?: string;
  mini_app_id?: string;
  created_at?: string;
  updated_at?: string;
  expires_at?: string; // For temp_themes
  preview_token?: string; // For temp_themes
}
