/**
 * Server-side CSS Build Service
 * 
 * Pure server-side implementation for CSS generation without client dependencies
 */

import { createHash } from 'crypto';

// Server-safe types (no client dependencies)
interface PuckData {
  content: any[];
  root: any;
  zones?: any;
}

interface CSSBuildResult {
  css: string;
  minified: string;
  hash: string;
  size: number;
  classes: string[];
  stats: {
    originalSize: number;
    minifiedSize: number;
    compressionRatio: number;
    classCount: number;
  };
}

/**
 * Extract Tailwind classes from Puck data (server-side)
 */
function extractTailwindClassesServer(data: PuckData): string[] {
  const classes: string[] = [];
  
  // Field keys that contain Tailwind classes
  const TAILWIND_FIELD_KEYS = [
    'className',
    'containerClassName',
    'contentClassName',
    'headerClassName',
    'bodyClassName',
    'footerClassName',
    'textClassName',
    'buttonClassName',
    'inputClassName',
    'labelClassName',
    'imageClassName',
    'linkClassName',
    'iconClassName',
  ];

  function traverse(obj: any) {
    if (!obj || typeof obj !== 'object') return;

    if (Array.isArray(obj)) {
      obj.forEach(traverse);
      return;
    }

    for (const [key, value] of Object.entries(obj)) {
      if (TAILWIND_FIELD_KEYS.includes(key) && typeof value === 'string') {
        // Split by whitespace and filter out empty strings
        const classNames = value.split(/\s+/).filter(Boolean);
        classes.push(...classNames);
      }
      
      if (typeof value === 'object') {
        traverse(value);
      }
    }
  }

  traverse(data);
  return classes;
}

/**
 * Basic CSS minification (server-side)
 */
function minifyCSSServer(css: string): string {
  return css
    // Remove comments
    .replace(/\/\*[\s\S]*?\*\//g, '')
    // Remove extra whitespace
    .replace(/\s+/g, ' ')
    // Remove whitespace around specific characters
    .replace(/\s*([{}:;,>+~])\s*/g, '$1')
    // Remove trailing semicolons
    .replace(/;}/g, '}')
    // Remove leading/trailing whitespace
    .trim();
}

/**
 * Generate hash for content
 */
function generateHashServer(content: string): string {
  return createHash('md5').update(content).digest('hex').slice(0, 8);
}

/**
 * Build CSS using PostCSS + Tailwind (server-side)
 */
async function buildCSSWithTailwind(classes: string[]): Promise<string> {
  if (classes.length === 0) {
    return '';
  }

  try {
    // Dynamic import to avoid bundling in client
    const postcss = await import('postcss');
    const tailwindcss = await import('tailwindcss');
    
    // Create minimal HTML with all classes for Tailwind to detect
    const htmlContent = `<div class="${classes.join(' ')}"></div>`;
    
    // Tailwind config for server-side processing
    const tailwindConfig = {
      content: [{ raw: htmlContent, extension: 'html' }],
      theme: {
        extend: {
          colors: {
            'zalo': '#0068ff',
            'zalo-light': '#4096ff',
            'zalo-dark': '#0050d1',
          },
        },
      },
      corePlugins: {
        preflight: false, // Disable base styles for component-only CSS
      },
    };

    // Process CSS with PostCSS + Tailwind
    const result = await postcss.default([
      tailwindcss.default(tailwindConfig),
    ]).process('@tailwind utilities;', {
      from: undefined,
    });

    return result.css;
    
  } catch (error) {
    console.error('Failed to build CSS with Tailwind:', error);
    
    // Fallback: generate basic CSS for common classes
    return generateFallbackCSS(classes);
  }
}

/**
 * Fallback CSS generation for common Tailwind classes
 */
function generateFallbackCSS(classes: string[]): string {
  const cssRules: string[] = [];
  
  classes.forEach(className => {
    // Basic padding classes
    const paddingMatch = className.match(/^p-(\d+)$/);
    if (paddingMatch) {
      const value = parseInt(paddingMatch[1]) * 0.25; // Tailwind spacing scale
      cssRules.push(`.${className}{padding:${value}rem}`);
      return;
    }
    
    // Basic margin classes
    const marginMatch = className.match(/^m-(\d+)$/);
    if (marginMatch) {
      const value = parseInt(marginMatch[1]) * 0.25;
      cssRules.push(`.${className}{margin:${value}rem}`);
      return;
    }
    
    // Basic text colors
    if (className.startsWith('text-')) {
      const colorMap: Record<string, string> = {
        'text-white': '#ffffff',
        'text-black': '#000000',
        'text-gray-500': '#6b7280',
        'text-blue-500': '#3b82f6',
        'text-red-500': '#ef4444',
        'text-green-500': '#10b981',
      };
      
      if (colorMap[className]) {
        cssRules.push(`.${className}{color:${colorMap[className]}}`);
      }
    }
    
    // Basic background colors
    if (className.startsWith('bg-')) {
      const colorMap: Record<string, string> = {
        'bg-white': '#ffffff',
        'bg-black': '#000000',
        'bg-gray-100': '#f3f4f6',
        'bg-blue-500': '#3b82f6',
        'bg-red-500': '#ef4444',
        'bg-green-500': '#10b981',
      };
      
      if (colorMap[className]) {
        cssRules.push(`.${className}{background-color:${colorMap[className]}}`);
      }
    }
    
    // Basic border radius
    const borderRadiusMap: Record<string, string> = {
      'rounded': '0.25rem',
      'rounded-md': '0.375rem',
      'rounded-lg': '0.5rem',
      'rounded-xl': '0.75rem',
      'rounded-full': '9999px',
    };
    
    if (borderRadiusMap[className]) {
      cssRules.push(`.${className}{border-radius:${borderRadiusMap[className]}}`);
    }
  });
  
  return cssRules.join('');
}

/**
 * Build optimized CSS (server-side implementation)
 */
export async function buildOptimizedCSSServer(data: PuckData): Promise<CSSBuildResult> {
  try {
    console.log('🔧 Building CSS server-side...');
    
    // Extract classes
    const allClasses = extractTailwindClassesServer(data);
    const uniqueClasses = Array.from(new Set(allClasses));
    
    console.log(`📋 Extracted ${uniqueClasses.length} unique classes from ${allClasses.length} total`);
    
    // Generate CSS
    const css = await buildCSSWithTailwind(uniqueClasses);
    
    // Minify CSS
    const minified = minifyCSSServer(css);
    
    // Generate hash
    const hash = generateHashServer(minified);
    
    // Calculate stats
    const originalSize = Buffer.byteLength(css, 'utf8');
    const minifiedSize = Buffer.byteLength(minified, 'utf8');
    const compressionRatio = originalSize > 0 ? minifiedSize / originalSize : 0;
    
    const result: CSSBuildResult = {
      css,
      minified,
      hash,
      size: minifiedSize,
      classes: uniqueClasses,
      stats: {
        originalSize,
        minifiedSize,
        compressionRatio,
        classCount: uniqueClasses.length,
      },
    };
    
    console.log('✅ CSS build completed:', {
      hash,
      originalSize: `${(originalSize / 1024).toFixed(2)} KB`,
      minifiedSize: `${(minifiedSize / 1024).toFixed(2)} KB`,
      compression: `${((1 - compressionRatio) * 100).toFixed(1)}%`,
      classes: uniqueClasses.length,
    });
    
    return result;
    
  } catch (error) {
    console.error('❌ Server CSS build failed:', error);
    throw new Error(`Server CSS build failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export default {
  buildOptimizedCSSServer,
  extractTailwindClassesServer,
  minifyCSSServer,
  generateHashServer,
};
