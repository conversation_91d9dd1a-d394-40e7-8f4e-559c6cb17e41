// Marketing Templates for Education - JSON Seed Data
// Following correct Puck data model structure

export const MARKETING_TEMPLATES = [
  {
    id: 'education-hero-landing',
    name: 'Hero Landing - <PERSON>i<PERSON><PERSON>',
    description: 'Trang chủ thu hút phụ huynh với hero section và thông tin chương trình',
    category: 'landing',
    thumbnail: '/images/templates/education-hero-landing.jpg',
    data: {
      content: [
        {
          type: 'Text',
          props: {
            id: 'hero-title',
            text: '🌟 Trung Tâm Giáo Dục Sao Mai',
            tag: 'h1',
            size: '3xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '0 0 16px 0',
          },
        },
        {
          type: 'Text',
          props: {
            id: 'hero-subtitle',
            text: 'Nơi ươm mầm tài năng - Phát triển toàn diện cho con em bạn',
            tag: 'p',
            size: 'lg',
            weight: 'medium',
            align: 'center',
            color: '#374151',
            margin: '0 0 32px 0',
          },
        },
        {
          type: 'Button',
          props: {
            id: 'hero-cta',
            label: '📞 Đăng Ký <PERSON>ư <PERSON>ấn <PERSON>',
            variant: 'primary',
            size: 'large',
            fullWidth: false,
          },
        },
        {
          type: 'Grid',
          props: {
            id: 'features-grid',
            columnCount: 2,
            columnGap: '1rem',
            rowGap: '1rem',
            items: [
              {
                type: 'Card',
                props: {
                  id: 'feature-1',
                  title: '🎯 Chương Trình Cá Nhân Hóa',
                  subtitle: 'Thiết kế riêng cho từng học sinh',
                  padding: '20px',
                  shadow: 'md',
                  borderRadius: '12px',
                  backgroundColor: '#f0f9ff',
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'feature-2',
                  title: '👨‍🏫 Đội Ngũ Giáo Viên Chuyên Nghiệp',
                  subtitle: 'Kinh nghiệm 10+ năm giảng dạy',
                  padding: '20px',
                  shadow: 'md',
                  borderRadius: '12px',
                  backgroundColor: '#f0fdf4',
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'feature-3',
                  title: '📊 Theo Dõi Tiến Độ',
                  subtitle: 'Báo cáo chi tiết cho phụ huynh',
                  padding: '20px',
                  shadow: 'md',
                  borderRadius: '12px',
                  backgroundColor: '#fef3c7',
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'feature-4',
                  title: '🏆 Cam Kết Chất Lượng',
                  subtitle: 'Hoàn tiền 100% nếu không hài lòng',
                  padding: '20px',
                  shadow: 'md',
                  borderRadius: '12px',
                  backgroundColor: '#fce7f3',
                },
              },
            ],
          },
        },
        {
          type: 'Text',
          props: {
            id: 'contact-title',
            text: '📞 Liên Hệ Ngay Hôm Nay',
            tag: 'h2',
            size: '2xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '32px 0 16px 0',
          },
        },
        {
          type: 'List',
          props: {
            id: 'contact-info',
            items: [
              {
                id: 'phone',
                title: '📱 Hotline: 0283456789',
                subtitle: 'Tư vấn 24/7',
                prefix: '📞',
              },
              {
                id: 'address',
                title: '📍 123 Đường ABC, Quận 1, TP.HCM',
                subtitle: 'Ghé thăm cơ sở',
                prefix: '🏢',
              },
              {
                id: 'email',
                title: '✉️ <EMAIL>',
                subtitle: 'Gửi email tư vấn',
                prefix: '📧',
              },
            ],
            divider: true,
            clickable: true,
          },
        },
      ],
      root: {
        props: {
          title: 'Trung Tâm Giáo Dục Sao Mai',
          spacing: '16px',
          textColor: '#1f2937',
          fontFamily: 'Inter, system-ui, sans-serif',
          accentColor: '#f59e0b',
          borderRadius: '8px',
          primaryColor: '#2563eb',
          bodyFontFamily: 'Inter, system-ui, sans-serif',
          bodyFontWeight: '400',
          secondaryColor: '#10b981',
          accentDarkColor: '#d97706',
          backgroundColor: '#ffffff',
          accentLightColor: '#fbbf24',
          primaryDarkColor: '#1d4ed8',
          primaryLightColor: '#3b82f6',
          headingsFontFamily: 'Inter, system-ui, sans-serif',
          headingsFontWeight: '600',
          secondaryDarkColor: '#4338ca',
          secondaryTextColor: '#4b5563',
          secondaryLightColor: '#6366f1',
          paperBackgroundColor: '#f3f4f6',
        },
      },
      zones: {},
    },
  },

  {
    id: 'programs-showcase',
    name: 'Giới Thiệu Chương Trình',
    description: 'Showcase các chương trình học với thông tin chi tiết và giá cả',
    category: 'programs',
    thumbnail: '/images/templates/programs-showcase.jpg',
    data: {
      content: [
        {
          type: 'Text',
          props: {
            id: 'programs-header',
            text: '🎓 Các Chương Trình Đào Tạo',
            tag: 'h1',
            size: '3xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '0 0 32px 0',
          },
        },
        {
          type: 'Grid',
          props: {
            id: 'programs-grid',
            columnCount: 1,
            rowGap: '2rem',
            items: [
              {
                type: 'Card',
                props: {
                  id: 'program-math',
                  title: '🧮 Toán Tư Duy Sáng Tạo',
                  subtitle: 'Phát triển tư duy logic và khả năng giải quyết vấn đề',
                  padding: '24px',
                  shadow: 'lg',
                  borderRadius: '16px',
                  backgroundColor: '#f0f9ff',
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'program-english',
                  title: '🗣️ Tiếng Anh Giao Tiếp',
                  subtitle: 'Học tiếng Anh qua hoạt động vui chơi và thực hành',
                  padding: '24px',
                  shadow: 'lg',
                  borderRadius: '16px',
                  backgroundColor: '#f0fdf4',
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'program-art',
                  title: '🎨 Mỹ Thuật Sáng Tạo',
                  subtitle: 'Phát triển khả năng thẩm mỹ và sáng tạo nghệ thuật',
                  padding: '24px',
                  shadow: 'lg',
                  borderRadius: '16px',
                  backgroundColor: '#fef3c7',
                },
              },
            ],
          },
        },
        {
          type: 'Text',
          props: {
            id: 'cta-text',
            text: '💝 Ưu đãi đặc biệt: Giảm 20% học phí tháng đầu',
            tag: 'p',
            size: 'lg',
            weight: 'semibold',
            align: 'center',
            color: '#dc2626',
            margin: '32px 0 16px 0',
          },
        },
        {
          type: 'Button',
          props: {
            id: 'register-btn',
            label: '🎯 Đăng Ký Học Thử Miễn Phí',
            variant: 'primary',
            size: 'large',
            fullWidth: true,
          },
        },
      ],
      root: {
        props: {
          title: 'Chương Trình Đào Tạo',
          spacing: '16px',
          textColor: '#1f2937',
          fontFamily: 'Inter, system-ui, sans-serif',
          accentColor: '#f59e0b',
          borderRadius: '8px',
          primaryColor: '#2563eb',
          bodyFontFamily: 'Inter, system-ui, sans-serif',
          bodyFontWeight: '400',
          secondaryColor: '#10b981',
          accentDarkColor: '#d97706',
          backgroundColor: '#ffffff',
          accentLightColor: '#fbbf24',
          primaryDarkColor: '#1d4ed8',
          primaryLightColor: '#3b82f6',
          headingsFontFamily: 'Inter, system-ui, sans-serif',
          headingsFontWeight: '600',
          secondaryDarkColor: '#4338ca',
          secondaryTextColor: '#4b5563',
          secondaryLightColor: '#6366f1',
          paperBackgroundColor: '#f3f4f6',
        },
      },
      zones: {},
    },
  },

  {
    id: 'testimonials-social-proof',
    name: 'Phản Hồi Phụ Huynh',
    description: 'Hiển thị testimonials và đánh giá từ phụ huynh để tăng độ tin cậy',
    category: 'testimonials',
    thumbnail: '/images/templates/testimonials-social-proof.jpg',
    data: {
      content: [
        {
          type: 'Text',
          props: {
            id: 'testimonials-header',
            text: '💬 Phụ Huynh Nói Gì Về Chúng Tôi',
            tag: 'h1',
            size: '3xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '0 0 32px 0',
          },
        },
        {
          type: 'Grid',
          props: {
            id: 'testimonials-grid',
            columnCount: 1,
            rowGap: '1rem',
            items: [
              {
                type: 'Card',
                props: {
                  id: 'testimonial-1',
                  title: '💬 Phụ Huynh Hài Lòng',
                  subtitle: '"Con tôi đã tiến bộ rất nhiều sau 3 tháng học tại trung tâm. Các thầy cô rất tận tâm và chuyên nghiệp." - Chị Nguyễn Thị Lan',
                  padding: '20px',
                  shadow: 'md',
                  borderRadius: '12px',
                  backgroundColor: '#f0f9ff',
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'testimonial-2',
                  title: '⭐ Đánh Giá Tích Cực',
                  subtitle: '"Chương trình học rất phù hợp với lứa tuổi. Con tôi rất thích đến lớp và học tập tích cực hơn." - Anh Trần Văn Nam',
                  padding: '20px',
                  shadow: 'md',
                  borderRadius: '12px',
                  backgroundColor: '#f0fdf4',
                },
              },
            ],
          },
        },
        {
          type: 'Grid',
          props: {
            id: 'stats-grid',
            columnCount: 3,
            columnGap: '1rem',
            rowGap: '1rem',
            items: [
              {
                type: 'Text',
                props: {
                  id: 'stat-students',
                  text: '500+ Học Sinh',
                  tag: 'h3',
                  size: 'xl',
                  weight: 'bold',
                  align: 'center',
                  color: '#2563eb',
                },
              },
              {
                type: 'Text',
                props: {
                  id: 'stat-satisfaction',
                  text: '98% Hài Lòng',
                  tag: 'h3',
                  size: 'xl',
                  weight: 'bold',
                  align: 'center',
                  color: '#10b981',
                },
              },
              {
                type: 'Text',
                props: {
                  id: 'stat-experience',
                  text: '10+ Năm KN',
                  tag: 'h3',
                  size: 'xl',
                  weight: 'bold',
                  align: 'center',
                  color: '#f59e0b',
                },
              },
            ],
          },
        },
        {
          type: 'Button',
          props: {
            id: 'final-cta-btn',
            label: '🚀 Bắt Đầu Hành Trình Học Tập',
            variant: 'primary',
            size: 'large',
            fullWidth: true,
          },
        },
      ],
      root: {
        props: {
          title: 'Phản Hồi Phụ Huynh',
          spacing: '16px',
          textColor: '#1f2937',
          fontFamily: 'Inter, system-ui, sans-serif',
          accentColor: '#f59e0b',
          borderRadius: '8px',
          primaryColor: '#2563eb',
          bodyFontFamily: 'Inter, system-ui, sans-serif',
          bodyFontWeight: '400',
          secondaryColor: '#10b981',
          accentDarkColor: '#d97706',
          backgroundColor: '#ffffff',
          accentLightColor: '#fbbf24',
          primaryDarkColor: '#1d4ed8',
          primaryLightColor: '#3b82f6',
          headingsFontFamily: 'Inter, system-ui, sans-serif',
          headingsFontWeight: '600',
          secondaryDarkColor: '#4338ca',
          secondaryTextColor: '#4b5563',
          secondaryLightColor: '#6366f1',
          paperBackgroundColor: '#f3f4f6',
        },
      },
      zones: {},
    },
  },

  {
    id: 'contact-form-lead',
    name: 'Form Liên Hệ & Thu Thập Lead',
    description: 'Form đăng ký tư vấn với các trường thông tin cần thiết',
    category: 'contact',
    thumbnail: '/images/templates/contact-form-lead.jpg',
    data: {
      content: [
        {
          type: 'Text',
          props: {
            id: 'form-header',
            text: '📝 Đăng Ký Tư Vấn Miễn Phí',
            tag: 'h1',
            size: '3xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '0 0 16px 0',
          },
        },
        {
          type: 'Text',
          props: {
            id: 'form-subtitle',
            text: 'Để lại thông tin, chúng tôi sẽ liên hệ tư vấn trong 24h',
            tag: 'p',
            size: 'base',
            weight: 'normal',
            align: 'center',
            color: '#6b7280',
            margin: '0 0 32px 0',
          },
        },
        {
          type: 'Input',
          props: {
            id: 'parent-name',
            label: 'Họ tên phụ huynh *',
            placeholder: 'Nhập họ tên của bạn',
            required: true,
          },
        },
        {
          type: 'Input',
          props: {
            id: 'phone-number',
            label: 'Số điện thoại *',
            placeholder: 'Nhập số điện thoại',
            required: true,
          },
        },
        {
          type: 'Select',
          props: {
            id: 'child-age',
            label: 'Độ tuổi con em',
            placeholder: 'Chọn độ tuổi',
            options: [
              { label: '3-5 tuổi', value: '3-5' },
              { label: '6-8 tuổi', value: '6-8' },
              { label: '9-12 tuổi', value: '9-12' },
              { label: '13-15 tuổi', value: '13-15' },
              { label: '16+ tuổi', value: '16+' },
            ],
          },
        },
        {
          type: 'Button',
          props: {
            id: 'submit-btn',
            label: '📞 Gửi Thông Tin & Nhận Tư Vấn',
            variant: 'primary',
            size: 'large',
            fullWidth: true,
          },
        },
        {
          type: 'Text',
          props: {
            id: 'privacy-note',
            text: '🔒 Thông tin của bạn được bảo mật tuyệt đối',
            tag: 'p',
            size: 'sm',
            weight: 'medium',
            align: 'center',
            color: '#10b981',
            margin: '16px 0 0 0',
          },
        },
      ],
      root: {
        props: {
          title: 'Đăng Ký Tư Vấn',
          spacing: '16px',
          textColor: '#1f2937',
          fontFamily: 'Inter, system-ui, sans-serif',
          accentColor: '#f59e0b',
          borderRadius: '8px',
          primaryColor: '#2563eb',
          bodyFontFamily: 'Inter, system-ui, sans-serif',
          bodyFontWeight: '400',
          secondaryColor: '#10b981',
          accentDarkColor: '#d97706',
          backgroundColor: '#ffffff',
          accentLightColor: '#fbbf24',
          primaryDarkColor: '#1d4ed8',
          primaryLightColor: '#3b82f6',
          headingsFontFamily: 'Inter, system-ui, sans-serif',
          headingsFontWeight: '600',
          secondaryDarkColor: '#4338ca',
          secondaryTextColor: '#4b5563',
          secondaryLightColor: '#6366f1',
          paperBackgroundColor: '#f3f4f6',
        },
      },
      zones: {},
    },
  },
];

export default MARKETING_TEMPLATES;
