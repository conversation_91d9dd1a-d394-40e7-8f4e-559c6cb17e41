/**
 * CSS Cache Hook
 * 
 * React hook for managing CSS cache operations
 */

import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

export interface CSSCacheStats {
  hasCachedCSS: boolean;
  cssSize: number;
  classCount: number;
  compressionRatio: number;
  lastGenerated?: string;
  cacheAge?: number;
}

export interface CSSCacheValidation {
  isValid: boolean;
  issues: string[];
}

export interface CSSCacheInfo {
  themeId: string;
  tableName: string;
  cacheStats: CSSCacheStats;
  validation: CSSCacheValidation;
  configInfo: {
    currentMD5: string;
    storedMD5: string;
    isChanged: boolean;
  };
  timestamps: {
    createdAt: string;
    updatedAt: string;
    expiresAt?: string;
  };
}

/**
 * Hook for CSS cache operations
 */
export function useCSSCache(params: {
  themeId?: string;
  tempThemeId?: string;
  accountThemeId?: string;
}) {
  const queryClient = useQueryClient();
  const { themeId, tempThemeId, accountThemeId } = params;

  // Create query key
  const queryKey = ['css-cache', { themeId, tempThemeId, accountThemeId }];

  // Fetch CSS cache info
  const {
    data: cacheInfo,
    isLoading,
    error,
    refetch,
  } = useQuery<CSSCacheInfo>({
    queryKey,
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      if (themeId) searchParams.set('themeId', themeId);
      if (tempThemeId) searchParams.set('tempThemeId', tempThemeId);
      if (accountThemeId) searchParams.set('accountThemeId', accountThemeId);

      const response = await fetch(`/api/miniapp/css-cache?${searchParams}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch CSS cache info');
      }

      const result = await response.json();
      return result.data;
    },
    enabled: Boolean(themeId || tempThemeId || accountThemeId),
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });

  // Regenerate CSS mutation
  const regenerateCSS = useMutation({
    mutationFn: async (force: boolean = false) => {
      const response = await fetch('/api/miniapp/css-cache/regenerate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          themeId,
          tempThemeId,
          accountThemeId,
          force,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to regenerate CSS');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch cache info
      queryClient.invalidateQueries({ queryKey });
    },
  });

  // Clear CSS cache mutation
  const clearCache = useMutation({
    mutationFn: async () => {
      const searchParams = new URLSearchParams();
      if (themeId) searchParams.set('themeId', themeId);
      if (tempThemeId) searchParams.set('tempThemeId', tempThemeId);
      if (accountThemeId) searchParams.set('accountThemeId', accountThemeId);

      const response = await fetch(`/api/miniapp/css-cache?${searchParams}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to clear CSS cache');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch cache info
      queryClient.invalidateQueries({ queryKey });
    },
  });

  // Helper functions
  const isConfigChanged = cacheInfo?.configInfo.isChanged ?? false;
  const needsRegeneration = !cacheInfo?.cacheStats.hasCachedCSS || isConfigChanged;
  const hasValidCache = cacheInfo?.validation.isValid ?? false;

  // Format cache age
  const formatCacheAge = useCallback((ageMs?: number): string => {
    if (!ageMs) return 'Unknown';
    
    const minutes = Math.floor(ageMs / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h ago`;
    if (hours > 0) return `${hours}h ${minutes % 60}m ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  }, []);

  // Format file size
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  }, []);

  return {
    // Data
    cacheInfo,
    isLoading,
    error,

    // Status checks
    isConfigChanged,
    needsRegeneration,
    hasValidCache,

    // Actions
    refetch,
    regenerateCSS: regenerateCSS.mutate,
    clearCache: clearCache.mutate,

    // Loading states
    isRegenerating: regenerateCSS.isPending,
    isClearing: clearCache.isPending,

    // Helpers
    formatCacheAge,
    formatFileSize,

    // Computed values
    cacheAge: cacheInfo?.cacheStats.cacheAge 
      ? formatCacheAge(cacheInfo.cacheStats.cacheAge)
      : undefined,
    cssSize: cacheInfo?.cacheStats.cssSize 
      ? formatFileSize(cacheInfo.cacheStats.cssSize)
      : undefined,
    compressionPercent: cacheInfo?.cacheStats.compressionRatio
      ? Math.round((1 - cacheInfo.cacheStats.compressionRatio) * 100)
      : undefined,
  };
}

/**
 * Hook for monitoring CSS cache across multiple themes
 */
export function useCSSCacheMonitor(themes: Array<{
  id: string;
  type: 'theme' | 'account_theme' | 'temp_theme';
}>) {
  const [cacheStates, setCacheStates] = useState<Record<string, CSSCacheInfo>>({});

  // Fetch cache info for all themes
  const queries = themes.map(theme => {
    const params = {
      themeId: theme.type === 'theme' ? theme.id : undefined,
      accountThemeId: theme.type === 'account_theme' ? theme.id : undefined,
      tempThemeId: theme.type === 'temp_theme' ? theme.id : undefined,
    };

    return useCSSCache(params);
  });

  // Aggregate results
  const isLoading = queries.some(q => q.isLoading);
  const hasErrors = queries.some(q => q.error);
  const needsRegeneration = queries.some(q => q.needsRegeneration);
  const totalCSSSize = queries.reduce((total, q) => 
    total + (q.cacheInfo?.cacheStats.cssSize || 0), 0
  );

  return {
    queries,
    isLoading,
    hasErrors,
    needsRegeneration,
    totalCSSSize,
    formatFileSize: queries[0]?.formatFileSize || ((size: number) => `${size} B`),
  };
}

export default useCSSCache;
