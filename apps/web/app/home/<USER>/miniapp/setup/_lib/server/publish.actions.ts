'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { saveFinalTheme } from '~/home/<USER>/miniapp/setup/customization/_lib/server/customization.actions';

export async function publishTheme(
  themeId: string,
  accountSlug: string,
  tempThemeId?: string,
) {
  const supabase = getSupabaseServerClient();

  try {
    let finalThemeId = themeId;

    // If tempThemeId is provided, save final theme first
    if (tempThemeId) {
      const result = await saveFinalTheme(tempThemeId);
      finalThemeId = result.themeId;
    }

    const { error } = await supabase
      .from('account_themes')
      .update({
        is_active: true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', finalThemeId);

    if (error) throw error;

    // Revalidate related paths
    revalidatePath(`/home/<USER>/miniapp`);
    revalidatePath(`/home/<USER>/miniapp/setup`);

    return { themeId: finalThemeId };
  } catch (error) {
    console.error('Error publishing theme:', error);
    throw new Error('Failed to publish theme');
  }
}
