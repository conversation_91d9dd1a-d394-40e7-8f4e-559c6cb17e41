import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { Customer } from '~/home/<USER>/customers/_lib/types';

interface LoadCustomersOptions {
  accountId: string;
  search?: string;
  page?: number;
  limit?: number;
}

export async function loadCustomers({
  accountId,
  search,
  page = 1,
  limit = 10,
}: LoadCustomersOptions) {
  const supabase = getSupabaseServerClient();

  try {
    // Build the query
    let query = supabase
      .from('customer_profiles')
      .select('*', { count: 'exact' })
      .eq('team_account_id', accountId);

    // Add search filter if provided
    if (search && search.trim()) {
      query = query.or(
        `first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%`
      );
    }

    // Add pagination
    const offset = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: customersData, error, count } = await query;

    if (error) {
      console.error('Error loading customers:', error);
      throw error;
    }

    if (!customersData || customersData.length === 0) {
      return {
        data: [],
        total: count || 0,
      };
    }

    // Transform database results to Customer format
    const customers: Customer[] = customersData.map((row) => ({
      id: row.id,
      email: row.email || '',
      name: `${row.first_name || ''} ${row.last_name || ''}`.trim() || row.email,
      phone: row.phone || null,
      created_at: row.created_at || new Date().toISOString(),
      updated_at: row.updated_at || new Date().toISOString(),
      account_id: row.team_account_id,
      is_vip: row.value_tier === 'high',
      total_orders: row.total_orders || 0,
      total_spent: Number(row.total_spent) || 0,
      status: row.customer_status === 'active' ? 'active' : 'inactive',
      avatar_url: row.avatar_url || null,
    }));

    return {
      data: customers,
      total: count || 0,
    };
  } catch (error) {
    console.error('Error loading customers:', error);
    throw new Error(
      `Failed to load customers: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}
