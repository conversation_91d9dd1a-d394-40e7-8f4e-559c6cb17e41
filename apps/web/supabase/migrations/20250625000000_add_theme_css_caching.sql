-- Migration: Add CSS caching and optimization support to theme tables
-- Created: 2025-06-25

-- Add CSS optimization columns to themes table
ALTER TABLE public.themes 
ADD COLUMN IF NOT EXISTS optimized_css TEXT,
ADD COLUMN IF NOT EXISTS css_hash VARCHAR(32),
ADD COLUMN IF NOT EXISTS css_metadata JSONB,
ADD COLUMN IF NOT EXISTS config_md5 VARCHAR(32);

-- Add CSS optimization columns to account_themes table  
ALTER TABLE public.account_themes
ADD COLUMN IF NOT EXISTS optimized_css TEXT,
ADD COLUMN IF NOT EXISTS css_hash VARCHAR(32), 
ADD COLUMN IF NOT EXISTS css_metadata JSONB,
ADD COLUMN IF NOT EXISTS config_md5 VARCHAR(32);

-- Add CSS optimization columns to temp_themes table
ALTER TABLE public.temp_themes
ADD COLUMN IF NOT EXISTS optimized_css TEXT,
ADD COLUMN IF NOT EXISTS css_hash VARCHAR(32),
ADD COLUMN IF NOT EXISTS css_metadata JSONB, 
ADD COLUMN IF NOT EXISTS config_md5 VARCHAR(32);

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_themes_css_hash ON public.themes(css_hash) WHERE css_hash IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_themes_config_md5 ON public.themes(config_md5) WHERE config_md5 IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_account_themes_css_hash ON public.account_themes(css_hash) WHERE css_hash IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_account_themes_config_md5 ON public.account_themes(config_md5) WHERE config_md5 IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_temp_themes_css_hash ON public.temp_themes(css_hash) WHERE css_hash IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_temp_themes_config_md5 ON public.temp_themes(config_md5) WHERE config_md5 IS NOT NULL;

-- Add comments for documentation
COMMENT ON COLUMN public.themes.optimized_css IS 'Minified and optimized CSS generated from Tailwind classes';
COMMENT ON COLUMN public.themes.css_hash IS 'Hash of the optimized CSS for caching';
COMMENT ON COLUMN public.themes.css_metadata IS 'Metadata about CSS generation (stats, classes, etc.)';
COMMENT ON COLUMN public.themes.config_md5 IS 'MD5 hash of the config JSON for change detection';

COMMENT ON COLUMN public.account_themes.optimized_css IS 'Minified and optimized CSS generated from Tailwind classes';
COMMENT ON COLUMN public.account_themes.css_hash IS 'Hash of the optimized CSS for caching';
COMMENT ON COLUMN public.account_themes.css_metadata IS 'Metadata about CSS generation (stats, classes, etc.)';
COMMENT ON COLUMN public.account_themes.config_md5 IS 'MD5 hash of the config JSON for change detection';

COMMENT ON COLUMN public.temp_themes.optimized_css IS 'Minified and optimized CSS generated from Tailwind classes';
COMMENT ON COLUMN public.temp_themes.css_hash IS 'Hash of the optimized CSS for caching';
COMMENT ON COLUMN public.temp_themes.css_metadata IS 'Metadata about CSS generation (stats, classes, etc.)';
COMMENT ON COLUMN public.temp_themes.config_md5 IS 'MD5 hash of the config JSON for change detection';

-- Function to generate MD5 hash of config
CREATE OR REPLACE FUNCTION generate_config_md5(config_data JSONB)
RETURNS VARCHAR(32)
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
  -- Handle NULL input
  IF config_data IS NULL THEN
    RETURN NULL;
  END IF;

  RETURN md5(config_data::text);
END;
$$;

-- Function to auto-update config_md5 when config changes
CREATE OR REPLACE FUNCTION update_config_md5()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Update config_md5 when config changes
  IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.config IS DISTINCT FROM NEW.config) THEN
    NEW.config_md5 = generate_config_md5(NEW.config);
    NEW.updated_at = NOW();
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create triggers for auto-updating config_md5
DROP TRIGGER IF EXISTS trigger_themes_config_md5 ON public.themes;
CREATE TRIGGER trigger_themes_config_md5
  BEFORE INSERT OR UPDATE ON public.themes
  FOR EACH ROW
  EXECUTE FUNCTION update_config_md5();

DROP TRIGGER IF EXISTS trigger_account_themes_config_md5 ON public.account_themes;
CREATE TRIGGER trigger_account_themes_config_md5
  BEFORE INSERT OR UPDATE ON public.account_themes
  FOR EACH ROW
  EXECUTE FUNCTION update_config_md5();

DROP TRIGGER IF EXISTS trigger_temp_themes_config_md5 ON public.temp_themes;
CREATE TRIGGER trigger_temp_themes_config_md5
  BEFORE INSERT OR UPDATE ON public.temp_themes
  FOR EACH ROW
  EXECUTE FUNCTION update_config_md5();

-- Function to check if CSS needs regeneration
CREATE OR REPLACE FUNCTION needs_css_regeneration(
  current_config_md5 VARCHAR(32),
  stored_config_md5 VARCHAR(32),
  stored_css_hash VARCHAR(32)
)
RETURNS BOOLEAN
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
  -- Need regeneration if:
  -- 1. Config MD5 has changed
  -- 2. No CSS hash exists
  -- 3. Config MD5 is null (first time)
  RETURN (
    current_config_md5 IS DISTINCT FROM stored_config_md5 OR
    stored_css_hash IS NULL OR
    stored_config_md5 IS NULL
  );
END;
$$;

-- Update existing records to generate config_md5
UPDATE public.themes 
SET config_md5 = generate_config_md5(config)
WHERE config_md5 IS NULL AND config IS NOT NULL;

UPDATE public.account_themes 
SET config_md5 = generate_config_md5(config)
WHERE config_md5 IS NULL AND config IS NOT NULL;

UPDATE public.temp_themes 
SET config_md5 = generate_config_md5(config)
WHERE config_md5 IS NULL AND config IS NOT NULL;
