-- Migration: Fix theme CSS functions and triggers
-- Created: 2025-06-25

-- Drop existing functions and triggers if they exist
DROP TRIGGER IF EXISTS trigger_themes_config_md5 ON public.themes;
DROP TRIGGER IF EXISTS trigger_account_themes_config_md5 ON public.account_themes;
DROP TRIGGER IF EXISTS trigger_temp_themes_config_md5 ON public.temp_themes;

DROP FUNCTION IF EXISTS update_config_md5();
DROP FUNCTION IF EXISTS generate_config_md5(jsonb);
DROP FUNCTION IF EXISTS needs_css_regeneration(varchar(32), varchar(32), varchar(32));

-- Create MD5 generation function
CREATE OR REPLACE FUNCTION generate_config_md5(config_data jsonb)
RETURNS varchar(32)
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
  -- Generate MD5 hash from JSONB config
  RETURN md5(config_data::text);
END;
$$;

-- Create trigger function to auto-update config_md5
CREATE OR REPLACE FUNCTION update_config_md5()
R<PERSON><PERSON>NS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  -- Update config_md5 when config changes
  IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.config IS DISTINCT FROM NEW.config) THEN
    NEW.config_md5 = generate_config_md5(NEW.config);
    NEW.updated_at = NOW();
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create CSS regeneration check function
CREATE OR REPLACE FUNCTION needs_css_regeneration(
  current_config_md5 varchar(32),
  stored_config_md5 varchar(32),
  stored_css_hash varchar(32)
)
RETURNS boolean
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
  -- Need regeneration if:
  -- 1. Config MD5 has changed
  -- 2. No CSS hash exists
  -- 3. Config MD5 is null (first time)
  RETURN (
    current_config_md5 IS DISTINCT FROM stored_config_md5 OR
    stored_css_hash IS NULL OR
    stored_config_md5 IS NULL
  );
END;
$$;

-- Create triggers for auto-updating config_md5
CREATE TRIGGER trigger_themes_config_md5
  BEFORE INSERT OR UPDATE ON public.themes
  FOR EACH ROW
  EXECUTE FUNCTION update_config_md5();

CREATE TRIGGER trigger_account_themes_config_md5
  BEFORE INSERT OR UPDATE ON public.account_themes
  FOR EACH ROW
  EXECUTE FUNCTION update_config_md5();

CREATE TRIGGER trigger_temp_themes_config_md5
  BEFORE INSERT OR UPDATE ON public.temp_themes
  FOR EACH ROW
  EXECUTE FUNCTION update_config_md5();

-- Update existing records to generate config_md5
UPDATE public.themes 
SET config_md5 = generate_config_md5(config)
WHERE config_md5 IS NULL AND config IS NOT NULL;

UPDATE public.account_themes 
SET config_md5 = generate_config_md5(config)
WHERE config_md5 IS NULL AND config IS NOT NULL;

UPDATE public.temp_themes 
SET config_md5 = generate_config_md5(config)
WHERE config_md5 IS NULL AND config IS NOT NULL;

-- Add comments for documentation
COMMENT ON FUNCTION generate_config_md5(jsonb) IS 'Generate MD5 hash for theme config JSON';
COMMENT ON FUNCTION update_config_md5() IS 'Trigger function to auto-update config_md5 when config changes';
COMMENT ON FUNCTION needs_css_regeneration(varchar(32), varchar(32), varchar(32)) IS 'Check if CSS needs regeneration based on config changes';
