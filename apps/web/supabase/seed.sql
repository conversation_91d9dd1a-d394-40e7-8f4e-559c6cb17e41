-- WEBHOOKS SEED
-- PLEASE NOTE: These webhooks are only for development purposes. Leave them as they are or add new ones.

-- These webhooks are only for development purposes.
-- In production, you should manually create webhooks in the Supabase dashboard (or create a migration to do so).
-- We don't do it because you'll need to manually add your webhook URL and secret key.

-- this webhook will be triggered after deleting an account
DROP TRIGGER IF EXISTS "accounts_teardown" ON "public"."accounts";
create trigger "accounts_teardown"
    after delete
    on "public"."accounts"
    for each row
execute function "supabase_functions"."http_request"(
        'http://host.docker.internal:3000/api/db/webhook',
        'POST',
        '{"Content-Type":"application/json", "X-Supabase-Event-Signature":"WEBHOOKSECRET"}',
        '{}',
        '5000'
                 );

-- this webhook will be triggered after a delete on the subscriptions table
-- which should happen when a user deletes their account (and all their subscriptions)
DROP TRIGGER IF EXISTS "subscriptions_delete" ON "public"."subscriptions";
create trigger "subscriptions_delete"
    after delete
    on "public"."subscriptions"
    for each row
execute function "supabase_functions"."http_request"(
        'http://host.docker.internal:3000/api/db/webhook',
        'POST',
        '{"Content-Type":"application/json", "X-Supabase-Event-Signature":"WEBHOOKSECRET"}',
        '{}',
        '5000'
                 );

-- this webhook will be triggered after every insert on the invitations table
-- which should happen when a user invites someone to their account
DROP TRIGGER IF EXISTS "invitations_insert" ON "public"."invitations";
create trigger "invitations_insert"
    after insert
    on "public"."invitations"
    for each row
execute function "supabase_functions"."http_request"(
        'http://host.docker.internal:3000/api/db/webhook',
        'POST',
        '{"Content-Type":"application/json", "X-Supabase-Event-Signature":"WEBHOOKSECRET"}',
        '{}',
        '5000'
                 );


-- DATA SEED
-- This is a data dump for testing purposes. It should be used to seed the database with data for testing.


--
-- Data for Name: flow_state; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: users; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."users" ("instance_id", "id", "aud", "role", "email", "encrypted_password", "email_confirmed_at",
                            "invited_at", "confirmation_token", "confirmation_sent_at", "recovery_token",
                            "recovery_sent_at", "email_change_token_new", "email_change", "email_change_sent_at",
                            "last_sign_in_at", "raw_app_meta_data", "raw_user_meta_data", "is_super_admin",
                            "created_at", "updated_at", "phone", "phone_confirmed_at", "phone_change",
                            "phone_change_token", "phone_change_sent_at", "email_change_token_current",
                            "email_change_confirm_status", "banned_until", "reauthentication_token",
                            "reauthentication_sent_at", "is_sso_user", "deleted_at", "is_anonymous")
VALUES ('********-0000-0000-0000-********0000', 'b73eb03e-fb7a-424d-84ff-18e2791ce0b4', 'authenticated',
        'authenticated', '<EMAIL>', '$2a$10$b3ZPpU6TU3or30QzrXnZDuATPAx2pPq3JW.sNaneVY3aafMSuR4yi',
        '2024-04-20 08:38:00.860548+00', NULL, '', '2024-04-20 08:37:43.343769+00', '', NULL, '', '', NULL,
        '2024-04-20 08:38:00.93864+00', '{
    "provider": "email",
    "providers": [
      "email"
    ],
    "role": "customer"
  }',
        '{
          "sub": "b73eb03e-fb7a-424d-84ff-18e2791ce0b4",
          "email": "<EMAIL>",
          "email_verified": false,
          "phone_verified": false
        }',
        NULL, '2024-04-20 08:37:43.3385+00', '2024-04-20 08:38:00.942809+00', NULL, NULL, '', '', NULL, '', 0, NULL, '',
        NULL, false, NULL, false),
       ('********-0000-0000-0000-********0000', '31a03e74-1639-45b6-bfa7-77447f1a4762', 'authenticated',
        'authenticated', '<EMAIL>', '$2a$10$NaMVRrI7NyfwP.AfAVWt6O/abulGnf9BBqwa6DqdMwXMvOCGpAnVO',
        '2024-04-20 08:20:38.165331+00', NULL, '', NULL, '', NULL, '', '', NULL, '2024-04-20 09:36:02.521776+00',
        '{
          "provider": "email",
          "providers": [
            "email"
          ],
          "role": "customer"
        }',
        '{
          "sub": "31a03e74-1639-45b6-bfa7-77447f1a4762",
          "email": "<EMAIL>",
          "email_verified": false,
          "phone_verified": false
        }',
        NULL, '2024-04-20 08:20:34.459113+00', '2024-04-20 10:07:48.554125+00', NULL, NULL, '', '', NULL, '', 0, NULL,
        '', NULL, false, NULL, false),
       ('********-0000-0000-0000-********0000', '5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf', 'authenticated',
        'authenticated', '<EMAIL>', '$2a$10$D6arGxWJShy8q4RTW18z7eW0vEm2hOxEUovUCj5f3NblyHfamm5/a',
        '2024-04-20 08:36:37.517993+00', NULL, '', '2024-04-20 08:36:27.639648+00', '', NULL, '', '', NULL,
        '2024-04-20 08:36:37.614337+00', '{
         "provider": "email",
         "providers": [
           "email"
         ],
         "role": "owner"
       }',
        '{
          "sub": "5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf",
          "email": "<EMAIL>",
          "email_verified": false,
          "phone_verified": false
        }',
        NULL, '2024-04-20 08:36:27.630379+00', '2024-04-20 08:36:37.617955+00', NULL, NULL, '', '', NULL, '', 0, NULL,
        '', NULL, false, NULL, false),
       ('********-0000-0000-0000-********0000', '6b83d656-e4ab-48e3-a062-c0c54a427368', 'authenticated',
        'authenticated', '<EMAIL>', '$2a$10$6h/x.AX.6zzphTfDXIJMzuYx13hIYEi/Iods9FXH19J2VxhsLycfa',
        '2024-04-20 08:41:15.376778+00', NULL, '', '2024-04-20 08:41:08.689674+00', '', NULL, '', '', NULL,
        '2024-04-20 08:41:15.484606+00', '{
         "provider": "email",
         "providers": [
           "email"
         ],
         "role": "member"

       }',
        '{
          "sub": "6b83d656-e4ab-48e3-a062-c0c54a427368",
          "email": "<EMAIL>",
          "email_verified": false,
          "phone_verified": false
        }',
        NULL, '2024-04-20 08:41:08.683395+00', '2024-04-20 08:41:15.485494+00', NULL, NULL, '', '', NULL, '', 0, NULL,
        '', NULL, false, NULL, false),
       ('********-0000-0000-0000-********0000', 'c5b930c9-0a76-412e-a836-4bc4849a3270', 'authenticated',
        'authenticated', '<EMAIL>',
        '$2a$10$gzxQw3vaVni8Ke9UVcn6ueWh674.6xImf6/yWYNc23BSeYdE9wmki', '2025-02-24 13:25:11.176987+00', null, '',
        '2025-02-24 13:25:01.649714+00', '', null, '', '', null, '2025-02-24 13:25:11.17957+00',
        '{
          "provider": "email",
          "providers": [
            "email"
          ],
          "role": "super-admin"
        }',
        '{
          "sub": "c5b930c9-0a76-412e-a836-4bc4849a3270",
          "email": "<EMAIL>",
          "email_verified": true,
          "phone_verified": false
        }',
        null, '2025-02-24 13:25:01.646641+00', '2025-02-24 13:25:11.181332+00', null, null, '', '', null
           , '', '0', null, '', null, 'false', null, 'false');

--
-- Data for Name: identities; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."identities" ("provider_id", "user_id", "identity_data", "provider", "last_sign_in_at", "created_at",
                                 "updated_at", "id")
VALUES ('31a03e74-1639-45b6-bfa7-77447f1a4762', '31a03e74-1639-45b6-bfa7-77447f1a4762',
        '{
          "sub": "31a03e74-1639-45b6-bfa7-77447f1a4762",
          "email": "<EMAIL>",
          "email_verified": false,
          "phone_verified": false
        }',
        'email', '2024-04-20 08:20:34.46275+00', '2024-04-20 08:20:34.462773+00', '2024-04-20 08:20:34.462773+00',
        '9bb58bad-24a4-41a8-9742-1b5b4e2d8abd'),
       ('5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf', '5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf',
        '{
          "sub": "5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf",
          "email": "<EMAIL>",
          "email_verified": false,
          "phone_verified": false
        }',
        'email', '2024-04-20 08:36:27.637388+00', '2024-04-20 08:36:27.637409+00', '2024-04-20 08:36:27.637409+00',
        '090598a1-ebba-4879-bbe3-38d517d5066f'),
       ('b73eb03e-fb7a-424d-84ff-18e2791ce0b4', 'b73eb03e-fb7a-424d-84ff-18e2791ce0b4',
        '{
          "sub": "b73eb03e-fb7a-424d-84ff-18e2791ce0b4",
          "email": "<EMAIL>",
          "email_verified": false,
          "phone_verified": false
        }',
        'email', '2024-04-20 08:37:43.342194+00', '2024-04-20 08:37:43.342218+00', '2024-04-20 08:37:43.342218+00',
        '4392e228-a6d8-4295-a7d6-baed50c33e7c'),
       ('6b83d656-e4ab-48e3-a062-c0c54a427368', '6b83d656-e4ab-48e3-a062-c0c54a427368',
        '{
          "sub": "6b83d656-e4ab-48e3-a062-c0c54a427368",
          "email": "<EMAIL>",
          "email_verified": false,
          "phone_verified": false
        }',
        'email', '2024-04-20 08:41:08.687948+00', '2024-04-20 08:41:08.687982+00', '2024-04-20 08:41:08.687982+00',
        'd122aca5-4f29-43f0-b1b1-940b000638db'),
       ('c5b930c9-0a76-412e-a836-4bc4849a3270', 'c5b930c9-0a76-412e-a836-4bc4849a3270',
        '{
          "sub": "c5b930c9-0a76-412e-a836-4bc4849a3270",
          "email": "<EMAIL>",
          "email_verified": true,
          "phone_verified": false
        }',
        'email', '2025-02-24 13:25:01.646641+00', '2025-02-24 13:25:11.181332+00', '2025-02-24 13:25:11.181332+00',
        'c5b930c9-0a76-412e-a836-4bc4849a3270');

--
-- Data for Name: instances; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: sessions; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

--
-- Data for Name: mfa_amr_claims; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: mfa_factors; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: mfa_challenges; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: refresh_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

--
-- Data for Name: sso_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: saml_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: saml_relay_states; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: sso_domains; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: key; Type: TABLE DATA; Schema: pgsodium; Owner: supabase_admin
--


--
-- Data for Name: accounts; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."accounts" ("id", "primary_owner_user_id", "name", "slug", "email", "is_personal_account",
                                 "updated_at", "created_at", "created_by", "updated_by", "picture_url", "public_data")
VALUES ('5deaa894-2094-4da3-b4fd-1fada0809d1c', '5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf', 'ShopOne', 'makerkit', NULL,
        false, NULL, NULL, NULL, NULL, NULL, '{}');

--
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."roles" ("name", "hierarchy_level")
VALUES ('custom-role', 4);

--
-- Data for Name: accounts_memberships; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."accounts_memberships" ("user_id", "account_id", "account_role", "created_at", "updated_at",
                                             "created_by", "updated_by")
VALUES ('31a03e74-1639-45b6-bfa7-77447f1a4762', '5deaa894-2094-4da3-b4fd-1fada0809d1c', 'owner',
        '2024-04-20 08:21:16.802867+00', '2024-04-20 08:21:16.802867+00', NULL, NULL),
       ('5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf', '5deaa894-2094-4da3-b4fd-1fada0809d1c', 'owner',
        '2024-04-20 08:36:44.21028+00', '2024-04-20 08:36:44.21028+00', NULL, NULL),
       ('b73eb03e-fb7a-424d-84ff-18e2791ce0b4', '5deaa894-2094-4da3-b4fd-1fada0809d1c', 'custom-role',
        '2024-04-20 08:38:02.50993+00', '2024-04-20 08:38:02.50993+00', NULL, NULL),
       ('6b83d656-e4ab-48e3-a062-c0c54a427368', '5deaa894-2094-4da3-b4fd-1fada0809d1c', 'member',
        '2024-04-20 08:41:17.833709+00', '2024-04-20 08:41:17.833709+00', NULL, NULL);

-- MFA Factors
INSERT INTO "auth"."mfa_factors" ("id", "user_id", "friendly_name", "factor_type", "status", "created_at", "updated_at",
                                  "secret", "phone", "last_challenged_at")
VALUES ('659e3b57-1128-4d26-8757-f714fd073fc4', 'c5b930c9-0a76-412e-a836-4bc4849a3270', 'iPhone', 'totp', 'verified',
        '2025-02-24 13:23:55.5805+00', '2025-02-24 13:24:32.591999+00', 'NHOHJVGPO3R3LKVPRMNIYLCDMBHUM2SE', null,
        '2025-02-24 13:24:32.563314+00');

--
-- Data for Name: billing_customers; Type: TABLE DATA; Schema: public; Owner: postgres
--


--
-- Data for Name: invitations; Type: TABLE DATA; Schema: public; Owner: postgres
--


--
-- Data for Name: orders; Type: TABLE DATA; Schema: public; Owner: postgres
--


--
-- Data for Name: order_items; Type: TABLE DATA; Schema: public; Owner: postgres
--


--
-- Data for Name: subscriptions; Type: TABLE DATA; Schema: public; Owner: postgres
--


--
-- Data for Name: subscription_items; Type: TABLE DATA; Schema: public; Owner: postgres
--


--
-- Data for Name: buckets; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

--
-- Data for Name: objects; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--


--
-- Data for Name: s3_multipart_uploads; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--


--
-- Data for Name: s3_multipart_uploads_parts; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--


--
-- Data for Name: hooks; Type: TABLE DATA; Schema: supabase_functions; Owner: supabase_functions_admin
--

--
-- Data for Name: secrets; Type: TABLE DATA; Schema: vault; Owner: supabase_admin
--

--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE SET; Schema: auth; Owner: supabase_auth_admin
--

SELECT pg_catalog.setval('"auth"."refresh_tokens_id_seq"', 5, true);


--
-- Name: billing_customers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('"public"."billing_customers_id_seq"', 1, false);


--
-- Name: invitations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('"public"."invitations_id_seq"', 19, true);


--
-- Name: role_permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('"public"."role_permissions_id_seq"', 7, true);


--
-- Name: hooks_id_seq; Type: SEQUENCE SET; Schema: supabase_functions; Owner: supabase_functions_admin
--

SELECT pg_catalog.setval('"supabase_functions"."hooks_id_seq"', 19, true);


-- Now run the branches seed
do
$$
    declare
        v_account_id uuid;
    begin
        -- Get account id for makerkit team
        select id
        into v_account_id
        from public.accounts
        where slug = 'makerkit';

        -- Insert branches for major cities in Vietnam
        insert into public.branches (account_id,
                                     name,
                                     address,
                                     phone,
                                     is_active)
        values
            -- Ho Chi Minh City branches
            (v_account_id,
             'Chi nhánh Quận 1',
             '123 Nguyễn Huệ, Phường Bến Nghé, Quận 1, TP.HCM',
             '028 3822 1234',
             true),
            (v_account_id,
             'Chi nhánh Quận 3',
             '45 Cao Thắng, Phường 2, Quận 3, TP.HCM',
             '028 3833 5678',
             true),
            (v_account_id,
             'Chi nhánh Phú Mỹ Hưng',
             '1234 Nguyễn Văn Linh, Phường Tân Phong, Quận 7, TP.HCM',
             '028 3844 9012',
             true),

            -- Hanoi branches
            (v_account_id,
             'Chi nhánh Hoàn Kiếm',
             '67 Hàng Bông, Phường Hàng Bông, Quận Hoàn Kiếm, Hà Nội',
             '024 3855 3456',
             true),
            (v_account_id,
             'Chi nhánh Cầu Giấy',
             '89 Xuân Thủy, Phường Dịch Vọng Hậu, Quận Cầu Giấy, Hà Nội',
             '024 3866 7890',
             true),

            -- Da Nang branch
            (v_account_id,
             'Chi nhánh Đà Nẵng',
             '123 Nguyễn Văn Linh, Phường Nam Dương, Quận Hải Châu, Đà Nẵng',
             '0236 3877 1234',
             true),

            -- Can Tho branch
            (v_account_id,
             'Chi nhánh Cần Thơ',
             '456 Nguyễn Văn Cừ, Phường An Hòa, Quận Ninh Kiều, Cần Thơ',
             '0292 3888 5678',
             true),

            -- Nha Trang branch
            (v_account_id,
             'Chi nhánh Nha Trang',
             '789 Trần Phú, Phường Lộc Thọ, TP. Nha Trang, Khánh Hòa',
             '0258 3899 9012',
             true);
    end;
$$;

-- Now seed the categories with parent-child relationships
do
$$
    declare
        v_account_id      uuid;
        v_mon_khai_vi_id  uuid;
        v_mon_chinh_id    uuid;
        v_mon_nuong_id    uuid;
        v_mon_lau_id      uuid;
        v_mon_chien_id    uuid;
        v_mon_chay_id     uuid;
        v_trang_mieng_id  uuid;
        v_do_uong_id      uuid;
        v_combo_set_id    uuid;
        v_mon_dac_biet_id uuid;
    begin
        -- Get account id for makerkit team
        select id
        into v_account_id
        from public.accounts
        where slug = 'makerkit';

        -- Insert top-level categories and store their IDs
        insert into public.categories (account_id,
                                       name,
                                       description)
        values
            -- Top-level: Món khai vị
            (v_account_id,
             'Món khai vị',
             'Các món ăn nhẹ khai vị như gỏi, salad, súp')
        returning id into v_mon_khai_vi_id;

        -- Top-level: Món chính
        insert into public.categories (account_id,
                                       name,
                                       description)
        values (v_account_id,
                'Món chính',
                'Các món ăn chính như cơm, phở, bún, mì')
        returning id into v_mon_chinh_id;

        -- Top-level: Món nướng
        insert into public.categories (account_id,
                                       name,
                                       description)
        values (v_account_id,
                'Món nướng',
                'Các món nướng như thịt nướng, hải sản nướng, rau củ nướng')
        returning id into v_mon_nuong_id;

        -- Top-level: Món lẩu
        insert into public.categories (account_id,
                                       name,
                                       description)
        values (v_account_id,
                'Món lẩu',
                'Các loại lẩu như lẩu thái, lẩu hải sản, lẩu nấm')
        returning id into v_mon_lau_id;

        -- Top-level: Món chiên
        insert into public.categories (account_id,
                                       name,
                                       description)
        values (v_account_id,
                'Món chiên',
                'Các món chiên như cơm chiên, mì xào, đồ chiên giòn')
        returning id into v_mon_chien_id;

        -- Top-level: Món chay
        insert into public.categories (account_id,
                                       name,
                                       description)
        values (v_account_id,
                'Món chay',
                'Các món ăn chay như rau xào, đậu hũ, nấm các loại')
        returning id into v_mon_chay_id;

        -- Top-level: Tráng miệng
        insert into public.categories (account_id,
                                       name,
                                       description)
        values (v_account_id,
                'Tráng miệng',
                'Các món tráng miệng như chè, bánh ngọt, trái cây')
        returning id into v_trang_mieng_id;

        -- Top-level: Đồ uống
        insert into public.categories (account_id,
                                       name,
                                       description)
        values (v_account_id,
                'Đồ uống',
                'Nước ngọt, nước ép, sinh tố, trà, cà phê')
        returning id into v_do_uong_id;

        -- Top-level: Combo set
        insert into public.categories (account_id,
                                       name,
                                       description)
        values (v_account_id,
                'Combo set',
                'Các set combo món ăn được thiết kế sẵn')
        returning id into v_combo_set_id;

        -- Top-level: Món đặc biệt
        insert into public.categories (account_id,
                                       name,
                                       description)
        values (v_account_id,
                'Món đặc biệt',
                'Các món đặc biệt theo mùa hoặc đầu bếp đề xuất')
        returning id into v_mon_dac_biet_id;

        -- Insert subcategories under "Món khai vị"
        insert into public.categories (account_id,
                                       name,
                                       description,
                                       parent_id)
        values (v_account_id,
                'Gỏi',
                'Các món gỏi như gỏi gà, gỏi ngó sen',
                v_mon_khai_vi_id),
               (v_account_id,
                'Salad',
                'Các món salad như salad rau củ, salad cá ngừ',
                v_mon_khai_vi_id),
               (v_account_id,
                'Súp',
                'Các món súp như súp cua, súp nấm',
                v_mon_khai_vi_id);

        -- Insert subcategories under "Món chính"
        insert into public.categories (account_id,
                                       name,
                                       description,
                                       parent_id)
        values (v_account_id,
                'Cơm',
                'Các món cơm như cơm tấm, cơm gà',
                v_mon_chinh_id),
               (v_account_id,
                'Phở',
                'Các món phở như phở bò, phở gà',
                v_mon_chinh_id),
               (v_account_id,
                'Bún',
                'Các món bún như bún bò, bún chả',
                v_mon_chinh_id),
               (v_account_id,
                'Mì',
                'Các món mì như mì hoành thánh, mì bò',
                v_mon_chinh_id);

        -- Insert subcategories under "Món nướng"
        insert into public.categories (account_id,
                                       name,
                                       description,
                                       parent_id)
        values (v_account_id,
                'Thịt nướng',
                'Các món thịt nướng như thịt bò nướng, thịt heo nướng',
                v_mon_nuong_id),
               (v_account_id,
                'Hải sản nướng',
                'Các món hải sản nướng như tôm nướng, mực nướng',
                v_mon_nuong_id),
               (v_account_id,
                'Rau củ nướng',
                'Các món rau củ nướng như nấm nướng, cà tím nướng',
                v_mon_nuong_id);

        -- Insert subcategories under "Món lẩu"
        insert into public.categories (account_id,
                                       name,
                                       description,
                                       parent_id)
        values (v_account_id,
                'Lẩu Thái',
                'Lẩu Thái chua cay',
                v_mon_lau_id),
               (v_account_id,
                'Lẩu hải sản',
                'Lẩu hải sản tươi ngon',
                v_mon_lau_id),
               (v_account_id,
                'Lẩu nấm',
                'Lẩu nấm thanh đạm',
                v_mon_lau_id);

        -- Insert subcategories under "Món chiên"
        insert into public.categories (account_id,
                                       name,
                                       description,
                                       parent_id)
        values (v_account_id,
                'Cơm chiên',
                'Các món cơm chiên như cơm chiên dương châu, cơm chiên hải sản',
                v_mon_chien_id),
               (v_account_id,
                'Mì xào',
                'Các món mì xào như mì xào bò, mì xào hải sản',
                v_mon_chien_id),
               (v_account_id,
                'Đồ chiên giòn',
                'Các món chiên giòn như gà rán, khoai tây chiên',
                v_mon_chien_id);

        -- Insert subcategories under "Món chay"
        insert into public.categories (account_id,
                                       name,
                                       description,
                                       parent_id)
        values (v_account_id,
                'Rau xào',
                'Các món rau xào chay',
                v_mon_chay_id),
               (v_account_id,
                'Đậu hũ',
                'Các món đậu hũ chay',
                v_mon_chay_id),
               (v_account_id,
                'Nấm các loại',
                'Các món nấm chay',
                v_mon_chay_id);

        -- Insert subcategories under "Tráng miệng"
        insert into public.categories (account_id,
                                       name,
                                       description,
                                       parent_id)
        values (v_account_id,
                'Chè',
                'Các món chè như chè ba màu, chè đậu đỏ',
                v_trang_mieng_id),
               (v_account_id,
                'Bánh ngọt',
                'Các món bánh ngọt như bánh flan, bánh kem',
                v_trang_mieng_id),
               (v_account_id,
                'Trái cây',
                'Các loại trái cây tươi',
                v_trang_mieng_id);

        -- Insert subcategories under "Đồ uống"
        insert into public.categories (account_id,
                                       name,
                                       description,
                                       parent_id)
        values (v_account_id,
                'Nước ngọt',
                'Các loại nước ngọt như Coca, Pepsi',
                v_do_uong_id),
               (v_account_id,
                'Nước ép',
                'Các loại nước ép như nước ép cam, nước ép dưa hấu',
                v_do_uong_id),
               (v_account_id,
                'Sinh tố',
                'Các loại sinh tố như sinh tố xoài, sinh tố dâu',
                v_do_uong_id),
               (v_account_id,
                'Trà',
                'Các loại trà như trà xanh, trà sữa',
                v_do_uong_id),
               (v_account_id,
                'Cà phê',
                'Các loại cà phê như cà phê đen, cà phê sữa',
                v_do_uong_id);

        -- Insert subcategories under "Combo set"
        insert into public.categories (account_id,
                                       name,
                                       description,
                                       parent_id)
        values (v_account_id,
                'Combo gia đình',
                'Combo dành cho gia đình 4-6 người',
                v_combo_set_id),
               (v_account_id,
                'Combo đôi',
                'Combo dành cho 2 người',
                v_combo_set_id);

        -- Insert subcategories under "Món đặc biệt"
        insert into public.categories (account_id,
                                       name,
                                       description,
                                       parent_id)
        values (v_account_id,
                'Món theo mùa',
                'Các món đặc biệt theo mùa',
                v_mon_dac_biet_id),
               (v_account_id,
                'Đầu bếp đề xuất',
                'Các món do đầu bếp đề xuất',
                v_mon_dac_biet_id);
    end;
$$;


-- Seed Products (Không thay đổi)
do
$$
    declare
        v_account_id     uuid;
        v_mon_khai_vi_id uuid;
        v_mon_chinh_id   uuid;
        v_do_uong_id     uuid;
        v_trang_mieng_id uuid;
    begin
        select id
        into v_account_id
        from public.accounts
        where slug = 'makerkit';

        select id
        into v_mon_khai_vi_id
        from public.categories
        where name = 'Món khai vị'
          and account_id = v_account_id;

        select id
        into v_mon_chinh_id
        from public.categories
        where name = 'Món chính'
          and account_id = v_account_id;

        select id
        into v_do_uong_id
        from public.categories
        where name = 'Đồ uống'
          and account_id = v_account_id;

        select id
        into v_trang_mieng_id
        from public.categories
        where name = 'Tráng miệng'
          and account_id = v_account_id;

        insert into public.products (account_id,
                                     category_id,
                                     name,
                                     description,
                                     type,
                                     compare_at_price,
                                     price,
                                     sku,
                                     barcode,
                                     status,
                                     weight,
                                     dimensions,
                                     tax_rate,
                                     image_url,
                                     image_urls,
                                     created_at,
                                     created_by)
        values (v_account_id,
                v_mon_khai_vi_id,
                'Gỏi ngó sen tôm thịt',
                'Gỏi ngó sen giòn với tôm và thịt heo, trộn nước mắm chua ngọt',
                'physical',
                65000,
                55000,
                'GOI-NSTT-001',
                '*************',
                'active',
                0.3,
                '{
                  "length": 20,
                  "width": 15,
                  "height": 5
                }',
                10,
                'https://zalo-miniapp.github.io/zaui-market/dummy/product/apples.png',
                ARRAY ['https://zalo-miniapp.github.io/zaui-market/dummy/product/apples.png'],
                now(),
                '31a03e74-1639-45b6-bfa7-77447f1a4762'::uuid),
               (v_account_id,
                v_mon_khai_vi_id,
                'Súp cua',
                'Súp cua thơm ngon với thịt cua tươi và trứng gà',
                'physical',
                45000,
                40000,
                'SUP-CUA-001',
                '*************',
                'active',
                0.25,
                '{
                  "length": 15,
                  "width": 15,
                  "height": 10
                }',
                10,
                'https://zalo-miniapp.github.io/zaui-market/dummy/product/creamy.png',
                ARRAY ['https://zalo-miniapp.github.io/zaui-market/dummy/product/creamy.png'],
                now(),
                '31a03e74-1639-45b6-bfa7-77447f1a4762'::uuid),
               (v_account_id,
                v_mon_chinh_id,
                'Phở bò tái',
                'Phở bò tái với nước dùng thơm ngon và bánh phở mềm',
                'physical',
                85000,
                75000,
                'PHO-BO-001',
                '*************',
                'active',
                0.5,
                '{
                  "length": 25,
                  "width": 20,
                  "height": 10
                }',
                10,
                'https://zalo-miniapp.github.io/zaui-market/dummy/product/pans.png',
                ARRAY ['https://zalo-miniapp.github.io/zaui-market/dummy/product/pans.png'],
                now(),
                '5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf'::uuid),
               (v_account_id,
                v_mon_chinh_id,
                'Cơm tấm sườn nướng',
                'Cơm tấm với sườn nướng thơm lừng và nước mắm đặc trưng',
                'physical',
                70000,
                60000,
                'COM-TAM-001',
                '*************',
                'active',
                0.4,
                '{
                  "length": 20,
                  "width": 15,
                  "height": 5
                }',
                10,
                'https://zalo-miniapp.github.io/zaui-market/dummy/product/danisa.png',
                ARRAY ['https://zalo-miniapp.github.io/zaui-market/dummy/product/danisa.png'],
                now(),
                '5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf'::uuid),
               (v_account_id,
                v_do_uong_id,
                'Trà sữa trân châu',
                'Trà sữa thơm ngon với trân châu dai giòn',
                'physical',
                40000,
                35000,
                'TRA-SUA-001',
                '*************',
                'active',
                0.3,
                '{
                  "length": 10,
                  "width": 10,
                  "height": 20
                }',
                10,
                'https://zalo-miniapp.github.io/zaui-market/dummy/product/milk.png',
                ARRAY ['https://zalo-miniapp.github.io/zaui-market/dummy/product/milk.png'],
                now(),
                '6b83d656-e4ab-48e3-a062-c0c54a427368'::uuid),
               (v_account_id,
                v_trang_mieng_id,
                'Chè ba màu',
                'Chè ba màu ngọt mát với đậu xanh, đậu đỏ và nước cốt dừa',
                'physical',
                30000,
                25000,
                'CHE-3M-001',
                '*************',
                'active',
                0.2,
                '{
                  "length": 15,
                  "width": 15,
                  "height": 10
                }',
                10,
                'https://zalo-miniapp.github.io/zaui-market/dummy/product/creamy.png',
                ARRAY ['https://zalo-miniapp.github.io/zaui-market/dummy/product/creamy.png'],
                now(),
                '6b83d656-e4ab-48e3-a062-c0c54a427368'::uuid);
    end;
$$;

-- Seed Branch Products
do
$$
    declare
        v_account_id         uuid;
        v_branch_quan1_id    uuid;
        v_branch_hoankiem_id uuid;
        v_branch_danang_id   uuid;
    begin
        select id
        into v_account_id
        from public.accounts
        where slug = 'makerkit';

        select id
        into v_branch_quan1_id
        from public.branches
        where name = 'Chi nhánh Quận 1'
          and account_id = v_account_id;

        select id
        into v_branch_hoankiem_id
        from public.branches
        where name = 'Chi nhánh Hoàn Kiếm'
          and account_id = v_account_id;

        select id
        into v_branch_danang_id
        from public.branches
        where name = 'Chi nhánh Đà Nẵng'
          and account_id = v_account_id;

        insert into public.branch_products (branch_id,
                                            product_id,
                                            is_active,
                                            created_at,
                                            created_by)
        select v_branch_quan1_id,
               id,
               true,
               now(),
               '31a03e74-1639-45b6-bfa7-77447f1a4762'::uuid
        from public.products
        where account_id = v_account_id
        union all
        select v_branch_hoankiem_id,
               id,
               true,
               now(),
               '5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf'::uuid
        from public.products
        where account_id = v_account_id
        union all
        select v_branch_danang_id,
               id,
               true,
               now(),
               '6b83d656-e4ab-48e3-a062-c0c54a427368'::uuid
        from public.products
        where account_id = v_account_id;
    end;
$$;

-- Seed Inventory
do
$$
    declare
        v_account_id         uuid;
        v_branch_quan1_id    uuid;
        v_branch_hoankiem_id uuid;
        v_branch_danang_id   uuid;
    begin
        select id
        into v_account_id
        from public.accounts
        where slug = 'makerkit';

        select id
        into v_branch_quan1_id
        from public.branches
        where name = 'Chi nhánh Quận 1'
          and account_id = v_account_id;

        select id
        into v_branch_hoankiem_id
        from public.branches
        where name = 'Chi nhánh Hoàn Kiếm'
          and account_id = v_account_id;

        select id
        into v_branch_danang_id
        from public.branches
        where name = 'Chi nhánh Đà Nẵng'
          and account_id = v_account_id;

        insert into public.inventory (branch_id,
                                      product_id,
                                      attribute_id,
                                      stock,
                                      reserved_stock,
                                      created_at,
                                      created_by)
        select v_branch_quan1_id,
               id,
               NULL::uuid, -- Sử dụng NULL rõ ràng và ép kiểu thành uuid
               50,
               0,
               now(),
               '31a03e74-1639-45b6-bfa7-77447f1a4762'::uuid
        from public.products
        where account_id = v_account_id
        union all
        select v_branch_hoankiem_id,
               id,
               NULL::uuid,
               30,
               0,
               now(),
               '5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf'::uuid
        from public.products
        where account_id = v_account_id
        union all
        select v_branch_danang_id,
               id,
               NULL::uuid,
               20,
               0,
               now(),
               '6b83d656-e4ab-48e3-a062-c0c54a427368'::uuid
        from public.products
        where account_id = v_account_id;
    end;
$$;

-- Seed Customer Orders and Items with more comprehensive data
do
$$
    declare
        v_account_id         uuid;
        v_customer_id1      uuid;
        v_customer_id2      uuid;
        v_customer_id3      uuid;
        v_branch_quan1_id   uuid;
        v_branch_quan3_id   uuid;
        v_branch_hoankiem_id uuid;
        v_branch_danang_id  uuid;
        v_pho_bo_id         uuid;
        v_com_tam_id        uuid;
        v_goi_ngo_sen_id    uuid;
        v_sup_cua_id        uuid;
        v_tra_sua_id        uuid;
        v_che_ba_mau_id     uuid;
        v_order_id          uuid;
    begin
        -- Get account ID
        select id
        into v_account_id
        from public.accounts
        where slug = 'makerkit';

        -- Get customer IDs
        select id
        into v_customer_id1
        from auth.users
        where email = '<EMAIL>';

        select id
        into v_customer_id2
        from auth.users
        where email = '<EMAIL>';

        select id
        into v_customer_id3
        from auth.users
        where email = '<EMAIL>';

        -- Get branch IDs
        select id
        into v_branch_quan1_id
        from public.branches
        where name = 'Chi nhánh Quận 1'
          and account_id = v_account_id;

        select id
        into v_branch_quan3_id
        from public.branches
        where name = 'Chi nhánh Quận 3'
          and account_id = v_account_id;

        select id
        into v_branch_hoankiem_id
        from public.branches
        where name = 'Chi nhánh Hoàn Kiếm'
          and account_id = v_account_id;

        select id
        into v_branch_danang_id
        from public.branches
        where name = 'Chi nhánh Đà Nẵng'
          and account_id = v_account_id;

        -- Get product IDs
        select id
        into v_pho_bo_id
        from public.products
        where name = 'Phở bò tái'
          and account_id = v_account_id;

        select id
        into v_com_tam_id
        from public.products
        where name = 'Cơm tấm sườn nướng'
          and account_id = v_account_id;

        select id
        into v_goi_ngo_sen_id
        from public.products
        where name = 'Gỏi ngó sen tôm thịt'
          and account_id = v_account_id;

        select id
        into v_sup_cua_id
        from public.products
        where name = 'Súp cua'
          and account_id = v_account_id;

        select id
        into v_tra_sua_id
        from public.products
        where name = 'Trà sữa trân châu'
          and account_id = v_account_id;

        select id
        into v_che_ba_mau_id
        from public.products
        where name = 'Chè ba màu'
          and account_id = v_account_id;

        -- Create customer activities for tracking
        insert into public.customer_activities (customer_id, account_id, action, details, created_at)
        values
            (v_customer_id1, v_account_id, 'account_login', jsonb_build_object('device', 'mobile', 'location', 'Ho Chi Minh City'), now() - interval '5 days'),
            (v_customer_id1, v_account_id, 'view_product', jsonb_build_object('product_id', v_pho_bo_id, 'product_name', 'Phở bò tái'), now() - interval '5 days'),
            (v_customer_id1, v_account_id, 'add_to_cart', jsonb_build_object('product_id', v_pho_bo_id, 'product_name', 'Phở bò tái', 'quantity', 1), now() - interval '5 days'),
            (v_customer_id1, v_account_id, 'view_product', jsonb_build_object('product_id', v_tra_sua_id, 'product_name', 'Trà sữa trân châu'), now() - interval '5 days'),
            (v_customer_id1, v_account_id, 'add_to_cart', jsonb_build_object('product_id', v_tra_sua_id, 'product_name', 'Trà sữa trân châu', 'quantity', 1), now() - interval '5 days'),
            (v_customer_id1, v_account_id, 'checkout', jsonb_build_object('total_items', 2, 'total_amount', 110000), now() - interval '5 days'),
            (v_customer_id2, v_account_id, 'account_login', jsonb_build_object('device', 'desktop', 'location', 'Hanoi'), now() - interval '3 days'),
            (v_customer_id2, v_account_id, 'view_product', jsonb_build_object('product_id', v_com_tam_id, 'product_name', 'Cơm tấm sườn nướng'), now() - interval '3 days'),
            (v_customer_id2, v_account_id, 'add_to_cart', jsonb_build_object('product_id', v_com_tam_id, 'product_name', 'Cơm tấm sườn nướng', 'quantity', 2), now() - interval '3 days'),
            (v_customer_id2, v_account_id, 'checkout', jsonb_build_object('total_items', 2, 'total_amount', 120000), now() - interval '3 days');

        -- ORDER 1: Completed order with multiple items (Customer 1 at Branch Quận 1)
        insert into public.customer_orders (account_id,
                                            customer_id,
                                            branch_id,
                                            total_amount,
                                            payment_method,
                                            status,
                                            webhook_processed,
                                            webhook_data,
                                            created_at,
                                            created_by,
                                            order_code)
        values (v_account_id,
                v_customer_id1,
                v_branch_quan1_id,
                110000,
                'cash',
                'completed',
                true,
                jsonb_build_object('payment_id', 'PAY-123456', 'transaction_id', 'TXN-789012', 'payment_time', '2024-04-18T14:30:00Z'),
                now() - interval '5 days',
                v_customer_id1,
                'ORD-123456')
        returning id into v_order_id;

        insert into public.customer_order_items (order_id,
                                                 product_id,
                                                 attribute_id,
                                                 quantity,
                                                 price,
                                                 created_at,
                                                 created_by)
        values (v_order_id,
                v_pho_bo_id,
                NULL::uuid,
                1,
                75000,
                now() - interval '5 days',
                v_customer_id1),
               (v_order_id,
                v_tra_sua_id,
                NULL::uuid,
                1,
                35000,
                now() - interval '5 days',
                v_customer_id1);

        -- ORDER 2: Pending order (Customer 1 at Branch Quận 1)
        insert into public.customer_orders (account_id,
                                            customer_id,
                                            branch_id,
                                            total_amount,
                                            payment_method,
                                            status,
                                            webhook_processed,
                                            webhook_data,
                                            created_at,
                                            created_by,
                                            order_code)
        values (v_account_id,
                v_customer_id1,
                v_branch_quan1_id,
                60000,
                'card',
                'pending',
                false,
                jsonb_build_object('payment_id', 'PAY-234567', 'payment_status', 'pending'),
                now() - interval '1 day',
                v_customer_id1,
                'ORD-234567')
        returning id into v_order_id;

        insert into public.customer_order_items (order_id,
                                                 product_id,
                                                 attribute_id,
                                                 quantity,
                                                 price,
                                                 created_at,
                                                 created_by)
        values (v_order_id,
                v_com_tam_id,
                NULL::uuid,
                1,
                60000,
                now() - interval '1 day',
                v_customer_id1);

        -- ORDER 3: Completed order with multiple items (Customer 2 at Branch Quận 3)
        insert into public.customer_orders (account_id,
                                            customer_id,
                                            branch_id,
                                            total_amount,
                                            payment_method,
                                            status,
                                            webhook_processed,
                                            webhook_data,
                                            created_at,
                                            created_by,
                                            order_code)
        values (v_account_id,
                v_customer_id2,
                v_branch_quan3_id,
                120000,
                'momo',
                'completed',
                true,
                jsonb_build_object('payment_id', 'PAY-345678', 'transaction_id', 'TXN-901234', 'payment_time', '2024-04-20T10:15:00Z'),
                now() - interval '3 days',
                v_customer_id2,
                'ORD-345678')
        returning id into v_order_id;

        insert into public.customer_order_items (order_id,
                                                 product_id,
                                                 attribute_id,
                                                 quantity,
                                                 price,
                                                 created_at,
                                                 created_by)
        values (v_order_id,
                v_com_tam_id,
                NULL::uuid,
                2,
                60000,
                now() - interval '3 days',
                v_customer_id2);

        -- ORDER 4: Processing order (Customer 3 at Branch Hoàn Kiếm)
        insert into public.customer_orders (account_id,
                                            customer_id,
                                            branch_id,
                                            total_amount,
                                            payment_method,
                                            status,
                                            webhook_processed,
                                            webhook_data,
                                            created_at,
                                            created_by,
                                            order_code)
        values (v_account_id,
                v_customer_id3,
                v_branch_hoankiem_id,
                170000,
                'zalopay',
                'processing',
                true,
                jsonb_build_object('payment_id', 'PAY-456789', 'transaction_id', 'TXN-012345', 'payment_time', '2024-04-22T09:45:00Z'),
                now() - interval '1 day',
                v_customer_id3,
                'ORD-456789')
        returning id into v_order_id;

        insert into public.customer_order_items (order_id,
                                                 product_id,
                                                 attribute_id,
                                                 quantity,
                                                 price,
                                                 created_at,
                                                 created_by)
        values (v_order_id,
                v_goi_ngo_sen_id,
                NULL::uuid,
                1,
                55000,
                now() - interval '1 day',
                v_customer_id3),
               (v_order_id,
                v_pho_bo_id,
                NULL::uuid,
                1,
                75000,
                now() - interval '1 day',
                v_customer_id3),
               (v_order_id,
                v_tra_sua_id,
                NULL::uuid,
                1,
                35000,
                now() - interval '1 day',
                v_customer_id3),
               (v_order_id,
                v_che_ba_mau_id,
                NULL::uuid,
                1,
                25000,
                now() - interval '1 day',
                v_customer_id3);

        -- ORDER 5: Cancelled order (Customer 2 at Branch Đà Nẵng)
        insert into public.customer_orders (account_id,
                                            customer_id,
                                            branch_id,
                                            total_amount,
                                            payment_method,
                                            status,
                                            webhook_processed,
                                            webhook_data,
                                            created_at,
                                            created_by,
                                            order_code)
        values (v_account_id,
                v_customer_id2,
                v_branch_danang_id,
                95000,
                'card',
                'cancelled',
                true,
                jsonb_build_object('payment_id', 'PAY-567890', 'cancellation_reason', 'Customer requested', 'cancelled_at', '2024-04-21T16:30:00Z'),
                now() - interval '2 days',
                v_customer_id2,
                'ORD-567890')
        returning id into v_order_id;

        insert into public.customer_order_items (order_id,
                                                 product_id,
                                                 attribute_id,
                                                 quantity,
                                                 price,
                                                 created_at,
                                                 created_by)
        values (v_order_id,
                v_sup_cua_id,
                NULL::uuid,
                1,
                40000,
                now() - interval '2 days',
                v_customer_id2),
               (v_order_id,
                v_goi_ngo_sen_id,
                NULL::uuid,
                1,
                55000,
                now() - interval '2 days',
                v_customer_id2);

        -- ORDER 6: Delivered order (Customer 1 at Branch Quận 1) - Today
        insert into public.customer_orders (account_id,
                                            customer_id,
                                            branch_id,
                                            total_amount,
                                            payment_method,
                                            status,
                                            webhook_processed,
                                            webhook_data,
                                            created_at,
                                            created_by,
                                            order_code)
        values (v_account_id,
                v_customer_id1,
                v_branch_quan1_id,
                135000,
                'momo',
                'delivered',
                true,
                jsonb_build_object('payment_id', 'PAY-678901', 'transaction_id', 'TXN-123456', 'payment_time', '2024-04-23T11:00:00Z', 'delivery_time', '2024-04-23T11:45:00Z'),
                now() - interval '6 hours',
                v_customer_id1,
                'ORD-678901')
        returning id into v_order_id;

        insert into public.customer_order_items (order_id,
                                                 product_id,
                                                 attribute_id,
                                                 quantity,
                                                 price,
                                                 created_at,
                                                 created_by)
        values (v_order_id,
                v_pho_bo_id,
                NULL::uuid,
                1,
                75000,
                now() - interval '6 hours',
                v_customer_id1),
               (v_order_id,
                v_tra_sua_id,
                NULL::uuid,
                1,
                35000,
                now() - interval '6 hours',
                v_customer_id1),
               (v_order_id,
                v_che_ba_mau_id,
                NULL::uuid,
                1,
                25000,
                now() - interval '6 hours',
                v_customer_id1);
    end;
$$;

-- Seed 100 customers for makerkit team
do
$$
    declare
        v_account_id uuid;
        v_user_id uuid;
        v_zalo_id text;
        v_name text;
        v_email text;
        v_picture text;
        v_cities text[] := ARRAY['Ho Chi Minh City', 'Hanoi', 'Da Nang', 'Can Tho', 'Nha Trang', 'Hue', 'Hai Phong', 'Vung Tau', 'Quy Nhon', 'Buon Ma Thuot'];
        v_districts text[] := ARRAY['District 1', 'District 2', 'District 3', 'District 4', 'District 5', 'District 6', 'District 7', 'District 8', 'District 9', 'District 10'];
        v_streets text[] := ARRAY['Nguyen Hue', 'Le Loi', 'Dong Khoi', 'Nguyen Van Linh', 'Pham Ngu Lao', 'Bui Vien', 'Vo Van Tan', 'Tran Hung Dao', 'Le Thanh Ton', 'Hai Ba Trung'];
        v_first_names text[] := ARRAY['Nguyen', 'Tran', 'Le', 'Pham', 'Hoang', 'Huynh', 'Vu', 'Vo', 'Dang', 'Bui', 'Do', 'Ho', 'Ngo', 'Duong', 'Ly'];
        v_middle_names text[] := ARRAY['Van', 'Thi', 'Huu', 'Duc', 'Minh', 'Quoc', 'Dinh', 'Manh', 'Cong', 'Thanh', 'Tuan', 'Quang', 'Anh', 'Thu', 'Kim'];
        v_last_names text[] := ARRAY['An', 'Binh', 'Cuong', 'Dung', 'Giang', 'Hai', 'Hung', 'Khanh', 'Lam', 'Minh', 'Nam', 'Phuc', 'Quyen', 'Son', 'Thao', 'Trang', 'Tuan', 'Vy', 'Xuan', 'Yen'];
        v_phone_prefixes text[] := ARRAY['090', '091', '092', '093', '094', '095', '096', '097', '098', '099', '070', '076', '077', '078', '079', '081', '082', '083', '084', '085'];
        v_random_number int;
        v_address text;
        v_phone text;
        v_created_at timestamp;
    begin
        -- Get account ID for makerkit team
        select id
        into v_account_id
        from public.accounts
        where slug = 'makerkit';

        -- Create 100 customers
        for i in 1..100 loop
            -- Generate random Zalo ID (8-10 digits)
            v_random_number := floor(random() * 9********) + 1********;
            v_zalo_id := i || v_random_number::text;

            -- Generate random name
            v_name := v_first_names[floor(random() * array_length(v_first_names, 1)) + 1] || ' ' ||
                     v_middle_names[floor(random() * array_length(v_middle_names, 1)) + 1] || ' ' ||
                     v_last_names[floor(random() * array_length(v_last_names, 1)) + 1];

            -- Generate unique email based on Zalo ID
            v_email := v_zalo_id || '@zalo.user';

            -- Generate random profile picture URL
            v_picture := 'https://api.dicebear.com/7.x/avataaars/svg?seed=' || v_zalo_id;

            -- Generate random address
            v_address := floor(random() * 100 + 1)::text || ' ' ||
                        v_streets[floor(random() * array_length(v_streets, 1)) + 1] || ', ' ||
                        v_districts[floor(random() * array_length(v_districts, 1)) + 1] || ', ' ||
                        v_cities[floor(random() * array_length(v_cities, 1)) + 1];

            -- Generate random phone number
            v_phone := v_phone_prefixes[floor(random() * array_length(v_phone_prefixes, 1)) + 1] ||
                      lpad(floor(random() * ********)::text, 7, '0');

            -- Generate random creation date (within the last 90 days)
            v_created_at := now() - (random() * interval '90 days');

            -- Create user in auth.users
            insert into auth.users (instance_id, id, aud, role, email, encrypted_password, email_confirmed_at,
                                   raw_app_meta_data, raw_user_meta_data, created_at, updated_at, phone, phone_confirmed_at)
            values ('********-0000-0000-0000-********0000', gen_random_uuid(), 'authenticated', 'authenticated',
                    v_email, '$2a$10$NaMVRrI7NyfwP.AfAVWt6O/abulGnf9BBqwa6DqdMwXMvOCGpAnVO', v_created_at,
                    jsonb_build_object(
                        'provider', 'zalo',
                        'providers', array['zalo'],
                        'role', 'customer'
                    ),
                    jsonb_build_object(
                        'zalo_id', v_zalo_id,
                        'name', v_name,
                        'avatar_url', v_picture,
                        'account_id', v_account_id,
                        'address', v_address,
                        'sub', gen_random_uuid(),
                        'email', v_email,
                        'email_verified', true,
                        'phone_verified', true,
                        'phone', v_phone
                    ),
                    v_created_at, v_created_at, v_phone, v_created_at)
            returning id into v_user_id;

            -- Create identity for the user
            insert into auth.identities (provider_id, user_id, identity_data, provider, last_sign_in_at, created_at, updated_at, id)
            values (v_user_id, v_user_id,
                   jsonb_build_object(
                       'sub', v_user_id,
                       'email', v_email,
                       'zalo_id', v_zalo_id,
                       'name', v_name,
                       'avatar_url', v_picture,
                       'email_verified', true,
                       'phone_verified', true,
                       'phone', v_phone
                   ),
                   'zalo', v_created_at, v_created_at, v_created_at, gen_random_uuid());

            -- Cập nhật thông tin khách hàng vào bảng accounts
            update public.accounts
            set public_data = jsonb_build_object(
                'total_orders', 0,
                'total_spent', 0,
                'is_vip', false,
                'avatar_url', v_picture,
                'address', v_address,
                'zalo_email', v_email,
                'zalo_id', v_zalo_id,
                'phone', v_phone
            )
            where primary_owner_user_id = v_user_id;

            -- Add user to account as customer
            insert into public.accounts_memberships (user_id, account_id, account_role, created_at, updated_at)
            values (v_user_id, v_account_id, 'customer', v_created_at, v_created_at);

            -- Create customer activities
            insert into public.customer_activities (customer_id, account_id, action, details, created_at)
            values
                (v_user_id, v_account_id, 'account_created',
                 jsonb_build_object('method', 'zalo', 'device', 'mobile'), v_created_at),
                (v_user_id, v_account_id, 'profile_updated',
                 jsonb_build_object('fields', array['name', 'phone', 'address']), v_created_at + interval '1 day'),
                (v_user_id, v_account_id, 'account_login',
                 jsonb_build_object('device', 'mobile', 'location', v_cities[floor(random() * array_length(v_cities, 1)) + 1]),
                 v_created_at + interval '2 days');

            -- Add some random product views and cart activities for some customers (every 3rd customer)
            if i % 3 = 0 then
                insert into public.customer_activities (customer_id, account_id, action, details, created_at)
                select
                    v_user_id,
                    v_account_id,
                    'view_product',
                    jsonb_build_object('product_id', id, 'product_name', name),
                    v_created_at + interval '3 days' + (random() * interval '10 days')
                from public.products
                where account_id = v_account_id
                order by random()
                limit floor(random() * 4) + 1;

                -- Add to cart for some viewed products (every 6th customer)
                if i % 6 = 0 then
                    insert into public.customer_activities (customer_id, account_id, action, details, created_at)
                    select
                        v_user_id,
                        v_account_id,
                        'add_to_cart',
                        jsonb_build_object('product_id', id, 'product_name', name, 'quantity', floor(random() * 3) + 1),
                        v_created_at + interval '4 days' + (random() * interval '10 days')
                    from public.products
                    where account_id = v_account_id
                    order by random()
                    limit floor(random() * 3) + 1;

                    -- Checkout for some customers who added items to cart (every 12th customer)
                    if i % 12 = 0 then
                        insert into public.customer_activities (customer_id, account_id, action, details, created_at)
                        values (v_user_id, v_account_id, 'checkout',
                                jsonb_build_object('total_items', floor(random() * 5) + 1, 'total_amount', (random() * 500000)::int),
                                v_created_at + interval '5 days' + (random() * interval '10 days'));
                    end if;
                end if;
            end if;

            -- Create orders for some customers (every 4th customer)
            if i % 4 = 0 then
                declare
                    v_branch_id uuid;
                    v_order_id uuid;
                    v_order_date timestamp;
                    v_total_amount numeric := 0;
                    v_payment_methods text[] := ARRAY['cash', 'card', 'momo', 'zalopay', 'bank_transfer'];
                    v_order_statuses text[] := ARRAY['pending', 'processing', 'completed', 'delivered', 'cancelled'];
                    v_selected_payment text;
                    v_selected_status text;
                    v_num_orders integer;
                begin
                    -- Get a random branch
                    select id into v_branch_id from public.branches
                    where account_id = v_account_id
                    order by random() limit 1;

                    -- Create 1-3 orders for this customer
                    v_num_orders := floor(random() * 3) + 1;

                    for j in 1..v_num_orders loop
                        -- Set order date (within the last 30 days after customer creation)
                        v_order_date := v_created_at + interval '5 days' + (random() * interval '30 days');

                        -- Select random payment method and status
                        v_selected_payment := v_payment_methods[floor(random() * array_length(v_payment_methods, 1)) + 1];
                        v_selected_status := v_order_statuses[floor(random() * array_length(v_order_statuses, 1)) + 1];

                        -- Create the order
                        insert into public.customer_orders (
                            account_id,
                            customer_id,
                            branch_id,
                            total_amount,
                            payment_method,
                            status,
                            webhook_processed,
                            webhook_data,
                            created_at,
                            created_by,
                            order_code
                        ) values (
                            v_account_id,
                            v_user_id,
                            v_branch_id,
                            0, -- Will update after adding items
                            v_selected_payment,
                            v_selected_status,
                            true,
                            jsonb_build_object(
                                'payment_id', 'PAY-' || floor(random() * 1000000)::text,
                                'transaction_id', 'TXN-' || floor(random() * 1000000)::text,
                                'payment_time', v_order_date,
                                'customer_phone', v_phone
                            ),
                            v_order_date,
                            v_user_id,
                            'ORD-' || LPAD(floor(random() * 1000000)::text, 6, '0')
                        ) returning id into v_order_id;

                        -- Add 1-5 random products to the order
                        with product_selection as (
                            select id, price, name
                            from public.products
                            where account_id = v_account_id
                            order by random()
                            limit floor(random() * 5) + 1
                        ),
                        inserted_items as (
                            insert into public.customer_order_items (
                                order_id,
                                product_id,
                                attribute_id,
                                quantity,
                                price,
                                created_at,
                                created_by
                            )
                            select
                                v_order_id,
                                id,
                                NULL::uuid,
                                floor(random() * 3) + 1, -- Quantity between 1-3
                                price,
                                v_order_date,
                                v_user_id
                            from product_selection
                            returning quantity * price as item_total
                        )
                        select sum(item_total) into v_total_amount from inserted_items;

                        -- Update the order total
                        update public.customer_orders
                        set total_amount = v_total_amount
                        where id = v_order_id;

                        -- Add order activity
                        insert into public.customer_activities (
                            customer_id,
                            account_id,
                            action,
                            details,
                            created_at
                        ) values (
                            v_user_id,
                            v_account_id,
                            'order_placed',
                            jsonb_build_object(
                                'order_id', v_order_id,
                                'total_amount', v_total_amount,
                                'payment_method', v_selected_payment,
                                'status', v_selected_status,
                                'branch', (select name from public.branches where id = v_branch_id)
                            ),
                            v_order_date
                        );

                        -- If order is completed or delivered, add a review for some orders
                        if (v_selected_status = 'completed' or v_selected_status = 'delivered') and random() > 0.5 then
                            insert into public.customer_activities (
                                customer_id,
                                account_id,
                                action,
                                details,
                                created_at
                            ) values (
                                v_user_id,
                                v_account_id,
                                'order_review',
                                jsonb_build_object(
                                    'order_id', v_order_id,
                                    'rating', floor(random() * 3) + 3, -- Rating between 3-5
                                    'comment', CASE
                                        WHEN random() < 0.33 THEN 'Đồ ăn rất ngon, giao hàng nhanh!'
                                        WHEN random() < 0.66 THEN 'Chất lượng tốt, sẽ đặt lại lần sau.'
                                        ELSE 'Dịch vụ tuyệt vời, nhân viên thân thiện.'
                                    END
                                ),
                                v_order_date + interval '1 day'
                            );
                        end if;
                    end loop;
                end;
            end if;
        end loop;
    end;
$$;

-- Seed OA hệ thống (shared) trong oa_configurations
insert into public.oa_configurations (
  id,
  oa_type,
  oa_id,
  app_id,
  secret_key,
  access_token,
  refresh_token,
  token_expires_at,
  author_id,
  is_system_default,
  created_at,
  updated_at
) values (
  '********-0000-4000-a000-********0001',
  'shared',
  '4249362066646192564', -- DBIZ Test
  '1519751964765164619', -- app_id
  'Eh4DP3WGLe6CJs4YjUbb', -- secret_key
  null, -- access_token
  null, -- refresh_token
  null, -- token_expires_at
  null, -- author_id (will be updated later)
  true, -- is_system_default
  now(),
  now()
) on conflict (id) do update set
  oa_type = EXCLUDED.oa_type,
  oa_id = EXCLUDED.oa_id,
  app_id = EXCLUDED.app_id,
  secret_key = EXCLUDED.secret_key,
  is_system_default = EXCLUDED.is_system_default,
  updated_at = now();

-- Seed default themes
insert into public.themes (
  id,
  name,
  description,
  thumbnail_url,
  type,
  category,
  config,
  version,
  oa_config_id,
  mini_app_id,
  author_id,
  created_at,
  updated_at
) values (
  '********-0000-4000-a000-********0001',
  'Market',
  'A clean and modern e-commerce theme with minimalist design',
  '/images/theme/market.png',
  'default',
  'ecommerce',
  jsonb_build_object(
    'version', '1.0.0',
    'colors', jsonb_build_object(
      'primary', jsonb_build_object('main', '#2563eb', 'light', '#3b82f6', 'dark', '#1d4ed8'),
      'secondary', jsonb_build_object('main', '#4f46e5', 'light', '#6366f1', 'dark', '#4338ca'),
      'accent', jsonb_build_object('main', '#f59e0b', 'light', '#fbbf24', 'dark', '#d97706'),
      'background', jsonb_build_object('default', '#ffffff', 'paper', '#f3f4f6'),
      'text', jsonb_build_object('primary', '#111827', 'secondary', '#4b5563')
    ),
    'typography', jsonb_build_object(
      'fontFamily', 'Inter, system-ui, sans-serif',
      'headings', jsonb_build_object('fontFamily', 'Inter, system-ui, sans-serif', 'fontWeight', '600'),
      'body', jsonb_build_object('fontFamily', 'Inter, system-ui, sans-serif', 'fontWeight', '400')
    ),
    'layout', jsonb_build_object(
      'maxWidth', '1280px',
      'containerPadding', '1rem',
      'header', jsonb_build_object('height', '80px', 'sticky', true),
      'footer', jsonb_build_object('columns', 4)
    ),
    'components', jsonb_build_object(
      'button', jsonb_build_object('borderRadius', '0.5rem', 'paddingY', '0.5rem', 'paddingX', '1rem'),
      'card', jsonb_build_object('borderRadius', '0.75rem', 'shadow', 'sm'),
      'input', jsonb_build_object('borderRadius', '0.5rem', 'borderColor', '#e5e7eb')
    )
  ),
  '1.0.0',
  '********-0000-4000-a000-********0001', -- oa_config_id (shared OA)
  '3423472366583461276',
  null, -- author_id (will be updated later)
  now(),
  now()
) on conflict (id) do update set
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  thumbnail_url = EXCLUDED.thumbnail_url,
  type = EXCLUDED.type,
  category = EXCLUDED.category,
  config = EXCLUDED.config,
  version = EXCLUDED.version,
  oa_config_id = EXCLUDED.oa_config_id,
  updated_at = now();



-- Seed integrations cho account makerkit
do $$
DECLARE
    v_account_id UUID;
    v_oa_config_id UUID := '********-0000-4000-a000-********0001';
BEGIN
    -- Lấy account_id của makerkit
    SELECT id INTO v_account_id FROM public.accounts WHERE slug = 'makerkit';

    IF v_account_id IS NULL THEN
        RAISE NOTICE 'Account makerkit not found, skipping integrations seed';
        RETURN;
    END IF;

    -- Kiểm tra xem integration đã tồn tại chưa
    IF NOT EXISTS (SELECT 1 FROM public.integrations WHERE account_id = v_account_id AND type = 'zalo') THEN
        -- Insert integration cho account makerkit
        INSERT INTO public.integrations (
            account_id,
            type,
            name,
            status,
            enabled,
            metadata,
            created_at,
            updated_at
        ) VALUES (
            v_account_id,
            'zalo',
            'Zalo Notification Service',
            'not_connected', -- Status should be not_connected until token is obtained
            true,
            jsonb_build_object(
                'oa_config_id', v_oa_config_id,
                'oa_config_type', 'system'
            ),
            now(),
            now()
        );

        RAISE NOTICE 'Created integration for account makerkit';
    ELSE
        -- Cập nhật metadata cho integration hiện có
        UPDATE public.integrations
        SET
            metadata = jsonb_build_object(
                'oa_config_id', v_oa_config_id,
                'oa_config_type', 'system'
            ),
            status = 'not_connected', -- Status should be not_connected until token is obtained
            updated_at = now()
        WHERE account_id = v_account_id AND type = 'zalo';

        RAISE NOTICE 'Updated integration for account makerkit';
    END IF;
END;
$$;

-- Seed account_themes cho account makerkit
do $$
DECLARE
    v_account_id UUID;
    v_theme_id UUID := '********-0000-4000-a000-********0001';
    v_oa_config_id UUID := '********-0000-4000-a000-********0001';
BEGIN
    -- Lấy account_id của makerkit
    SELECT id INTO v_account_id FROM public.accounts WHERE slug = 'makerkit';

    IF v_account_id IS NULL THEN
        RAISE NOTICE 'Account makerkit not found, skipping account_themes seed';
        RETURN;
    END IF;

    -- Kiểm tra xem account_themes đã tồn tại chưa
    IF NOT EXISTS (SELECT 1 FROM public.account_themes WHERE account_id = v_account_id) THEN
        -- Insert account_themes cho account makerkit
        INSERT INTO public.account_themes (
            account_id,
            template_id,
            name,
            config,
            is_active,
            oa_config_id,
            mini_app_id,
            created_at,
            updated_at
        )
        SELECT
            v_account_id,
            t.id,
            t.name,
            t.config,
            true,
            t.oa_config_id,
            t.mini_app_id,
            now(),
            now()
        FROM public.themes t
        WHERE t.id = v_theme_id;

        RAISE NOTICE 'Created account_themes for account makerkit';
    ELSE
        RAISE NOTICE 'Account_themes already exists for account makerkit';
    END IF;
END;
$$;

-- Cập nhật author_id cho OA configurations và themes
do $$
DECLARE
    owner_id UUID;
    owner_email TEXT := '<EMAIL>';
BEGIN
    RAISE NOTICE 'Starting fix_oa_author migration...';

    -- Tìm user có email <EMAIL>
    SELECT id INTO owner_id FROM auth.users WHERE email = owner_email;

    -- Log kết quả tìm kiếm
    IF owner_id IS NOT NULL THEN
        RAISE NOTICE 'Found user with email %: %', owner_email, owner_id;
    ELSE
        RAISE NOTICE 'User with email % not found', owner_email;
    END IF;

    -- Nếu không tìm thấy, lấy user đầu tiên
    IF owner_id IS NULL THEN
        SELECT id INTO owner_id FROM auth.users LIMIT 1;

        IF owner_id IS NOT NULL THEN
            RAISE NOTICE 'Using first user from auth.users: %', owner_id;
        ELSE
            RAISE NOTICE 'No users found in auth.users table';
        END IF;
    END IF;

    -- Nếu vẫn không tìm thấy, thoát
    IF owner_id IS NULL THEN
        RAISE NOTICE 'No users found, cannot update author_id';
        RETURN;
    END IF;

    -- Cập nhật author_id cho OA configurations
    UPDATE public.oa_configurations
    SET
        author_id = owner_id,
        is_system_default = TRUE
    WHERE author_id IS NULL OR NOT EXISTS (
        SELECT 1 FROM auth.users WHERE id = oa_configurations.author_id
    );

    RAISE NOTICE 'Updated author_id for % OA configurations', (SELECT COUNT(*) FROM public.oa_configurations WHERE author_id = owner_id);

    -- Cập nhật author_id cho themes
    UPDATE public.themes
    SET author_id = owner_id
    WHERE author_id IS NULL OR NOT EXISTS (
        SELECT 1 FROM auth.users WHERE id = themes.author_id
    );

    RAISE NOTICE 'Updated author_id for % themes', (SELECT COUNT(*) FROM public.themes WHERE author_id = owner_id);

    -- Đảm bảo rằng ít nhất một OA configuration có is_system_default = TRUE
    IF NOT EXISTS (SELECT 1 FROM public.oa_configurations WHERE is_system_default = TRUE) THEN
        UPDATE public.oa_configurations
        SET is_system_default = TRUE
        WHERE id = (SELECT id FROM public.oa_configurations LIMIT 1);

        RAISE NOTICE 'Set is_system_default = TRUE for first OA configuration';
    END IF;
END;
$$;

-- CDP Sample Data
-- Insert sample data for CDP tables
do
$$
    declare
        v_account_id uuid;
    begin
        -- Get account id for makerkit team
        select id
        into v_account_id
        from public.accounts
        where slug = 'makerkit';

        -- Insert sample customer segments
        insert into public.customer_segments (
            account_id,
            name,
            description,
            type,
            criteria,
            customer_count,
            growth_rate,
            is_auto_updating,
            is_active
        ) values
            (v_account_id,
             'High Value Customers',
             'Customers with high lifetime value and low churn risk',
             'value_based',
             '{"total_spent": {"operator": "gte", "value": ********}, "value_tier": "high"}',
             3,
             15.5,
             true,
             true),
            (v_account_id,
             'Highly Engaged Users',
             'Users with high engagement scores and frequent interactions',
             'behavioral',
             '{"engagement_score": {"operator": "gte", "value": 0.7}}',
             4,
             8.2,
             true,
             true);

        -- Insert sample customer journeys
        insert into public.customer_journeys (
            account_id,
            name,
            description,
            status,
            trigger_type,
            trigger_config,
            steps,
            participants,
            completion_rate,
            tags
        ) values
            (v_account_id,
             'Welcome New Customers',
             'Onboarding journey for new customer registration',
             'active',
             'segment_entry',
             '{"segment_id": "new_customers"}',
             '[
                {"type": "email", "name": "Welcome Email", "config": {"subject": "Welcome to our platform!", "template": "welcome_email"}, "delay_hours": 0},
                {"type": "wait", "name": "Wait 24 Hours", "config": {}, "delay_hours": 24},
                {"type": "email", "name": "Getting Started Guide", "config": {"subject": "Get started with these tips", "template": "getting_started"}, "delay_hours": 0}
             ]',
             1247,
             0.78,
             ARRAY['onboarding', 'email']),
            (v_account_id,
             'Cart Abandonment Recovery',
             'Re-engage customers who abandoned their cart',
             'active',
             'event',
             '{"event_name": "cart_abandoned"}',
             '[
                {"type": "email", "name": "Cart Reminder", "config": {"subject": "You left something in your cart", "template": "cart_reminder"}, "delay_hours": 1},
                {"type": "wait", "name": "Wait 24 Hours", "config": {}, "delay_hours": 24},
                {"type": "email", "name": "Special Discount", "config": {"subject": "10% off your cart items", "template": "discount_offer"}, "delay_hours": 0}
             ]',
             856,
             0.45,
             ARRAY['recovery', 'email', 'discount']);

        -- Insert sample analytics data
        insert into public.analytics_data (
            account_id,
            metric_name,
            metric_value,
            metric_type,
            period,
            metadata
        ) values
            (v_account_id,
             'total_customers',
             3,
             'count',
             'current_month',
             '{"source": "customer_profiles"}'),
            (v_account_id,
             'total_revenue',
             ********,
             'currency',
             'current_month',
             '{"source": "customer_profiles", "currency": "VND"}');

        -- Insert sample integration statuses
        insert into public.integration_statuses (
            account_id,
            name,
            provider,
            category,
            status,
            last_sync,
            records_synced,
            health_score,
            config
        ) values
            (v_account_id,
             'Email Marketing Platform',
             'mailchimp',
             'marketing',
             'connected',
             NOW() - INTERVAL '2 hours',
             1250,
             0.95,
             '{"api_key": "***", "list_id": "abc123", "sync_frequency": "hourly"}');

        -- Insert sample AI insights
        insert into public.ai_insights (
            account_id,
            type,
            title,
            description,
            confidence,
            impact,
            category,
            status,
            data,
            recommendations
        ) values
            (v_account_id,
             'trend',
             'Increasing Customer Engagement',
             'Customer engagement scores have increased by 15% over the past month',
             0.85,
             'high',
             'engagement',
             'active',
             '{"engagement_increase": 0.15, "timeframe": "30_days"}',
             ARRAY['Continue current engagement strategies', 'Expand successful campaigns', 'Monitor engagement metrics closely']);

        RAISE NOTICE 'CDP sample data inserted successfully';
    end;
$$;