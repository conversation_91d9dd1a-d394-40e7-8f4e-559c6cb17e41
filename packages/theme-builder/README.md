# @kit/theme-builder

A powerful theme builder package for Zalo Mini Apps using Puck Editor and ZMP-UI components.

## Features

- 🎨 **Visual Theme Builder** - Drag-and-drop interface powered by Puck Editor
- 🎯 **ZMP-UI Integration** - Pre-built components optimized for Zalo Mini Apps
- 🎪 **Live Preview** - Real-time theme preview and editing
- 📱 **Mobile-First** - Designed specifically for mobile app interfaces
- 🎨 **Theme System** - Comprehensive theming with colors, typography, spacing, and layout
- 🔧 **Extensible** - Easy to add custom components and themes
- 💾 **Persistence** - Save and load themes with localStorage support
- 🔄 **Undo/Redo** - Full history management for theme editing
- 📲 **Mobile-Optimized UI** - Custom mobile interface with bottom tabs and touch-friendly controls
- 🎯 **Touch-Friendly** - Optimized for touch interactions on mobile devices

## Installation

```bash
npm install @kit/theme-builder
# or
yarn add @kit/theme-builder
# or
pnpm add @kit/theme-builder
```

## Peer Dependencies

Make sure you have these peer dependencies installed:

```bash
npm install react react-dom zmp-ui zmp-sdk @measured/puck
```

## Quick Start

### Basic Theme Builder

```tsx
import React from 'react';
import { ThemeBuilder, ThemeProvider } from '@kit/theme-builder';

function App() {
  const handleChange = (data, theme) => {
    console.log('Auto-saving:', { data, theme });
  };

  const handlePreview = (data, theme) => {
    console.log('Previewing:', { data, theme });
  };

  const handlePublish = (data, theme) => {
    console.log('Publishing:', { data, theme });
  };

  return (
    <ThemeProvider>
      <ThemeBuilder
        onChange={handleChange}
        onPreview={handlePreview}
        onPublish={handlePublish}
        showThemePanel={true}
        showPreview={true}
      />
    </ThemeProvider>
  );
}

export default App;
```

## Mobile-Optimized Interface

The theme builder automatically detects mobile devices and switches to a mobile-optimized interface:

### Mobile Features

- **Bottom Tab Navigation** - Components, Fields, and Outline tabs at the bottom
- **Touch-Friendly Controls** - Larger touch targets and optimized spacing
- **Collapsible Interface** - Tabs can be collapsed to maximize preview area
- **Sticky Header** - Quick access to preview/publish actions
- **Responsive Layout** - Adapts to different screen sizes and orientations

### Mobile UI Components

```tsx
// The mobile interface includes:
// 1. Custom mobile header with action buttons
// 2. Bottom tabs with swipe gestures
// 3. Touch-optimized component list
// 4. Mobile-friendly form fields
// 5. Collapsible panels for better space usage
```

### Mobile Demo

```tsx
import { MobileThemeBuilderDemo } from '@kit/theme-builder/examples/mobile-demo';

function App() {
  return <MobileThemeBuilderDemo />;
}
```

### Theme Renderer (for displaying built themes)

```tsx
import React from 'react';
import { ThemeRenderer } from '@kit/theme-builder';

function MyPage({ themeData, themeConfig }) {
  return (
    <ThemeRenderer
      data={themeData}
      theme={themeConfig}
      className="my-theme-container"
    />
  );
}
```

### Using Theme Hooks

```tsx
import React from 'react';
import { useTheme, useThemeBuilder } from '@kit/theme-builder';

function ThemeControls() {
  const { theme, updateTheme } = useTheme();
  const { undo, redo, canUndo, canRedo } = useThemeBuilder();

  const changePrimaryColor = (color) => {
    updateTheme({
      colors: {
        ...theme.colors,
        primary: {
          ...theme.colors.primary,
          main: color,
        },
      },
    });
  };

  return (
    <div>
      <input
        type="color"
        value={theme.colors.primary.main}
        onChange={(e) => changePrimaryColor(e.target.value)}
      />
      <button onClick={undo} disabled={!canUndo}>
        Undo
      </button>
      <button onClick={redo} disabled={!canRedo}>
        Redo
      </button>
    </div>
  );
}
```

## Available Components

### Layout Components
- **Container** - Responsive container with max-width and padding options
- **Header** - App header with title and navigation
- **Footer** - App footer with customizable content
- **Grid** - Flexible grid system with responsive columns
- **Box** - Flexible container with layout properties

### Content Components
- **Text** - Typography component with size and weight options
- **Hero** - Hero section with title, subtitle, and call-to-action
- **Card** - Content card with image, title, and description
- **List** - Customizable list component with icons

### Interactive Components
- **Button** - ZMP-UI button with variants and sizes

## Theme Configuration

The theme system supports comprehensive customization:

```tsx
const customTheme = {
  name: 'My Custom Theme',
  colors: {
    primary: {
      main: '#1877F2',
      light: '#42A5F5',
      dark: '#1565C0',
      contrast: '#FFFFFF',
    },
    // ... more colors
  },
  typography: {
    fontFamily: {
      primary: 'Inter, sans-serif',
    },
    fontSize: {
      base: '1rem',
      lg: '1.125rem',
      // ... more sizes
    },
  },
  spacing: {
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    // ... more spacing
  },
  // ... more theme properties
};
```

## Manual Linking for Mobile Project

To use this package in your mobile project:

1. Build the package:
```bash
cd packages/theme-builder
npm run build
```

2. Link manually in your mobile project's package.json:
```json
{
  "dependencies": {
    "@kit/theme-builder": "file:../path/to/packages/theme-builder"
  }
}
```

3. Install dependencies:
```bash
npm install
```

## Development

```bash
# Install dependencies
npm install

# Start development mode
npm run dev

# Build the package
npm run build

# Type checking
npm run typecheck

# Linting
npm run lint
```

## API Reference

### Components

#### `<ThemeBuilder />`
Main theme builder component with visual editor.

**Props:**
- `config?: PuckConfig` - Puck editor configuration
- `data?: Data` - Initial page data
- `theme?: ThemeConfig` - Initial theme configuration
- `onChange?: (data, theme) => void` - Auto-save callback
- `onPreview?: (data, theme) => void` - Preview callback
- `onPublish?: (data, theme) => void` - Publish callback
- `showThemePanel?: boolean` - Show theme editing panel
- `showPreview?: boolean` - Show preview button

#### `<ThemeRenderer />`
Renders a built theme without editing capabilities.

**Props:**
- `data: Data` - Page data to render
- `config?: PuckConfig` - Component configuration
- `theme: ThemeConfig` - Theme configuration
- `className?: string` - Additional CSS classes
- `style?: CSSProperties` - Additional styles

#### `<ThemeProvider />`
Context provider for theme management.

**Props:**
- `children: ReactNode` - Child components
- `initialTheme?: ThemeConfig` - Initial theme

### Hooks

#### `useTheme()`
Access and modify the current theme.

**Returns:**
- `theme: ThemeConfig` - Current theme
- `updateTheme: (updates) => void` - Update theme
- `resetTheme: () => void` - Reset to default
- `applyTheme: (theme) => void` - Apply new theme

#### `useThemeBuilder()`
Advanced theme editing with history management.

**Returns:**
- `theme: ThemeConfig` - Current theme
- `isEditing: boolean` - Editing state
- `startEditing: () => void` - Start editing mode
- `stopEditing: () => void` - Stop editing mode
- `updateTheme: (updates) => void` - Update with history
- `undo: () => void` - Undo last change
- `redo: () => void` - Redo last undone change
- `canUndo: boolean` - Can undo
- `canRedo: boolean` - Can redo

## License

MIT
