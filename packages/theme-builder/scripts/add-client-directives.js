#!/usr/bin/env node

/**
 * Add Client Directives Script
 * 
 * Automatically adds "use client" directive to components that need it
 * for Next.js compatibility while keeping source files clean for React
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Components that need "use client" directive
const CLIENT_COMPONENTS = [
  'src/editor/**/*.tsx',
  'src/components/**/*.tsx',
  'src/fields/**/*.tsx',
  'src/hooks/**/*.ts',
  'src/hooks/**/*.tsx',
];

// Hooks that require client-side execution
const CLIENT_HOOKS = [
  'useState',
  'useEffect',
  'useCallback',
  'useMemo',
  'useRef',
  'useContext',
  'useReducer',
  'useLayoutEffect',
  'useImperativeHandle',
  'useDebugValue',
];

/**
 * Check if file needs "use client" directive
 */
function needsClientDirective(content) {
  // Already has directive
  if (content.includes("'use client'") || content.includes('"use client"')) {
    return false;
  }

  // Check for React hooks
  const hasHooks = CLIENT_HOOKS.some(hook => {
    const hookRegex = new RegExp(`\\b${hook}\\b`, 'g');
    return hookRegex.test(content);
  });

  // Check for event handlers
  const hasEventHandlers = /on[A-Z]\w*\s*=/.test(content);

  // Check for browser APIs
  const hasBrowserAPIs = /\b(window|document|localStorage|sessionStorage)\b/.test(content);

  return hasHooks || hasEventHandlers || hasBrowserAPIs;
}

/**
 * Add client directive to file
 */
function addClientDirective(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  if (!needsClientDirective(content)) {
    return false;
  }

  // Add directive at the top
  const newContent = `'use client';\n\n${content}`;
  
  // Write to dist directory for Next.js builds
  const distPath = filePath.replace('/src/', '/dist-nextjs/');
  const distDir = path.dirname(distPath);
  
  if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
  }
  
  fs.writeFileSync(distPath, newContent);
  console.log(`✅ Added "use client" to: ${distPath}`);
  
  return true;
}

/**
 * Process all component files
 */
function processFiles() {
  console.log('🔧 Adding client directives for Next.js compatibility...');
  
  let processedCount = 0;
  
  CLIENT_COMPONENTS.forEach(pattern => {
    const files = glob.sync(pattern, { cwd: __dirname + '/..' });
    
    files.forEach(file => {
      const fullPath = path.join(__dirname, '..', file);
      if (addClientDirective(fullPath)) {
        processedCount++;
      }
    });
  });
  
  console.log(`✅ Processed ${processedCount} files with client directives`);
}

/**
 * Clean dist directory
 */
function cleanDist() {
  const distPath = path.join(__dirname, '..', 'dist-nextjs');
  if (fs.existsSync(distPath)) {
    fs.rmSync(distPath, { recursive: true });
  }
}

// Main execution
if (require.main === module) {
  const command = process.argv[2];
  
  switch (command) {
    case 'clean':
      cleanDist();
      console.log('🧹 Cleaned dist-nextjs directory');
      break;
      
    case 'build':
      cleanDist();
      processFiles();
      break;
      
    default:
      console.log('Usage: node add-client-directives.js [clean|build]');
      break;
  }
}

module.exports = {
  needsClientDirective,
  addClientDirective,
  processFiles,
  cleanDist,
};
