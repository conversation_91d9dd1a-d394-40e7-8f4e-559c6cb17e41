import React, { useState } from 'react';
import { ThemeBuilder } from '../src/editor/ThemeBuilder';
import { ZMP_PUCK_CONFIG } from '../src/components/zmp-config';
import { Data } from '@measured/puck';

// Demo data showcasing enhanced Button features
const enhancedButtonDemoData: Data = {
  content: [
    {
      type: "HeroSection",
      props: {
        id: "hero-1",
        businessName: "Enhanced Button Demo",
        slogan: "Showcasing Zalo Mini App Button Features",
        description: "Explore the powerful button component with dynamic fields, Zalo integrations, and rich animations.",
        logo: "",
        backgroundGradient: "from-blue-600 to-purple-600",
        primaryButtonText: "Get Started",
        secondaryButtonText: "Learn More",
        primaryButtonAction: "#demo",
        secondaryButtonAction: "#features",
      },
    },
    
    // Link Button Example
    {
      type: "Button",
      props: {
        id: "link-button-1",
        label: "Visit Our Website",
        actionType: "link",
        href: "https://example.com",
        openInApp: false,
        variant: "primary",
        size: "medium",
        fullWidth: false,
        disabled: false,
        icon: "🔗",
        iconPosition: "left",
        animationType: "none",
        showRipple: true,
      },
    },
    
    // Phone Button Example
    {
      type: "Button",
      props: {
        id: "phone-button-1",
        label: "Call Support",
        actionType: "phone",
        phoneNumber: "+84 123 456 789",
        variant: "success",
        size: "large",
        fullWidth: true,
        disabled: false,
        icon: "📞",
        iconPosition: "left",
        animationType: "pulse",
        showRipple: true,
      },
    },
    
    // Zalo Chat Button Example
    {
      type: "Button",
      props: {
        id: "chat-button-1",
        label: "Chat with Support",
        actionType: "chat",
        chatType: "oa",
        chatId: "your-oa-id-here",
        chatMessage: "Xin chào, tôi cần hỗ trợ về sản phẩm của bạn. Có thể giúp tôi được không?",
        variant: "zalo",
        size: "medium",
        fullWidth: false,
        disabled: false,
        icon: "💬",
        iconPosition: "left",
        animationType: "bounce",
        showRipple: true,
      },
    },
    
    // Share Button Example
    {
      type: "Button",
      props: {
        id: "share-button-1",
        label: "Share This Page",
        actionType: "share",
        shareTitle: "Amazing Zalo Mini App",
        shareDescription: "Check out this incredible mini app with enhanced button features!",
        shareImageUrl: "https://example.com/share-image.jpg",
        variant: "secondary",
        size: "medium",
        fullWidth: false,
        disabled: false,
        icon: "📤",
        iconPosition: "right",
        animationType: "shake",
        showRipple: true,
      },
    },
    
    // Custom Action Button Example
    {
      type: "Button",
      props: {
        id: "custom-button-1",
        label: "Custom Action",
        actionType: "custom",
        customAction: `
          // Custom JavaScript code
          console.log('Custom action executed!');
          
          // Show a custom alert
          alert('This is a custom action! You can do anything here.');
          
          // Example: Track analytics
          if (typeof gtag !== 'undefined') {
            gtag('event', 'custom_button_click', {
              'event_category': 'engagement',
              'event_label': 'demo_button'
            });
          }
          
          // Example: Show a toast notification
          if (typeof window !== 'undefined' && window.showToast) {
            window.showToast('Custom action completed!');
          }
        `,
        variant: "warning",
        size: "small",
        fullWidth: false,
        disabled: false,
        icon: "⚙️",
        iconPosition: "left",
        animationType: "pulse",
        showRipple: true,
      },
    },
    
    // Danger Button Example
    {
      type: "Button",
      props: {
        id: "danger-button-1",
        label: "Delete Account",
        actionType: "custom",
        customAction: `
          if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
            console.log('Account deletion confirmed');
            alert('Account deletion process started...');
          }
        `,
        variant: "danger",
        size: "medium",
        fullWidth: true,
        disabled: false,
        icon: "🗑️",
        iconPosition: "left",
        animationType: "shake",
        showRipple: false,
      },
    },
    
    // Ghost Button Example
    {
      type: "Button",
      props: {
        id: "ghost-button-1",
        label: "Learn More",
        actionType: "link",
        href: "https://docs.example.com",
        openInApp: true,
        variant: "ghost",
        size: "medium",
        fullWidth: false,
        disabled: false,
        icon: "📚",
        iconPosition: "right",
        animationType: "none",
        showRipple: true,
      },
    },
  ],
  root: {
    props: {
      title: "Enhanced Button Demo",
      primaryColor: "#0068ff",
      primaryLightColor: "#4096ff",
      primaryDarkColor: "#0050d1",
      secondaryColor: "#52c41a",
      secondaryLightColor: "#73d13d",
      secondaryDarkColor: "#389e0d",
      accentColor: "#faad14",
      accentLightColor: "#ffc53d",
      accentDarkColor: "#d48806",
      backgroundColor: "#ffffff",
      paperBackgroundColor: "#f9fafb",
      textColor: "#111827",
      secondaryTextColor: "#6b7280",
      fontFamily: "Inter, system-ui, sans-serif",
      headingsFontFamily: "Inter, system-ui, sans-serif",
      headingsFontWeight: "600",
      bodyFontFamily: "Inter, system-ui, sans-serif",
      bodyFontWeight: "400",
      borderRadius: "8px",
      spacing: "16px",
    },
  },
  zones: {},
};

export function EnhancedButtonDemo() {
  const [data, setData] = useState<Data>(enhancedButtonDemoData);

  const handleChange = (newData: Data) => {
    setData(newData);
    console.log('Enhanced Button Demo - Data changed:', newData);
  };

  const handlePreview = (newData: Data) => {
    console.log('Enhanced Button Demo - Preview:', newData);
  };

  const handlePublish = async (newData: Data) => {
    console.log('Enhanced Button Demo - Publishing:', newData);
    
    // Save to localStorage for demo
    localStorage.setItem('enhanced-button-demo-data', JSON.stringify(newData));
    
    alert('Enhanced Button Demo published successfully!');
  };

  return (
    <div style={{ height: '100vh', width: '100vw' }}>
      <ThemeBuilder
        config={ZMP_PUCK_CONFIG}
        data={data}
        onChange={handleChange}
        onPreview={handlePreview}
        onPublish={handlePublish}
      />
      
      {/* Demo Instructions */}
      <div
        style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          background: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          padding: '12px',
          borderRadius: '8px',
          fontSize: '12px',
          maxWidth: '300px',
          zIndex: 9999,
        }}
      >
        <h4 style={{ margin: '0 0 8px 0' }}>🎯 Enhanced Button Demo</h4>
        <p style={{ margin: '0 0 8px 0' }}>
          Click on any button to see dynamic fields change based on action type.
        </p>
        <ul style={{ margin: 0, paddingLeft: '16px' }}>
          <li>🔗 Link: URL + Open in App option</li>
          <li>📞 Phone: Phone number field</li>
          <li>💬 Chat: Chat type, ID, message</li>
          <li>📤 Share: Title, description, image</li>
          <li>⚙️ Custom: JavaScript code editor</li>
        </ul>
      </div>
    </div>
  );
}

export default EnhancedButtonDemo;
