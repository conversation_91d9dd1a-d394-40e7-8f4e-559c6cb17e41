import React, { useState } from 'react';
import { ThemeBuilder } from '../src/editor/ThemeBuilder';
import { ZMP_PUCK_CONFIG } from '../src/components/zmp-config';
import { Data } from '@measured/puck';

// Simple test data to check if Button fields are working
const buttonFieldsTestData: Data = {
  content: [
    {
      type: "Button",
      props: {
        id: "button-test-1",
        label: "Test Button (Static Fields)",
        actionType: "link",
        href: "https://example.com",
        variant: "primary",
        size: "medium",
        fullWidth: false,
        disabled: false,
        icon: "🔗",
        iconPosition: "left",
        animationType: "none",
        showRipple: true,
      },
    },
    {
      type: "ButtonDynamic",
      props: {
        id: "button-dynamic-test-1",
        label: "Test Button (Dynamic Fields)",
        actionType: "link",
        href: "https://example.com",
        variant: "zalo",
        size: "medium",
        fullWidth: false,
        disabled: false,
        icon: "💬",
        iconPosition: "left",
        animationType: "pulse",
        showRipple: true,
      },
    },
  ],
  root: {
    props: {
      title: "Button Fields Test",
      primaryColor: "#0068ff",
      backgroundColor: "#ffffff",
      textColor: "#111827",
      fontFamily: "Inter, system-ui, sans-serif",
    },
  },
  zones: {},
};

export function ButtonFieldsTest() {
  const [data, setData] = useState<Data>(buttonFieldsTestData);

  const handleChange = (newData: Data) => {
    setData(newData);
    console.log('Button Fields Test - Data changed:', newData);
  };

  const handlePreview = (newData: Data) => {
    console.log('Button Fields Test - Preview:', newData);
  };

  const handlePublish = async (newData: Data) => {
    console.log('Button Fields Test - Publishing:', newData);
    localStorage.setItem('button-fields-test-data', JSON.stringify(newData));
    alert('Button Fields Test published successfully!');
  };

  return (
    <div style={{ height: '100vh', width: '100vw' }}>
      <ThemeBuilder
        config={ZMP_PUCK_CONFIG}
        data={data}
        onChange={handleChange}
        onPreview={handlePreview}
        onPublish={handlePublish}
      />
      
      {/* Test Instructions */}
      <div
        style={{
          position: 'fixed',
          top: '10px',
          left: '10px',
          background: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          padding: '12px',
          borderRadius: '8px',
          fontSize: '12px',
          maxWidth: '300px',
          zIndex: 9999,
        }}
      >
        <h4 style={{ margin: '0 0 8px 0' }}>🧪 Button Fields Test</h4>
        <p style={{ margin: '0 0 8px 0' }}>
          Click on buttons to test field visibility:
        </p>
        <ul style={{ margin: 0, paddingLeft: '16px', fontSize: '11px' }}>
          <li><strong>Button</strong>: Static fields (all visible)</li>
          <li><strong>ButtonDynamic</strong>: Dynamic fields (change based on actionType)</li>
        </ul>
        <p style={{ margin: '8px 0 0 0', fontSize: '11px', opacity: 0.8 }}>
          Try changing actionType in ButtonDynamic to see fields change!
        </p>
      </div>
    </div>
  );
}

export default ButtonFieldsTest;
