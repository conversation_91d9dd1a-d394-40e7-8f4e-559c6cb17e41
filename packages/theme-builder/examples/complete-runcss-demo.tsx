import React, { useState, useEffect } from 'react';
import { ThemeBuilder } from '../src/editor/ThemeBuilder';
import { ZMP_PUCK_CONFIG } from '../src/components/zmp-config';
import { useThemeBuilderPublish } from '../src/hooks/useOptimizedPublish';
import { Data } from '@measured/puck';

// Complete demo data showcasing RunCSS service integration
const completeRunCSSDemoData: Data = {
  content: [
    {
      type: "TailwindBox",
      props: {
        id: "hero-section",
        content: "🚀 Complete RunCSS Integration Demo\n\nThis demo showcases the full RunCSS service integration with npm package, real-time preview, and optimized production builds.",
        className: "p-8 bg-gradient-to-br from-zalo via-blue-600 to-purple-700 rounded-3xl shadow-hard border border-white/20 backdrop-blur-sm",
        containerClassName: "max-w-4xl mx-auto my-8",
        textClassName: "text-white text-center text-xl font-bold leading-relaxed",
        showPreview: true,
      },
    },
    
    {
      type: "TailwindBox",
      props: {
        id: "features-grid",
        content: "⚡ Real-time CSS Generation\n\n• NPM package integration (no CDN)\n• Instant preview with RunCSS service\n• Automatic CSS optimization\n• Production-ready builds",
        className: "p-6 bg-white rounded-xl shadow-medium border-2 border-gray-100 hover:border-zalo hover:shadow-hard transition-all duration-300",
        containerClassName: "max-w-lg mx-auto my-6",
        textClassName: "text-gray-800 font-semibold leading-loose",
        showPreview: true,
      },
    },
    
    {
      type: "TailwindBox",
      props: {
        id: "performance-stats",
        content: "📊 Performance Optimizations\n\n• CSS Minification: Up to 70% smaller\n• Hash-based caching\n• Class validation & warnings\n• Batch processing support",
        className: "p-6 bg-gradient-to-r from-success to-info rounded-2xl shadow-soft text-white",
        containerClassName: "max-w-lg mx-auto my-6",
        textClassName: "font-medium text-center leading-loose",
        showPreview: true,
      },
    },
    
    {
      type: "TailwindBox",
      props: {
        id: "zalo-integration",
        content: "🎨 Zalo Mini App Ready\n\n• Pre-configured Zalo brand colors\n• Mobile-optimized breakpoints\n• Touch-friendly editor interface\n• Production deployment ready",
        className: "p-6 bg-zalo-dark rounded-xl shadow-lg border-2 border-zalo-light/30",
        containerClassName: "max-w-lg mx-auto my-6",
        textClassName: "text-white font-semibold text-center leading-loose",
        showPreview: true,
      },
    },
    
    {
      type: "TailwindBox",
      props: {
        id: "interactive-demo",
        content: "🎯 Try It Yourself!\n\nClick on any box above and edit the 'Container Classes', 'Box Classes', or 'Text Classes' fields. You'll see:\n\n• Real-time autocomplete\n• Instant visual preview\n• Class validation\n• Live CSS generation",
        className: "p-8 bg-gradient-to-tr from-warning via-yellow-400 to-orange-500 rounded-2xl shadow-medium border-2 border-yellow-300",
        containerClassName: "max-w-2xl mx-auto my-8",
        textClassName: "text-gray-900 font-bold text-center leading-relaxed",
        showPreview: true,
      },
    },
    
    {
      type: "ButtonDynamic",
      props: {
        id: "publish-demo-button",
        label: "🚀 Publish & Extract CSS",
        actionType: "custom",
        customAction: `
          console.log('Publishing theme with CSS extraction...');
          
          // This will trigger the optimized publish process
          // which extracts Tailwind classes and generates optimized CSS
          alert('Check the console and browser storage to see the extracted CSS and optimization stats!');
        `,
        variant: "zalo",
        size: "large",
        fullWidth: true,
        disabled: false,
        icon: "📦",
        iconPosition: "left",
        animationType: "pulse",
        showRipple: true,
      },
    },
  ],
  root: {
    props: {
      title: "Complete RunCSS Demo",
      primaryColor: "#0068ff",
      primaryLightColor: "#4096ff",
      primaryDarkColor: "#0050d1",
      secondaryColor: "#10b981",
      secondaryLightColor: "#34d399",
      secondaryDarkColor: "#059669",
      accentColor: "#f59e0b",
      accentLightColor: "#fbbf24",
      accentDarkColor: "#d97706",
      backgroundColor: "#f8fafc",
      paperBackgroundColor: "#ffffff",
      textColor: "#111827",
      secondaryTextColor: "#6b7280",
      fontFamily: "Inter, system-ui, sans-serif",
      headingsFontFamily: "Inter, system-ui, sans-serif",
      headingsFontWeight: "700",
      bodyFontFamily: "Inter, system-ui, sans-serif",
      bodyFontWeight: "400",
      borderRadius: "12px",
      spacing: "16px",
    },
  },
  zones: {},
};

export function CompleteRunCSSDemo() {
  const [data, setData] = useState<Data>(completeRunCSSDemoData);
  const [cssStats, setCSSStats] = useState<any>(null);
  const { publish, isPublishing, error, progress, lastPublished } = useThemeBuilderPublish();

  // Monitor localStorage for CSS extraction results
  useEffect(() => {
    const checkForCSSStats = () => {
      const cssData = localStorage.getItem('optimized-theme-css');
      const hashData = localStorage.getItem('optimized-theme-hash');
      const configData = localStorage.getItem('optimized-theme-config');
      
      if (cssData && hashData && configData) {
        try {
          const config = JSON.parse(configData);
          setCSSStats({
            css: cssData,
            hash: hashData,
            metadata: config.metadata,
          });
        } catch (error) {
          console.error('Failed to parse CSS stats:', error);
        }
      }
    };

    // Check immediately and then every second
    checkForCSSStats();
    const interval = setInterval(checkForCSSStats, 1000);
    
    return () => clearInterval(interval);
  }, []);

  const handleChange = (newData: Data) => {
    setData(newData);
    console.log('🔄 Data changed - RunCSS processing classes in real-time');
  };

  const handlePreview = (newData: Data) => {
    console.log('👀 Preview mode - Current data:', newData);
  };

  const handlePublish = async (newData: Data) => {
    console.log('🚀 Publishing with RunCSS optimization...');
    await publish(newData);
  };

  return (
    <div style={{ height: '100vh', width: '100vw', position: 'relative' }}>
      <ThemeBuilder
        config={ZMP_PUCK_CONFIG}
        data={data}
        onChange={handleChange}
        onPreview={handlePreview}
        onPublish={handlePublish}
      />
      
      {/* Publishing Progress */}
      {(isPublishing || progress) && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-gray-200 border-t-zalo rounded-full animate-spin mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                RunCSS Processing
              </h3>
              {progress && (
                <>
                  <p className="text-gray-600 mb-4">{progress.step}</p>
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
                    <div
                      className="bg-gradient-to-r from-zalo to-blue-600 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${progress.percentage}%` }}
                    />
                  </div>
                  <p className="text-sm text-gray-500">{progress.percentage}%</p>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="fixed top-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 max-w-md shadow-lg z-50">
          <div className="flex items-start">
            <div className="text-red-400 mr-3">❌</div>
            <div>
              <h4 className="text-red-800 font-semibold">RunCSS Error</h4>
              <p className="text-red-600 text-sm mt-1">{error.message}</p>
            </div>
          </div>
        </div>
      )}

      {/* CSS Stats Display */}
      {cssStats && (
        <div className="fixed bottom-4 left-4 bg-white border border-gray-200 rounded-xl p-4 max-w-sm shadow-xl z-40">
          <h4 className="font-bold text-gray-900 mb-3 flex items-center">
            📊 CSS Optimization Results
          </h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Classes:</span>
              <span className="font-semibold text-zalo">
                {cssStats.metadata.stats.uniqueClasses}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Original Size:</span>
              <span className="font-semibold">
                {(cssStats.metadata.stats.originalSize / 1024).toFixed(2)} KB
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Minified Size:</span>
              <span className="font-semibold text-success">
                {(cssStats.metadata.stats.minifiedSize / 1024).toFixed(2)} KB
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Compression:</span>
              <span className="font-semibold text-success">
                {((1 - cssStats.metadata.stats.compressionRatio) * 100).toFixed(1)}%
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Hash:</span>
              <span className="font-mono text-xs text-gray-500">
                {cssStats.hash}
              </span>
            </div>
          </div>
          <div className="mt-3 pt-3 border-t border-gray-100">
            <button
              onClick={() => {
                console.log('📋 CSS Content:', cssStats.css);
                console.log('📊 Full Stats:', cssStats.metadata);
              }}
              className="text-xs text-zalo hover:text-zalo-dark font-medium"
            >
              📋 View in Console
            </button>
          </div>
        </div>
      )}
      
      {/* Instructions */}
      <div className="fixed top-4 left-4 bg-gradient-to-br from-zalo to-blue-600 text-white rounded-xl p-4 max-w-xs shadow-xl z-40">
        <h4 className="font-bold mb-2">🎯 RunCSS Demo Guide</h4>
        <div className="text-sm space-y-2 opacity-90">
          <p>1. Click any TailwindBox component</p>
          <p>2. Edit CSS class fields with autocomplete</p>
          <p>3. See real-time preview updates</p>
          <p>4. Click "Publish" to see CSS extraction</p>
          <p>5. Check console & stats panel</p>
        </div>
      </div>

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        .animate-spin {
          animation: spin 1s linear infinite;
        }
      `}</style>
    </div>
  );
}

export default CompleteRunCSSDemo;
