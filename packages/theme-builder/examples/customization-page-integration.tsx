/**
 * Integration example for apps/web/app/home/<USER>/miniapp/setup/customization/page.tsx
 * 
 * This shows how to integrate the Tailwind CSS field with optimized publishing
 * in the customization page.
 */

import React, { useState, useCallback } from 'react';
import { ThemeBuilder } from '@kit/theme-builder';
import { ZMP_PUCK_CONFIG } from '@kit/theme-builder/components';
import { useOptimizedPublish, OptimizedThemeConfig } from '@kit/theme-builder/hooks';
import { Data } from '@measured/puck';

interface CustomizationPageProps {
  accountSlug: string;
  initialData?: Data;
}

export function CustomizationPageIntegration({ 
  accountSlug, 
  initialData 
}: CustomizationPageProps) {
  const [data, setData] = useState<Data>(initialData || getDefaultData());
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Optimized publish hook with CSS extraction
  const { publish, isPublishing, error, progress } = useOptimizedPublish({
    onPublish: handleOptimizedPublish,
    onError: handlePublishError,
    onProgress: handleProgress,
  });

  // Handle optimized publish with CSS extraction
  async function handleOptimizedPublish(config: OptimizedThemeConfig) {
    try {
      setIsSaving(true);

      // 1. Save theme data to database
      const themeResponse = await fetch(`/api/accounts/${accountSlug}/miniapp/theme`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          data: config.data,
          metadata: config.metadata,
        }),
      });

      if (!themeResponse.ok) {
        throw new Error('Failed to save theme data');
      }

      // 2. Save optimized CSS separately for production use
      const cssResponse = await fetch(`/api/accounts/${accountSlug}/miniapp/css`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          css: config.css,
          hash: generateCSSHash(config.css),
          metadata: config.metadata,
        }),
      });

      if (!cssResponse.ok) {
        throw new Error('Failed to save optimized CSS');
      }

      // 3. Update last saved timestamp
      setLastSaved(new Date());

      // 4. Show success notification
      showSuccessNotification(config);

      console.log('✅ Theme published successfully:', {
        classes: config.metadata.stats.uniqueClasses,
        cssSize: config.metadata.stats.cssSize,
        extractedAt: config.metadata.extractedAt,
      });

    } catch (error) {
      console.error('❌ Failed to publish theme:', error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }

  // Handle publish errors
  function handlePublishError(error: Error) {
    console.error('Publish error:', error);
    
    // Show error notification
    showErrorNotification(error.message);
  }

  // Handle progress updates
  function handleProgress(step: string, percentage: number) {
    console.log(`🔄 ${step} (${percentage}%)`);
    
    // You could update a progress bar here
    // setProgressState({ step, percentage });
  }

  // Handle data changes (auto-save draft)
  const handleChange = useCallback(async (newData: Data) => {
    setData(newData);

    // Auto-save draft (debounced)
    try {
      await fetch(`/api/accounts/${accountSlug}/miniapp/draft`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ data: newData }),
      });
    } catch (error) {
      console.warn('Failed to auto-save draft:', error);
    }
  }, [accountSlug]);

  // Handle preview
  const handlePreview = useCallback((newData: Data) => {
    // Open preview in new tab/window
    const previewUrl = `/preview/${accountSlug}/miniapp?data=${encodeURIComponent(JSON.stringify(newData))}`;
    window.open(previewUrl, '_blank');
  }, [accountSlug]);

  // Handle publish with CSS extraction
  const handlePublish = useCallback(async (newData: Data) => {
    await publish(newData);
  }, [publish]);

  return (
    <div className="customization-page">
      {/* Header */}
      <div className="customization-header">
        <div className="flex items-center justify-between p-4 border-b">
          <div>
            <h1 className="text-2xl font-bold">Theme Customization</h1>
            <p className="text-gray-600">
              Customize your mini app theme with real-time Tailwind CSS editing
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Last saved indicator */}
            {lastSaved && (
              <span className="text-sm text-gray-500">
                Last saved: {lastSaved.toLocaleTimeString()}
              </span>
            )}
            
            {/* Publishing status */}
            {isPublishing && (
              <div className="flex items-center gap-2 text-blue-600">
                <div className="animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full" />
                <span className="text-sm">Publishing...</span>
              </div>
            )}
            
            {/* Error indicator */}
            {error && (
              <div className="text-red-600 text-sm">
                ❌ {error.message}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      {progress && (
        <div className="progress-bar">
          <div className="bg-blue-50 border-b p-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-blue-700">{progress.step}</span>
              <span className="text-blue-600">{progress.percentage}%</span>
            </div>
            <div className="mt-2 w-full bg-blue-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress.percentage}%` }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Theme Builder */}
      <div className="theme-builder-container">
        <ThemeBuilder
          config={ZMP_PUCK_CONFIG}
          data={data}
          onChange={handleChange}
          onPreview={handlePreview}
          onPublish={handlePublish}
        />
      </div>

      {/* CSS Extraction Info */}
      <div className="css-info">
        <div className="fixed bottom-4 right-4 bg-white border rounded-lg shadow-lg p-4 max-w-sm">
          <h4 className="font-semibold text-gray-800 mb-2">🎨 CSS Optimization</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <div>• Real-time preview with RunCSS</div>
            <div>• Automatic CSS extraction on publish</div>
            <div>• Production-optimized output</div>
            <div>• Tailwind autocomplete enabled</div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Helper functions
function getDefaultData(): Data {
  return {
    content: [
      {
        type: "TailwindBox",
        props: {
          id: "welcome-box",
          content: "Welcome to your Mini App theme customization! Start by editing the Tailwind CSS classes to see real-time changes.",
          className: "p-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-lg",
          containerClassName: "max-w-2xl mx-auto my-8",
          textClassName: "text-white text-center text-lg",
          showPreview: true,
        },
      },
    ],
    root: {
      props: {
        title: "Mini App Theme",
        primaryColor: "#3b82f6",
        backgroundColor: "#ffffff",
        textColor: "#111827",
        fontFamily: "Inter, system-ui, sans-serif",
      },
    },
    zones: {},
  };
}

function generateCSSHash(css: string): string {
  let hash = 0;
  for (let i = 0; i < css.length; i++) {
    const char = css.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  return Math.abs(hash).toString(36);
}

function showSuccessNotification(config: OptimizedThemeConfig) {
  const message = `Theme published successfully!\n\n` +
    `📊 Stats:\n` +
    `• Classes: ${config.metadata.stats.uniqueClasses}\n` +
    `• CSS Size: ${(config.metadata.stats.cssSize / 1024).toFixed(2)} KB\n` +
    `• Generated: ${new Date(config.metadata.extractedAt).toLocaleString()}`;
  
  // You could replace this with a proper toast notification
  alert(message);
}

function showErrorNotification(message: string) {
  // You could replace this with a proper error notification
  alert(`Failed to publish theme: ${message}`);
}

export default CustomizationPageIntegration;
