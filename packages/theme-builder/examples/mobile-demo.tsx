import React, { useState } from 'react';
import { ThemeBuilder } from '../src/editor/ThemeBuilder';
import { ZMP_PUCK_CONFIG } from '../src/components/zmp-config';
import { Data } from '@measured/puck';

// Sample initial data for mobile demo
const initialData: Data = {
  content: [
    {
      type: "HeroSection",
      props: {
        id: "hero-1",
        businessName: "Trường Mầm Non ABC",
        slogan: "Nơi ươm mầm tri thức",
        description: "Chúng tôi cam kết mang đến chất lượng giáo dục tốt nhất cho con em bạn với đội ngũ giáo viên tận tâm và cơ sở vật chất hiện đại.",
        logo: "",
        backgroundGradient: "from-blue-600 to-purple-600",
        primaryButtonText: "Liên hệ tư vấn ngay",
        secondaryButtonText: "Xem chương trình học",
        primaryButtonAction: "/contact",
        secondaryButtonAction: "/programs",
        showStats: true,
        studentsCount: "500+",
        teachersCount: "50+",
        achievementsCount: "10+",
      },
    },
    {
      type: "FeaturesSection",
      props: {
        id: "features-1",
        title: "Tại sao chọn chúng tôi?",
        subtitle: "Những ưu điểm nổi bật",
        features: [
          {
            icon: "👨‍🏫",
            title: "Đội ngũ giáo viên chuyên nghiệp",
            description: "Giáo viên được đào tạo bài bản, có kinh nghiệm và tâm huyết với nghề"
          },
          {
            icon: "🏫",
            title: "Cơ sở vật chất hiện đại",
            description: "Phòng học được trang bị đầy đủ thiết bị học tập và vui chơi"
          },
          {
            icon: "📚",
            title: "Chương trình học tiên tiến",
            description: "Áp dụng phương pháp giáo dục hiện đại, phát triển toàn diện"
          }
        ]
      },
    },
    {
      type: "ContactSection",
      props: {
        id: "contact-1",
        title: "Liên hệ với chúng tôi",
        subtitle: "Đăng ký tư vấn miễn phí",
        phone: "0123 456 789",
        email: "<EMAIL>",
        address: "123 Đường ABC, Quận XYZ, TP.HCM",
        workingHours: "Thứ 2 - Thứ 6: 7:00 - 17:00",
        showContactForm: true,
      },
    },
  ],
  root: {
    props: {
      title: "Trường Mầm Non ABC",
      primaryColor: "#3b82f6",
      primaryLightColor: "#60a5fa",
      primaryDarkColor: "#1d4ed8",
      secondaryColor: "#8b5cf6",
      secondaryLightColor: "#a78bfa",
      secondaryDarkColor: "#7c3aed",
      accentColor: "#f59e0b",
      accentLightColor: "#fbbf24",
      accentDarkColor: "#d97706",
      backgroundColor: "#ffffff",
      paperBackgroundColor: "#f9fafb",
      textColor: "#111827",
      secondaryTextColor: "#6b7280",
      fontFamily: "Inter, system-ui, sans-serif",
      headingsFontFamily: "Inter, system-ui, sans-serif",
      headingsFontWeight: "600",
      bodyFontFamily: "Inter, system-ui, sans-serif",
      bodyFontWeight: "400",
      borderRadius: "8px",
      spacing: "16px",
    },
  },
  zones: {},
};

export function MobileThemeBuilderDemo() {
  const [data, setData] = useState<Data>(initialData);
  const [isSaving, setIsSaving] = useState(false);

  const handleChange = (newData: Data) => {
    setData(newData);
    console.log('Data changed:', newData);
  };

  const handlePreview = (newData: Data) => {
    console.log('Preview data:', newData);
    // Here you could open a preview modal or navigate to preview page
  };

  const handlePublish = async (newData: Data) => {
    setIsSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Save to localStorage for demo
      localStorage.setItem('mobile-theme-data', JSON.stringify(newData));
      
      console.log('Published data:', newData);
      alert('Theme published successfully!');
    } catch (error) {
      console.error('Publish error:', error);
      alert('Failed to publish theme');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div style={{ height: '100vh', width: '100vw' }}>
      <ThemeBuilder
        config={ZMP_PUCK_CONFIG}
        data={data}
        onChange={handleChange}
        onPreview={handlePreview}
        onPublish={handlePublish}
        {/* viewports and iframe are now auto-configured based on device type */}
      />
      
      {/* Loading overlay */}
      {isSaving && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9999,
          }}
        >
          <div
            style={{
              background: 'white',
              padding: '24px',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
            }}
          >
            <div
              style={{
                width: '20px',
                height: '20px',
                border: '2px solid #e5e7eb',
                borderTop: '2px solid #3b82f6',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
              }}
            />
            <span>Publishing theme...</span>
          </div>
        </div>
      )}
      
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}

// Usage example
export default function App() {
  return <MobileThemeBuilderDemo />;
}
