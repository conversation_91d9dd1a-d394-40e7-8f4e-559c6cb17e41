import React, { useState } from 'react';
import { ThemeBuilder } from '../src/editor/ThemeBuilder';
import { ZMP_PUCK_CONFIG } from '../src/components/zmp-config';
import { Data } from '@measured/puck';
import { useThemeBuilderPublish } from '../src/hooks/useOptimizedPublish';

// Demo data showcasing Tailwind CSS field
const tailwindFieldDemoData: Data = {
  content: [
    {
      type: "TailwindBox",
      props: {
        id: "tailwind-box-1",
        content: "🎨 Welcome to Tailwind CSS Field Demo!\n\nThis box demonstrates real-time Tailwind CSS editing with autocomplete suggestions and live preview.",
        className: "p-8 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-2xl shadow-2xl border border-white/20",
        containerClassName: "max-w-2xl mx-auto my-8",
        textClassName: "text-white text-center text-lg font-medium leading-relaxed",
        showPreview: true,
      },
    },
    
    {
      type: "TailwindBox",
      props: {
        id: "tailwind-box-2",
        content: "⚡ Real-time CSS Generation\n\nAs you type Tailwind classes, RunCSS generates the CSS in real-time. Try changing the classes above!",
        className: "p-6 bg-white rounded-xl shadow-lg border-2 border-gray-200 hover:border-blue-300 transition-colors duration-300",
        containerClassName: "max-w-xl mx-auto my-6",
        textClassName: "text-gray-800 text-center font-semibold",
        showPreview: true,
      },
    },
    
    {
      type: "TailwindBox",
      props: {
        id: "tailwind-box-3",
        content: "🚀 Autocomplete Features\n\n• Type 'bg-' for background colors\n• Type 'text-' for text colors\n• Type 'p-' for padding\n• Type 'hover:' for hover states",
        className: "p-6 bg-gray-900 rounded-lg shadow-inner",
        containerClassName: "max-w-lg mx-auto my-6",
        textClassName: "text-green-400 font-mono text-sm leading-loose",
        showPreview: true,
      },
    },
    
    {
      type: "TailwindBox",
      props: {
        id: "tailwind-box-4",
        content: "📱 Mobile Responsive\n\nTry adding responsive prefixes like 'sm:', 'md:', 'lg:' to see how classes adapt to different screen sizes.",
        className: "p-4 sm:p-6 md:p-8 bg-yellow-100 sm:bg-yellow-200 md:bg-yellow-300 rounded-lg sm:rounded-xl md:rounded-2xl border-2 border-yellow-400",
        containerClassName: "max-w-md sm:max-w-lg md:max-w-xl mx-auto my-6",
        textClassName: "text-yellow-900 text-sm sm:text-base md:text-lg font-bold text-center",
        showPreview: true,
      },
    },
    
    {
      type: "TailwindBox",
      props: {
        id: "tailwind-box-5",
        content: "🎯 Interactive States\n\nHover over this box to see the transition effects. You can add 'hover:', 'focus:', 'active:' prefixes to any class.",
        className: "p-6 bg-indigo-500 hover:bg-indigo-600 rounded-xl shadow-md hover:shadow-xl transform hover:scale-105 transition-all duration-300 cursor-pointer",
        containerClassName: "max-w-lg mx-auto my-6",
        textClassName: "text-white text-center font-semibold",
        showPreview: true,
      },
    },
  ],
  root: {
    props: {
      title: "Tailwind CSS Field Demo",
      primaryColor: "#3b82f6",
      primaryLightColor: "#60a5fa",
      primaryDarkColor: "#1d4ed8",
      secondaryColor: "#8b5cf6",
      secondaryLightColor: "#a78bfa",
      secondaryDarkColor: "#7c3aed",
      accentColor: "#f59e0b",
      accentLightColor: "#fbbf24",
      accentDarkColor: "#d97706",
      backgroundColor: "#f8fafc",
      paperBackgroundColor: "#ffffff",
      textColor: "#111827",
      secondaryTextColor: "#6b7280",
      fontFamily: "Inter, system-ui, sans-serif",
      headingsFontFamily: "Inter, system-ui, sans-serif",
      headingsFontWeight: "600",
      bodyFontFamily: "Inter, system-ui, sans-serif",
      bodyFontWeight: "400",
      borderRadius: "8px",
      spacing: "16px",
    },
  },
  zones: {},
};

export function TailwindFieldDemo() {
  const [data, setData] = useState<Data>(tailwindFieldDemoData);
  const { publish, isPublishing, error, progress } = useThemeBuilderPublish();

  const handleChange = (newData: Data) => {
    setData(newData);
    console.log('Tailwind Field Demo - Data changed:', newData);
  };

  const handlePreview = (newData: Data) => {
    console.log('Tailwind Field Demo - Preview:', newData);
  };

  const handlePublish = async (newData: Data) => {
    console.log('Tailwind Field Demo - Publishing with CSS extraction:', newData);
    await publish(newData);
  };

  return (
    <div style={{ height: '100vh', width: '100vw' }}>
      <ThemeBuilder
        config={ZMP_PUCK_CONFIG}
        data={data}
        onChange={handleChange}
        onPreview={handlePreview}
        onPublish={handlePublish}
      />
      
      {/* Publishing Progress */}
      {(isPublishing || progress) && (
        <div
          style={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            background: 'rgba(0, 0, 0, 0.9)',
            color: 'white',
            padding: '24px',
            borderRadius: '12px',
            zIndex: 9999,
            minWidth: '300px',
            textAlign: 'center',
          }}
        >
          <div style={{ marginBottom: '16px' }}>
            <div
              style={{
                width: '40px',
                height: '40px',
                border: '3px solid #374151',
                borderTop: '3px solid #3b82f6',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                margin: '0 auto 12px',
              }}
            />
            <h3 style={{ margin: '0 0 8px 0', fontSize: '18px' }}>
              {isPublishing ? 'Publishing Theme...' : 'Processing...'}
            </h3>
            {progress && (
              <>
                <p style={{ margin: '0 0 12px 0', fontSize: '14px', opacity: 0.8 }}>
                  {progress.step}
                </p>
                <div
                  style={{
                    width: '100%',
                    height: '6px',
                    background: '#374151',
                    borderRadius: '3px',
                    overflow: 'hidden',
                  }}
                >
                  <div
                    style={{
                      width: `${progress.percentage}%`,
                      height: '100%',
                      background: 'linear-gradient(90deg, #3b82f6, #8b5cf6)',
                      transition: 'width 0.3s ease',
                    }}
                  />
                </div>
                <p style={{ margin: '8px 0 0 0', fontSize: '12px', opacity: 0.6 }}>
                  {progress.percentage}%
                </p>
              </>
            )}
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div
          style={{
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: '#fee2e2',
            color: '#dc2626',
            padding: '16px',
            borderRadius: '8px',
            border: '1px solid #fecaca',
            maxWidth: '400px',
            zIndex: 9999,
          }}
        >
          <h4 style={{ margin: '0 0 8px 0', fontSize: '16px' }}>❌ Publish Error</h4>
          <p style={{ margin: 0, fontSize: '14px' }}>{error.message}</p>
        </div>
      )}
      
      {/* Demo Instructions */}
      <div
        style={{
          position: 'fixed',
          top: '10px',
          left: '10px',
          background: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          padding: '16px',
          borderRadius: '8px',
          fontSize: '12px',
          maxWidth: '350px',
          zIndex: 9999,
        }}
      >
        <h4 style={{ margin: '0 0 12px 0', fontSize: '14px' }}>🎨 Tailwind CSS Field Demo</h4>
        <div style={{ marginBottom: '12px' }}>
          <strong>Features:</strong>
          <ul style={{ margin: '4px 0 0 0', paddingLeft: '16px' }}>
            <li>Real-time CSS generation with RunCSS</li>
            <li>Autocomplete for Tailwind classes</li>
            <li>Live preview in editor</li>
            <li>CSS extraction on publish</li>
          </ul>
        </div>
        <div style={{ marginBottom: '12px' }}>
          <strong>Try typing:</strong>
          <ul style={{ margin: '4px 0 0 0', paddingLeft: '16px', fontSize: '11px' }}>
            <li><code>bg-red-500</code> - Background color</li>
            <li><code>hover:bg-blue-600</code> - Hover state</li>
            <li><code>sm:text-lg</code> - Responsive text</li>
            <li><code>transform scale-105</code> - Transforms</li>
          </ul>
        </div>
        <p style={{ margin: 0, fontSize: '11px', opacity: 0.8 }}>
          Click on any TailwindBox component and edit the class fields to see real-time updates!
        </p>
      </div>

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}

export default TailwindFieldDemo;
