# Mobile Customization Guide

This guide explains how the theme builder has been customized for optimal mobile experience.

## 🎯 Mobile-First Approach

The theme builder automatically detects mobile devices (screen width < 768px) and switches to a mobile-optimized interface inspired by the Puck custom UI demo.

## 🏗️ Architecture

### Mobile Detection
```tsx
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return isMobile;
};
```

### Mobile Layout Components

#### 1. MobileHeader
- Sticky header with action buttons
- Preview/Edit mode toggle
- Publish and Preview buttons
- Compact design for mobile screens

#### 2. MobileTabs
- Bottom tab navigation
- Collapsible interface
- Touch-friendly tab switching
- Smooth scroll behavior

#### 3. MobilePuck
- Full-screen mobile layout
- Bottom action bar with tabs
- Touch-optimized interactions
- Responsive preview area

## 📱 Mobile UI Features

### Bottom Tab Navigation
```tsx
const tabs = [
  { label: "Components", body: <Puck.Components /> },
  { label: "Fields", body: <Puck.Fields /> },
  { label: "Outline", body: <Puck.Outline /> },
];
```

### Touch-Friendly Controls
- Minimum 44px touch targets
- Larger padding and margins
- Optimized button sizes
- Smooth animations and feedback

### Responsive Behavior
- Automatic layout switching
- Adaptive component sizing
- Mobile-optimized scrolling
- Safe area support

## 🎨 CSS Customizations

### Mobile-Specific Styles
```css
@media (max-width: 768px) {
  /* Hide default Puck sidebar */
  [data-puck-sidebar] {
    display: none !important;
  }

  /* Touch-friendly component buttons */
  [data-puck-component-list] button {
    min-height: 48px !important;
    padding: 12px 16px !important;
    font-size: 14px !important;
    touch-action: manipulation !important;
  }

  /* Mobile-optimized form fields */
  [data-puck-fields] input,
  [data-puck-fields] select,
  [data-puck-fields] textarea {
    min-height: 44px !important;
    font-size: 16px !important; /* Prevent zoom on iOS */
  }
}
```

### Touch Interactions
```css
/* Touch-friendly drag handles */
[data-puck-drag-handle] {
  min-width: 44px !important;
  min-height: 44px !important;
  touch-action: manipulation !important;
}

/* Optimized drop zones */
[data-puck-drop-zone] {
  min-height: 60px !important;
  border: 2px dashed #d1d5db !important;
}
```

## 🔧 Implementation Details

### Puck Overrides
The mobile interface uses Puck's override system to completely replace the default UI:

```tsx
<Puck
  config={config}
  data={data}
  onChange={onChange}
  overrides={{
    puck: () => (
      <MobilePuck
        config={config}
        data={data}
        onChange={onChange}
        onPublish={onPublish}
        onPreview={onPreview}
      />
    ),
  }}
/>
```

### State Management
- Tab state management
- Scroll position tracking
- Touch interaction handling
- Responsive behavior

### Performance Optimizations
- Lazy loading of tab content
- Optimized re-renders
- Touch event optimization
- Memory-efficient state updates

## 📲 Usage Examples

### Basic Mobile Setup
```tsx
import { ThemeBuilder } from '@kit/theme-builder';

function MobileApp() {
  return (
    <ThemeBuilder
      config={ZMP_PUCK_CONFIG}
      data={initialData}
      onChange={handleChange}
      onPublish={handlePublish}
      // No need to specify viewports or iframe - auto-configured!
    />
  );
}
```

### Auto-Configuration Details

The ThemeBuilder automatically configures itself based on device detection:

#### Mobile Devices (< 768px):
- **Viewports**: iPhone 13 (375x812), iPhone 14 (390x844), Android (360x800)
- **Iframe**: Disabled for better performance
- **UI**: Bottom tabs, touch-optimized controls
- **Layout**: Full-screen mobile interface

#### Desktop Devices (>= 768px):
- **Viewports**: Large desktop editor (1200x800) for easy drag & drop
- **Iframe**: Enabled by default
- **UI**: Traditional sidebar layout
- **Layout**: Standard Puck interface with wide preview area

### Manual Override (Optional)
```tsx
// You can still override auto-configuration if needed:
<ThemeBuilder
  config={ZMP_PUCK_CONFIG}
  data={initialData}
  viewports={[
    { width: 414, height: 896, label: 'iPhone 11', icon: '📱' },
  ]}
  iframe={{ enabled: true }}
  onChange={handleChange}
  onPublish={handlePublish}
/>
```

### Custom Mobile Actions
```tsx
const mobileActions = [
  {
    type: 'button',
    label: 'Save Draft',
    onClick: handleSaveDraft,
    variant: 'secondary',
  },
  {
    type: 'button',
    label: 'Publish',
    onClick: handlePublish,
    variant: 'primary',
  },
];

<ThemeBuilder
  headerActions={mobileActions}
  // ... other props
/>
```

## 🎯 Best Practices

### Touch Targets
- Minimum 44px for all interactive elements
- Adequate spacing between touch targets
- Clear visual feedback for interactions

### Performance
- Use `touch-action: manipulation` for better touch response
- Optimize scroll performance with `overscroll-behavior`
- Minimize layout shifts during interactions

### Accessibility
- Maintain keyboard navigation support
- Ensure proper focus management
- Use semantic HTML elements

### User Experience
- Provide clear visual feedback
- Use smooth animations and transitions
- Implement intuitive gesture support

## 🔄 Future Enhancements

### Planned Features
- Swipe gestures for tab navigation
- Pinch-to-zoom in preview mode
- Voice commands for accessibility
- Offline editing capabilities

### Performance Improvements
- Virtual scrolling for large component lists
- Progressive loading of preview content
- Optimized bundle splitting for mobile

## 🐛 Troubleshooting

### Common Issues
1. **Touch events not working**: Ensure `touch-action: manipulation` is set
2. **Zoom on input focus**: Use `font-size: 16px` on form inputs
3. **Scroll issues**: Check `overscroll-behavior` settings
4. **Layout shifts**: Verify viewport meta tag configuration

### Debug Tips
- Use browser dev tools mobile simulation
- Test on actual devices
- Monitor performance with React DevTools
- Check console for touch event warnings
