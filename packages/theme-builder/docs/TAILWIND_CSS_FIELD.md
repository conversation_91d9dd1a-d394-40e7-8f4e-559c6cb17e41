# Tailwind CSS Custom Field

## 🎯 Overview

The Tailwind CSS Custom Field provides a powerful autocomplete input for Tailwind CSS classes with real-time preview using RunCSS. This field is specifically designed for Puck Editor to enable dynamic styling without build-time constraints.

## ✨ Features

### 🔄 **Real-time CSS Generation**
- Uses RunCSS to generate CSS on-the-fly in the editor
- No build step required for immediate visual feedback
- Perfect for dynamic content where classes aren't known at build time

### 🎯 **Smart Autocomplete**
- 500+ built-in Tailwind CSS classes
- Intelligent filtering based on current input
- Keyboard navigation (Arrow keys, Enter, Escape)
- Click-to-select functionality

### 📱 **Responsive & State Support**
- Responsive prefixes: `sm:`, `md:`, `lg:`, `xl:`, `2xl:`
- State prefixes: `hover:`, `focus:`, `active:`, `visited:`, etc.
- Complete Tailwind utility coverage

### ⚡ **Performance Optimized**
- CSS extraction and optimization on publish
- Separate runtime (editor) vs production (optimized) CSS
- Minimal bundle size impact

## 🛠️ Implementation

### Basic Usage

```tsx
import { TailwindClassField } from '@kit/theme-builder/fields';

// In your component configuration
fields: {
  className: {
    type: 'custom',
    label: 'CSS Classes',
    render: ({ name, onChange, value, field }) => (
      <TailwindClassField
        name={name}
        value={value}
        onChange={onChange}
        field={{
          label: 'Container Classes',
          placeholder: 'p-4 bg-blue-500 rounded-lg...',
        }}
      />
    ),
  },
}
```

### Component Example

```tsx
export const TailwindBox: ComponentConfig<TailwindBoxProps> = {
  label: 'Tailwind Box',
  fields: {
    className: {
      type: 'custom',
      render: ({ name, onChange, value }) => (
        <TailwindClassField
          name={name}
          value={value}
          onChange={onChange}
          field={{
            label: 'Box Classes',
            placeholder: 'p-6 bg-gradient-to-r from-blue-500...',
          }}
        />
      ),
    },
  },
  render: ({ className }) => (
    <div className={className}>
      Content with dynamic Tailwind classes
    </div>
  ),
};
```

## 🔧 Technical Architecture

### Editor Runtime (RunCSS)
```tsx
// Loads RunCSS for real-time preview
useEffect(() => {
  if (typeof window !== 'undefined' && !window.RunCSS) {
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/runcss/dist/runcss.min.js';
    script.defer = true;
    script.setAttribute('watch', '');
    document.head.appendChild(script);
  }
}, []);

// Process classes in real-time
useEffect(() => {
  if (window.RunCSS && inputValue) {
    window.RunCSS.processClasses?.(inputValue);
  }
}, [inputValue]);
```

### Production Optimization
```tsx
import { useOptimizedPublish } from '@kit/theme-builder/hooks';

const { publish } = useOptimizedPublish({
  onPublish: async (config) => {
    // config.css contains optimized CSS
    // config.data contains theme data
    // config.metadata contains extraction stats
    
    await saveThemeConfig(config);
  },
});
```

## 📊 CSS Extraction Process

### 1. **Class Detection**
```typescript
// Automatically detects Tailwind classes in these fields
const TAILWIND_FIELD_KEYS = [
  'className',
  'containerClassName',
  'wrapperClassName',
  'contentClassName',
  'customClassName',
  'cssClasses',
  'tailwindClasses',
];
```

### 2. **CSS Generation**
```typescript
// Extract classes from Puck data
const classes = extractTailwindClasses(data);

// Generate optimized CSS
const css = await generateCSSFromClasses(classes);

// Create optimized config
const optimizedConfig = {
  data,
  css,
  metadata: {
    extractedAt: new Date().toISOString(),
    classes,
    stats: { totalClasses, uniqueClasses, cssSize },
  },
};
```

### 3. **Production Usage**
```tsx
// In production, use pre-generated CSS
<style dangerouslySetInnerHTML={{ __html: optimizedConfig.css }} />
<div className={className}>Content</div>
```

## 🎨 Autocomplete Features

### Class Categories
- **Layout**: `flex`, `grid`, `block`, `inline`, `hidden`
- **Spacing**: `p-4`, `m-2`, `px-6`, `py-3`, `mx-auto`
- **Sizing**: `w-full`, `h-screen`, `max-w-md`, `min-h-0`
- **Colors**: `text-blue-500`, `bg-red-600`, `border-gray-300`
- **Typography**: `text-lg`, `font-bold`, `text-center`, `uppercase`
- **Effects**: `shadow-lg`, `rounded-xl`, `border-2`, `opacity-75`

### Smart Filtering
```typescript
// Filters based on current word being typed
const words = input.split(' ');
const currentWord = words[words.length - 1];

const filtered = TAILWIND_CLASSES
  .filter(className => 
    className.toLowerCase().includes(currentWord.toLowerCase()) ||
    className.toLowerCase().startsWith(currentWord.toLowerCase())
  )
  .slice(0, 10);
```

## 🚀 Performance Benefits

### Editor Experience
- **Real-time Preview**: Immediate visual feedback
- **No Build Delays**: Instant class application
- **Rich Autocomplete**: Faster development

### Production Optimization
- **Minimal CSS**: Only used classes included
- **No Runtime Overhead**: Pre-generated CSS
- **Better Performance**: Smaller bundle sizes

### Statistics Example
```json
{
  "stats": {
    "totalClasses": 45,
    "uniqueClasses": 32,
    "cssSize": 2048
  }
}
```

## 🎯 Best Practices

### 1. **Field Naming**
Use consistent field names for automatic detection:
```tsx
fields: {
  className: { /* main container */ },
  containerClassName: { /* wrapper */ },
  textClassName: { /* text styling */ },
}
```

### 2. **Placeholder Guidance**
Provide helpful placeholders:
```tsx
field={{
  label: 'Button Classes',
  placeholder: 'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600...',
}}
```

### 3. **Preview Integration**
Show live preview in development:
```tsx
{showPreview && puck.isEditing && (
  <div className="preview">
    <code>{className}</code>
  </div>
)}
```

## 🔮 Advanced Usage

### Custom Class Validation
```tsx
import { validateTailwindClasses } from '@kit/theme-builder/utils';

const { valid, invalid, warnings } = validateTailwindClasses(classes);
```

### CSS Minification
```tsx
import { minifyCSS } from '@kit/theme-builder/utils';

const minified = minifyCSS(css);
```

### Hash Generation
```tsx
import { generateCSSHash } from '@kit/theme-builder/utils';

const hash = generateCSSHash(css); // For caching
```

## 🧪 Testing

### Demo Component
```tsx
import { TailwindFieldDemo } from '@kit/theme-builder/examples';

<TailwindFieldDemo />
```

### Manual Testing
1. **Type Classes**: Try typing `bg-`, `text-`, `p-`, etc.
2. **Use Autocomplete**: Arrow keys + Enter to select
3. **Test Responsive**: Add `sm:`, `md:`, `lg:` prefixes
4. **Test States**: Add `hover:`, `focus:` prefixes
5. **Publish**: Check CSS extraction in console

## 🔧 Troubleshooting

### Common Issues

1. **RunCSS Not Loading**
   - Check network connectivity
   - Verify CDN availability
   - Check browser console for errors

2. **Classes Not Applying**
   - Ensure RunCSS is loaded
   - Check for CSS conflicts
   - Verify class syntax

3. **Autocomplete Not Working**
   - Check input focus
   - Verify class database loading
   - Test with simple classes first

### Debug Mode
```tsx
// Enable debug logging
window.RUNCSS_DEBUG = true;
```

The Tailwind CSS Custom Field provides a powerful, flexible way to add dynamic styling to your Puck components with real-time preview and production optimization! 🎨⚡
