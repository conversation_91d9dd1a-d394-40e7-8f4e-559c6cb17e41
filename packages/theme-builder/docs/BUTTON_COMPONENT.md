# Enhanced Button Component for Zalo Mini Apps

## 🎯 Overview

The enhanced Button component is specifically designed for Zalo Mini Apps with advanced features including dynamic fields, multiple action types, animations, and Zalo-specific integrations.

## ✨ Features

### 🔄 Dynamic Fields
- **Smart UI**: Fields appear/disappear based on selected action type
- **Context-aware**: Only relevant options are shown to users
- **Clean Interface**: Reduces clutter and improves UX

### 📱 Zalo Mini App Actions

#### 1. **🔗 Link Action**
- Standard web links
- **Open in App**: Option to open within Zalo Mini App
- **External Links**: Open in external browser

#### 2. **📞 Phone Call Action**
- Direct phone dialing
- Supports international formats
- One-click calling functionality

#### 3. **💬 Chat Action**
- **User Chat**: Open chat with specific user
- **Official Account**: Open chat with OA
- **Pre-filled Message**: Auto-populate message content
- **Zalo API Integration**: Uses `openChat` API

#### 4. **📤 Share Action**
- **Native Sharing**: Uses Web Share API when available
- **Custom Title**: Set share title
- **Description**: Add share description
- **Image URL**: Include share image

#### 5. **⚙️ Custom Action**
- **JavaScript Execution**: Run custom code
- **Flexible Integration**: Handle any custom logic
- **Error Handling**: Safe execution with try-catch

### 🎨 Enhanced Styling

#### **Color Variants**
- 🔵 **Primary**: Default theme color
- ⚪ **Secondary**: Light background with border
- 👻 **Ghost**: Transparent with border
- 🟦 **Zalo Blue**: Official Zalo brand color (#0068ff)
- 🟢 **Success**: Green for positive actions (#52c41a)
- 🟡 **Warning**: Yellow for caution (#faad14)
- 🔴 **Danger**: Red for destructive actions (#ff4d4f)

#### **Sizes**
- **Small**: Compact for tight spaces
- **Medium**: Standard size (default)
- **Large**: Prominent for important actions

### 🎭 Animations & Effects

#### **Animation Types**
- **None**: Static button
- **Bounce**: Playful bouncing effect
- **Pulse**: Subtle scaling animation
- **Shake**: Attention-grabbing shake

#### **Interactive Effects**
- **Ripple Effect**: Material Design-style ripple on click
- **Hover States**: Smooth transitions and shadows
- **Focus States**: Accessibility-friendly focus indicators

## 🛠️ Usage Examples

### Basic Link Button
```tsx
<Button
  label="Visit Website"
  actionType="link"
  href="https://example.com"
  variant="primary"
  size="medium"
/>
```

### Phone Call Button
```tsx
<Button
  label="Call Support"
  actionType="phone"
  phoneNumber="+84 123 456 789"
  variant="success"
  icon="📞"
  iconPosition="left"
/>
```

### Zalo Chat Button
```tsx
<Button
  label="Chat with Support"
  actionType="chat"
  chatType="oa"
  chatId="your-oa-id"
  chatMessage="Xin chào, tôi cần hỗ trợ về sản phẩm"
  variant="zalo"
  icon="💬"
/>
```

### Share Button
```tsx
<Button
  label="Share This"
  actionType="share"
  shareTitle="Amazing Content"
  shareDescription="Check out this amazing content!"
  shareImageUrl="https://example.com/image.jpg"
  variant="secondary"
  animationType="pulse"
/>
```

### Custom Action Button
```tsx
<Button
  label="Custom Action"
  actionType="custom"
  customAction="alert('Custom action executed!');"
  variant="warning"
  showRipple={true}
  animationType="bounce"
/>
```

## 🔧 Technical Implementation

### Dynamic Fields Pattern
```tsx
fields: ({ data }) => {
  const actionType = data?.actionType || 'link';
  
  return {
    // Base fields always shown
    label: { type: "text", ... },
    actionType: { type: "select", ... },
    
    // Conditional fields based on actionType
    ...(actionType === 'link' && {
      href: { type: "text", ... },
      openInApp: { type: "radio", ... },
    }),
    
    ...(actionType === 'phone' && {
      phoneNumber: { type: "text", ... },
    }),
    
    // ... other conditional fields
  };
}
```

### Action Handler
```tsx
const handleAction = async (e: React.MouseEvent) => {
  switch (actionType) {
    case 'phone':
      window.location.href = `tel:${phoneNumber}`;
      break;
      
    case 'chat':
      await window.ZaloJavaScriptInterface?.openChat({
        type: chatType,
        id: chatId,
        message: chatMessage,
      });
      break;
      
    case 'share':
      await navigator.share({
        title: shareTitle,
        text: shareDescription,
        url: shareImageUrl,
      });
      break;
      
    // ... other cases
  }
};
```

## 🎯 Best Practices

### 1. **Action Type Selection**
- Use **Link** for navigation
- Use **Phone** for direct contact
- Use **Chat** for customer support
- Use **Share** for content promotion
- Use **Custom** for complex interactions

### 2. **Visual Design**
- Use **Primary** for main actions
- Use **Zalo** for Zalo-specific features
- Use **Success** for positive confirmations
- Use **Danger** for destructive actions

### 3. **Animations**
- Use **None** for professional interfaces
- Use **Pulse** for call-to-action buttons
- Use **Bounce** for playful interactions
- Use **Shake** for error states

### 4. **Accessibility**
- Always provide meaningful labels
- Use appropriate color contrast
- Include focus indicators
- Test with screen readers

## 🚀 Performance Considerations

- **Conditional Rendering**: Only loads relevant fields
- **Lazy Execution**: Actions only execute when needed
- **Error Handling**: Graceful fallbacks for API failures
- **Memory Management**: Proper cleanup of event listeners

## 🔮 Future Enhancements

- **Voice Commands**: Integration with voice APIs
- **Gesture Support**: Swipe and long-press actions
- **Analytics**: Built-in action tracking
- **A/B Testing**: Variant testing capabilities
