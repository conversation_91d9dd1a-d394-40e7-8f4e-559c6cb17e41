/**
 * RunCSS Extractor Utility
 * 
 * This utility extracts Tailwind CSS classes from Puck data and generates
 * optimized CSS using runcss for production use.
 */

import { Data } from '@measured/puck';
import { buildProductionCSS, batchProcessClasses } from '../services/runcss-service';

import { TAILWIND_FIELD_NAMES } from './tailwind-field-utils';

// Field keys that contain Tailwind classes - use standardized names
const TAILWIND_FIELD_KEYS = Object.values(TAILWIND_FIELD_NAMES);

/**
 * Extract all Tailwind CSS classes from Puck data
 */
export function extractTailwindClasses(data: Data): string[] {
  const classes = new Set<string>();

  // Recursive function to traverse data structure
  function traverse(obj: any) {
    if (!obj || typeof obj !== 'object') return;

    if (Array.isArray(obj)) {
      obj.forEach(traverse);
      return;
    }

    // Check each property
    for (const [key, value] of Object.entries(obj)) {
      // If key matches Tailwind field pattern
      if (TAILWIND_FIELD_KEYS.includes(key) && typeof value === 'string') {
        // Split classes and add to set
        const classNames = value.split(/\s+/).filter(Boolean);
        classNames.forEach(className => classes.add(className));
      }
      
      // Recursively traverse nested objects
      if (typeof value === 'object') {
        traverse(value);
      }
    }
  }

  // Start traversal from root
  traverse(data);

  return Array.from(classes);
}

/**
 * Generate CSS from Tailwind classes using runcss service
 */
export async function generateCSSFromClasses(classes: string[]): Promise<string> {
  try {
    const result = buildProductionCSS(classes);
    return result.css;
  } catch (error) {
    console.error('Failed to generate CSS with RunCSS:', error);
    return '';
  }
}

/**
 * Extract and build optimized CSS from Puck data
 */
export async function buildOptimizedCSS(data: Data): Promise<{
  classes: string[];
  css: string;
  minified: string;
  hash: string;
  stats: {
    totalClasses: number;
    uniqueClasses: number;
    cssSize: number;
    originalSize: number;
    minifiedSize: number;
    compressionRatio: number;
  };
}> {
  // Extract all Tailwind classes
  const classes = extractTailwindClasses(data);
  const uniqueClasses = Array.from(new Set(classes));

  // Generate optimized CSS using service
  const result = buildProductionCSS(uniqueClasses);

  // Calculate additional stats
  const stats = {
    totalClasses: classes.length,
    uniqueClasses: uniqueClasses.length,
    cssSize: result.size,
    originalSize: result.stats.originalSize,
    minifiedSize: result.stats.minifiedSize,
    compressionRatio: result.stats.compressionRatio,
  };

  return {
    classes: uniqueClasses,
    css: result.css,
    minified: result.minified,
    hash: result.hash,
    stats,
  };
}

/**
 * Create optimized theme config with extracted CSS
 */
export async function createOptimizedThemeConfig(data: Data): Promise<{
  data: Data;
  css: string;
  minified: string;
  hash: string;
  metadata: {
    extractedAt: string;
    classes: string[];
    stats: {
      totalClasses: number;
      uniqueClasses: number;
      cssSize: number;
      originalSize: number;
      minifiedSize: number;
      compressionRatio: number;
    };
  };
}> {
  const { classes, css, minified, hash, stats } = await buildOptimizedCSS(data);

  return {
    data,
    css,
    minified,
    hash,
    metadata: {
      extractedAt: new Date().toISOString(),
      classes,
      stats,
    },
  };
}

/**
 * Validate Tailwind class names
 */
export function validateTailwindClasses(classes: string[]): {
  valid: string[];
  invalid: string[];
  warnings: string[];
} {
  const valid: string[] = [];
  const invalid: string[] = [];
  const warnings: string[] = [];

  // Basic Tailwind class patterns
  const patterns = [
    /^(sm|md|lg|xl|2xl):/,  // Responsive prefixes
    /^(hover|focus|active|visited|disabled|checked|first|last|odd|even):/,  // State prefixes
    /^(p|m|px|py|pt|pr|pb|pl|mx|my|mt|mr|mb|ml)-\d+$/,  // Spacing
    /^(w|h|min-w|min-h|max-w|max-h)-/,  // Sizing
    /^text-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|7xl|8xl|9xl)$/,  // Text sizes
    /^text-\w+-\d+$/,  // Text colors
    /^bg-\w+-\d+$/,  // Background colors
    /^border-\w+-\d+$/,  // Border colors
    /^(flex|grid|block|inline|hidden|relative|absolute|fixed|sticky)$/,  // Layout
    /^(rounded|shadow|border)/,  // Effects
  ];

  classes.forEach(className => {
    if (!className.trim()) return;

    // Check if class matches any pattern
    const isValid = patterns.some(pattern => pattern.test(className)) ||
                   ['container', 'flex', 'grid', 'block', 'inline', 'hidden'].includes(className);

    if (isValid) {
      valid.push(className);
    } else {
      // Check for potential typos or custom classes
      if (className.includes('-') && !className.startsWith('_')) {
        warnings.push(`Potential custom class: ${className}`);
        valid.push(className); // Include it anyway
      } else {
        invalid.push(className);
      }
    }
  });

  return { valid, invalid, warnings };
}

/**
 * Minify CSS output
 */
export function minifyCSS(css: string): string {
  return css
    .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
    .replace(/\s+/g, ' ') // Collapse whitespace
    .replace(/;\s*}/g, '}') // Remove last semicolon in blocks
    .replace(/\s*{\s*/g, '{') // Clean up braces
    .replace(/\s*}\s*/g, '}')
    .replace(/\s*;\s*/g, ';') // Clean up semicolons
    .replace(/\s*,\s*/g, ',') // Clean up commas
    .trim();
}

/**
 * Generate CSS hash for caching
 */
export function generateCSSHash(css: string): string {
  let hash = 0;
  for (let i = 0; i < css.length; i++) {
    const char = css.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

/**
 * Create CSS file content with metadata
 */
export function createCSSFileContent(css: string, metadata: any): string {
  const minified = minifyCSS(css);
  const hash = generateCSSHash(minified);
  
  return `/* Generated by RunCSS Extractor */
/* Generated at: ${metadata.extractedAt} */
/* Classes: ${metadata.stats.uniqueClasses} */
/* Size: ${metadata.stats.cssSize} bytes */
/* Hash: ${hash} */

${minified}`;
}

export default {
  extractTailwindClasses,
  generateCSSFromClasses,
  buildOptimizedCSS,
  createOptimizedThemeConfig,
  validateTailwindClasses,
  minifyCSS,
  generateCSSHash,
  createCSSFileContent,
};
