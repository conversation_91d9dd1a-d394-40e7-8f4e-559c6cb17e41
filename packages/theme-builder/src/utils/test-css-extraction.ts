/**
 * Test CSS Extraction
 * 
 * Utility to test CSS extraction from Puck data with standardized Tailwind fields
 */

import { Data } from '@measured/puck';
import { extractTailwindClasses, buildOptimizedCSS } from './runcss-extractor';
import { TAILWIND_FIELD_NAMES } from './tailwind-field-utils';

// Test data with various Tailwind classes
export const TEST_PUCK_DATA: Data = {
  content: [
    {
      type: "Text",
      props: {
        id: "text-1",
        text: "Hello World",
        tag: "h1",
        className: "p-4 bg-blue-500 rounded-lg shadow-md",
        textClassName: "text-white text-2xl font-bold text-center",
      },
    },
    {
      type: "Button",
      props: {
        id: "button-1",
        label: "Click Me",
        variant: "primary",
        className: "mt-4 mx-auto",
        buttonClassName: "px-6 py-3 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg transform hover:scale-105 transition-all duration-200",
      },
    },
    {
      type: "Card",
      props: {
        id: "card-1",
        title: "Sample Card",
        subtitle: "This is a test card",
        className: "max-w-md mx-auto my-8",
        containerClassName: "bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden",
        headerClassName: "p-6 bg-gradient-to-r from-purple-500 to-pink-500",
        bodyClassName: "p-6 space-y-4",
        footerClassName: "p-4 bg-gray-50 border-t border-gray-200",
      },
    },
    {
      type: "Input",
      props: {
        id: "input-1",
        label: "Email Address",
        placeholder: "Enter your email",
        type: "email",
        className: "mb-4",
        inputClassName: "w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-colors",
        labelClassName: "block text-sm font-semibold text-gray-700 mb-2",
      },
    },
    {
      type: "TailwindBox",
      props: {
        id: "tailwind-box-1",
        content: "Custom Tailwind Box",
        className: "relative overflow-hidden",
        containerClassName: "max-w-2xl mx-auto my-12 transform hover:scale-102 transition-transform duration-300",
        textClassName: "text-lg text-gray-800 leading-relaxed text-center",
      },
    },
  ],
  root: {
    props: {
      title: "Test Theme",
      primaryColor: "#3b82f6",
      backgroundColor: "#ffffff",
    },
  },
  zones: {},
};

/**
 * Test CSS extraction functionality
 */
export async function testCSSExtraction(): Promise<{
  success: boolean;
  extractedClasses: string[];
  optimizedCSS: any;
  stats: {
    totalFields: number;
    fieldsWithClasses: number;
    uniqueClasses: number;
    cssSize: number;
  };
  errors: string[];
}> {
  const errors: string[] = [];
  let success = true;

  try {
    console.log('🧪 Testing CSS Extraction...');

    // Step 1: Extract classes
    console.log('📋 Step 1: Extracting Tailwind classes...');
    const extractedClasses = extractTailwindClasses(TEST_PUCK_DATA);
    console.log(`Found ${extractedClasses.length} classes:`, extractedClasses);

    // Step 2: Build optimized CSS
    console.log('🔧 Step 2: Building optimized CSS...');
    const optimizedCSS = await buildOptimizedCSS(TEST_PUCK_DATA);
    console.log('CSS generated:', optimizedCSS.css.length, 'characters');
    console.log('Minified CSS:', optimizedCSS.minified.length, 'characters');
    console.log('Compression ratio:', (optimizedCSS.stats.compressionRatio * 100).toFixed(1) + '%');

    // Step 3: Validate field detection
    console.log('✅ Step 3: Validating field detection...');
    const fieldStats = validateFieldDetection(TEST_PUCK_DATA);
    console.log('Field stats:', fieldStats);

    // Step 4: Test class validation
    console.log('🔍 Step 4: Testing class validation...');
    const classValidation = validateExtractedClasses(extractedClasses);
    console.log('Class validation:', classValidation);

    const stats = {
      totalFields: fieldStats.totalFields,
      fieldsWithClasses: fieldStats.fieldsWithClasses,
      uniqueClasses: optimizedCSS.stats.uniqueClasses,
      cssSize: optimizedCSS.stats.minifiedSize,
    };

    console.log('✅ CSS Extraction test completed successfully!');
    return {
      success: true,
      extractedClasses,
      optimizedCSS,
      stats,
      errors,
    };

  } catch (error) {
    success = false;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    errors.push(errorMessage);
    console.error('❌ CSS Extraction test failed:', errorMessage);

    return {
      success: false,
      extractedClasses: [],
      optimizedCSS: null,
      stats: {
        totalFields: 0,
        fieldsWithClasses: 0,
        uniqueClasses: 0,
        cssSize: 0,
      },
      errors,
    };
  }
}

/**
 * Validate field detection
 */
function validateFieldDetection(data: Data): {
  totalFields: number;
  fieldsWithClasses: number;
  detectedFields: string[];
} {
  let totalFields = 0;
  let fieldsWithClasses = 0;
  const detectedFields: string[] = [];

  function traverse(obj: any) {
    if (!obj || typeof obj !== 'object') return;

    if (Array.isArray(obj)) {
      obj.forEach(traverse);
      return;
    }

    for (const [key, value] of Object.entries(obj)) {
      if (Object.values(TAILWIND_FIELD_NAMES).includes(key as any)) {
        totalFields++;
        if (typeof value === 'string' && value.trim()) {
          fieldsWithClasses++;
          detectedFields.push(key);
        }
      }
      
      if (typeof value === 'object') {
        traverse(value);
      }
    }
  }

  traverse(data);

  return {
    totalFields,
    fieldsWithClasses,
    detectedFields: Array.from(new Set(detectedFields)),
  };
}

/**
 * Validate extracted classes
 */
function validateExtractedClasses(classes: string[]): {
  totalClasses: number;
  validClasses: number;
  invalidClasses: string[];
  duplicates: string[];
} {
  const classCount = new Map<string, number>();
  const invalidClasses: string[] = [];
  let validClasses = 0;

  // Count classes and detect duplicates
  classes.forEach(className => {
    classCount.set(className, (classCount.get(className) || 0) + 1);
    
    // Basic validation - Tailwind classes usually have specific patterns
    if (isValidTailwindClass(className)) {
      validClasses++;
    } else {
      invalidClasses.push(className);
    }
  });

  const duplicates = Array.from(classCount.entries())
    .filter(([, count]) => count > 1)
    .map(([className]) => className);

  return {
    totalClasses: classes.length,
    validClasses,
    invalidClasses,
    duplicates,
  };
}

/**
 * Basic Tailwind class validation
 */
function isValidTailwindClass(className: string): boolean {
  // Basic patterns for common Tailwind classes
  const patterns = [
    /^(p|m|px|py|pt|pr|pb|pl|mx|my|mt|mr|mb|ml)-\d+$/,  // Spacing
    /^(w|h|min-w|min-h|max-w|max-h)-/,  // Sizing
    /^text-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|7xl|8xl|9xl)$/,  // Text sizes
    /^text-\w+(-\d+)?$/,  // Text colors
    /^bg-\w+(-\d+)?$/,  // Background colors
    /^border(-\w+)?(-\d+)?$/,  // Borders
    /^rounded(-\w+)?$/,  // Border radius
    /^shadow(-\w+)?$/,  // Shadows
    /^(flex|grid|block|inline|hidden|relative|absolute|fixed|sticky)$/,  // Layout
    /^(hover|focus|active|visited|disabled|checked|first|last|odd|even):/,  // States
    /^(sm|md|lg|xl|2xl):/,  // Responsive
  ];

  return patterns.some(pattern => pattern.test(className)) ||
         ['container', 'group', 'sr-only', 'not-sr-only', 'space-y-4', 'space-x-4'].includes(className);
}

/**
 * Run CSS extraction test
 */
export async function runCSSExtractionTest(): Promise<void> {
  console.log('🚀 Starting CSS Extraction Test...\n');
  
  const result = await testCSSExtraction();
  
  console.log('\n📊 Test Results:');
  console.log('================');
  console.log(`Success: ${result.success ? '✅' : '❌'}`);
  console.log(`Extracted Classes: ${result.extractedClasses.length}`);
  console.log(`Total Fields: ${result.stats.totalFields}`);
  console.log(`Fields with Classes: ${result.stats.fieldsWithClasses}`);
  console.log(`Unique Classes: ${result.stats.uniqueClasses}`);
  console.log(`CSS Size: ${(result.stats.cssSize / 1024).toFixed(2)} KB`);
  
  if (result.errors.length > 0) {
    console.log('\n❌ Errors:');
    result.errors.forEach(error => console.log(`  - ${error}`));
  }
  
  console.log('\n🎉 Test completed!');
}

export default {
  TEST_PUCK_DATA,
  testCSSExtraction,
  runCSSExtractionTest,
  validateFieldDetection,
  validateExtractedClasses,
  isValidTailwindClass,
};
