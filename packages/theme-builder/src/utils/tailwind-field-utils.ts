/**
 * Tailwind Field Utilities
 * 
 * Provides consistent field definitions for adding Tailwind CSS classes
 * to all components with standardized field names for easy CSS extraction.
 */

import { TailwindClassField } from '../fields/TailwindClassField';

// Standardized field names for CSS extraction
export const TAILWIND_FIELD_NAMES = {
  // Main container styling
  className: 'className',
  
  // Wrapper/container styling  
  containerClassName: 'containerClassName',
  
  // Content area styling
  contentClassName: 'contentClassName',
  
  // Header styling
  headerClassName: 'headerClassName',
  
  // Body/main content styling
  bodyClassName: 'bodyClassName',
  
  // Footer styling
  footerClassName: 'footerClassName',
  
  // Text styling
  textClassName: 'textClassName',
  
  // Button styling
  buttonClassName: 'buttonClassName',
  
  // Input styling
  inputClassName: 'inputClassName',
  
  // Label styling
  labelClassName: 'labelClassName',
  
  // Icon styling
  iconClassName: 'iconClassName',
  
  // Image styling
  imageClassName: 'imageClassName',
  
  // Link styling
  linkClassName: 'linkClassName',
} as const;

export type TailwindFieldName = keyof typeof TAILWIND_FIELD_NAMES;

/**
 * Create a standardized Tailwind CSS field
 */
export function createTailwindField(
  fieldName: TailwindFieldName,
  options: {
    label?: string;
    placeholder?: string;
    description?: string;
  } = {}
) {
  const defaultLabels: Record<TailwindFieldName, string> = {
    className: 'CSS Classes',
    containerClassName: 'Container Classes',
    contentClassName: 'Content Classes', 
    headerClassName: 'Header Classes',
    bodyClassName: 'Body Classes',
    footerClassName: 'Footer Classes',
    textClassName: 'Text Classes',
    buttonClassName: 'Button Classes',
    inputClassName: 'Input Classes',
    labelClassName: 'Label Classes',
    iconClassName: 'Icon Classes',
    imageClassName: 'Image Classes',
    linkClassName: 'Link Classes',
  };

  const defaultPlaceholders: Record<TailwindFieldName, string> = {
    className: 'p-4 bg-white rounded-lg shadow-md...',
    containerClassName: 'max-w-4xl mx-auto px-4...',
    contentClassName: 'space-y-4 text-gray-800...',
    headerClassName: 'text-2xl font-bold text-gray-900...',
    bodyClassName: 'prose prose-lg text-gray-700...',
    footerClassName: 'mt-8 pt-4 border-t border-gray-200...',
    textClassName: 'text-base text-gray-800 leading-relaxed...',
    buttonClassName: 'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600...',
    inputClassName: 'w-full px-3 py-2 border border-gray-300 rounded focus:ring-2...',
    labelClassName: 'block text-sm font-medium text-gray-700 mb-1...',
    iconClassName: 'w-5 h-5 text-gray-500...',
    imageClassName: 'w-full h-auto rounded-lg object-cover...',
    linkClassName: 'text-blue-600 hover:text-blue-800 underline...',
  };

  return {
    type: 'custom' as const,
    label: options.label || defaultLabels[fieldName],
    render: ({ name, onChange, value }: any) => (
      TailwindClassField({
        name,
        value: value || '',
        onChange,
        field: {
          label: options.label || defaultLabels[fieldName],
          placeholder: options.placeholder || defaultPlaceholders[fieldName],
        },
      })
    ),
  };
}

/**
 * Create common Tailwind fields for a component
 */
export function createCommonTailwindFields(
  fields: TailwindFieldName[] = ['className']
) {
  const result: Record<string, any> = {};
  
  fields.forEach(fieldName => {
    result[fieldName] = createTailwindField(fieldName);
  });
  
  return result;
}

/**
 * Create layout-specific Tailwind fields
 */
export function createLayoutTailwindFields() {
  return createCommonTailwindFields([
    'className',
    'containerClassName',
    'contentClassName',
  ]);
}

/**
 * Create form-specific Tailwind fields
 */
export function createFormTailwindFields() {
  return createCommonTailwindFields([
    'className',
    'inputClassName',
    'labelClassName',
  ]);
}

/**
 * Create display-specific Tailwind fields
 */
export function createDisplayTailwindFields() {
  return createCommonTailwindFields([
    'className',
    'containerClassName',
    'textClassName',
  ]);
}

/**
 * Create typography-specific Tailwind fields
 */
export function createTypographyTailwindFields() {
  return createCommonTailwindFields([
    'className',
    'textClassName',
  ]);
}

/**
 * Create template-specific Tailwind fields
 */
export function createTemplateTailwindFields() {
  return createCommonTailwindFields([
    'className',
    'containerClassName',
    'headerClassName',
    'bodyClassName',
    'footerClassName',
  ]);
}

/**
 * Add Tailwind fields to existing component fields
 */
export function addTailwindFields(
  existingFields: Record<string, any>,
  tailwindFields: TailwindFieldName[] = ['className']
): Record<string, any> {
  const tailwindFieldsObj = createCommonTailwindFields(tailwindFields);
  
  return {
    ...existingFields,
    ...tailwindFieldsObj,
  };
}

/**
 * Extract default props for Tailwind fields
 */
export function getTailwindDefaultProps(
  fields: TailwindFieldName[]
): Record<string, string> {
  const result: Record<string, string> = {};
  
  fields.forEach(fieldName => {
    result[fieldName] = '';
  });
  
  return result;
}

/**
 * Apply Tailwind classes to component props
 */
export function applyTailwindClasses(
  props: Record<string, any>,
  element: React.ReactElement,
  fieldName: TailwindFieldName = 'className'
): React.ReactElement {
  const className = props[fieldName];
  
  if (!className) return element;
  
  return React.cloneElement(element, {
    className: `${element.props.className || ''} ${className}`.trim(),
  });
}

export default {
  TAILWIND_FIELD_NAMES,
  createTailwindField,
  createCommonTailwindFields,
  createLayoutTailwindFields,
  createFormTailwindFields,
  createDisplayTailwindFields,
  createTypographyTailwindFields,
  createTemplateTailwindFields,
  addTailwindFields,
  getTailwindDefaultProps,
  applyTailwindClasses,
};
