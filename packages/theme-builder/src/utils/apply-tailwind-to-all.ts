/**
 * Apply Tailwind Fields to All Components
 * 
 * This utility helps apply TailwindClass<PERSON>ield to all components in the config
 * with consistent field names for easy CSS extraction.
 */

import { ComponentConfig } from '@measured/puck';
import { addTailwindFields, getTailwindDefaultProps, TailwindFieldName } from './tailwind-field-utils';

// Component-specific Tailwind field mappings
export const COMPONENT_TAILWIND_FIELDS: Record<string, TailwindFieldName[]> = {
  // Layout Components
  'Grid': ['className', 'containerClassName'],
  'Flex': ['className', 'containerClassName'],
  
  // Display Components
  'Avatar': ['className', 'imageClassName'],
  'Badge': ['className', 'textClassName'],
  'Calendar': ['className', 'containerClassName', 'headerClassName'],
  'ImageViewer': ['className', 'imageClassName'],
  'List': ['className', 'containerClassName', 'textClassName'],
  'Progress': ['className', 'containerClassName'],
  'Spinner': ['className'],
  'Swiper': ['className', 'containerClassName'],
  'Card': ['className', 'containerClassName', 'headerClassName', 'bodyClassName', 'footerClassName'],
  
  // Typography Components
  'Text': ['className', 'textClassName'],
  
  // Form Components
  'Button': ['className', 'buttonClassName'],
  'ButtonDynamic': ['className', 'buttonClassName'],
  'TailwindBox': ['className', 'containerClassName', 'textClassName'],
  'Input': ['className', 'inputClassName', 'labelClassName'],
  'Password': ['className', 'inputClassName', 'labelClassName'],
  'Search': ['className', 'inputClassName', 'labelClassName'],
  'TextArea': ['className', 'inputClassName', 'labelClassName'],
  'OTP': ['className', 'inputClassName', 'labelClassName'],
  'Select': ['className', 'inputClassName', 'labelClassName'],
  'Picker': ['className', 'inputClassName', 'labelClassName'],
  'DatePicker': ['className', 'inputClassName', 'labelClassName'],
  'Switch': ['className', 'labelClassName'],
  'Checkbox': ['className', 'labelClassName'],
  'Radio': ['className', 'labelClassName'],
  'Slider': ['className', 'labelClassName'],
  
  // Overlay Components
  'Modal': ['className', 'containerClassName', 'headerClassName', 'bodyClassName', 'footerClassName'],
  'Sheet': ['className', 'containerClassName', 'headerClassName', 'bodyClassName'],
};

/**
 * Get Tailwind fields for a specific component
 */
export function getTailwindFieldsForComponent(componentName: string): TailwindFieldName[] {
  return COMPONENT_TAILWIND_FIELDS[componentName] || ['className'];
}

/**
 * Apply Tailwind fields to a component configuration
 */
export function applyTailwindToComponent<T extends Record<string, any>>(
  componentConfig: ComponentConfig<T>,
  componentName: string
): ComponentConfig<T & Record<TailwindFieldName, string>> {
  const tailwindFields = getTailwindFieldsForComponent(componentName);
  
  return {
    ...componentConfig,
    fields: addTailwindFields(componentConfig.fields as any, tailwindFields),
    defaultProps: {
      ...componentConfig.defaultProps,
      ...getTailwindDefaultProps(tailwindFields),
    },
  } as ComponentConfig<T & Record<TailwindFieldName, string>>;
}

/**
 * Apply Tailwind fields to multiple components
 */
export function applyTailwindToComponents(
  components: Record<string, ComponentConfig<any>>
): Record<string, ComponentConfig<any>> {
  const result: Record<string, ComponentConfig<any>> = {};
  
  Object.entries(components).forEach(([name, config]) => {
    result[name] = applyTailwindToComponent(config, name);
  });
  
  return result;
}

/**
 * Generate TypeScript types for components with Tailwind fields
 */
export function generateTailwindTypes(componentName: string): string {
  const fields = getTailwindFieldsForComponent(componentName);
  const typeFields = fields.map(field => `  ${field}: string;`).join('\n');
  
  return `
// Tailwind CSS fields for ${componentName}
export interface ${componentName}TailwindProps {
${typeFields}
}

export type Enhanced${componentName}Props = ${componentName}Props & ${componentName}TailwindProps;
`;
}

/**
 * Helper to combine className with Tailwind classes in render functions
 */
export function combineClassNames(
  baseClassName: string,
  tailwindClassName?: string,
  additionalClasses?: string[]
): string {
  const classes = [
    baseClassName,
    tailwindClassName,
    ...(additionalClasses || []),
  ].filter(Boolean);
  
  return classes.join(' ');
}

/**
 * Extract all Tailwind field names from components
 */
export function extractAllTailwindFieldNames(): TailwindFieldName[] {
  const allFields = new Set<TailwindFieldName>();
  
  Object.values(COMPONENT_TAILWIND_FIELDS).forEach(fields => {
    fields.forEach(field => allFields.add(field));
  });
  
  return Array.from(allFields);
}

/**
 * Validate component has required Tailwind fields
 */
export function validateComponentTailwindFields(
  componentName: string,
  componentProps: Record<string, any>
): {
  valid: boolean;
  missing: TailwindFieldName[];
  extra: string[];
} {
  const requiredFields = getTailwindFieldsForComponent(componentName);
  const propKeys = Object.keys(componentProps);
  
  const missing = requiredFields.filter(field => !(field in componentProps));
  const extra = propKeys.filter(key => 
    key.includes('ClassName') && !requiredFields.includes(key as TailwindFieldName)
  );
  
  return {
    valid: missing.length === 0,
    missing,
    extra,
  };
}

/**
 * Generate CSS extraction mapping
 */
export function generateCSSExtractionMapping(): Record<string, TailwindFieldName[]> {
  return COMPONENT_TAILWIND_FIELDS;
}

export default {
  COMPONENT_TAILWIND_FIELDS,
  getTailwindFieldsForComponent,
  applyTailwindToComponent,
  applyTailwindToComponents,
  generateTailwindTypes,
  combineClassNames,
  extractAllTailwindFieldNames,
  validateComponentTailwindFields,
  generateCSSExtractionMapping,
};
