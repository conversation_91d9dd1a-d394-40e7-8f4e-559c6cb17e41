/**
 * Merge Optimized CSS Utility
 * 
 * Utilities to merge optimized CSS into Puck data for render mode
 */

import { Data } from '@measured/puck';
import { OptimizedThemeConfig } from '../hooks/useOptimizedPublish';

/**
 * Merge optimized CSS into Puck data root props
 */
export function mergeOptimizedCSSIntoData(
  data: Data,
  optimizedConfig: OptimizedThemeConfig
): Data {
  return {
    ...data,
    root: {
      ...data.root,
      props: {
        ...data.root.props,
        // Add optimized CSS to root props for render mode
        optimizedCSS: optimizedConfig.minified,
        cssHash: optimizedConfig.hash,
        cssMetadata: optimizedConfig.metadata,
      },
    },
  };
}

/**
 * Extract optimized CSS from Puck data root props
 */
export function extractOptimizedCSSFromData(data: Data): {
  optimizedCSS?: string;
  cssHash?: string;
  cssMetadata?: any;
} {
  const rootProps = data.root?.props || {};
  
  return {
    optimizedCSS: rootProps.optimizedCSS,
    cssHash: rootProps.cssHash,
    cssMetadata: rootProps.cssMetadata,
  };
}

/**
 * Check if Puck data has optimized CSS
 */
export function hasOptimizedCSS(data: Data): boolean {
  const { optimizedCSS } = extractOptimizedCSSFromData(data);
  return Boolean(optimizedCSS && optimizedCSS.trim());
}

/**
 * Create render-ready data with optimized CSS
 */
export function createRenderReadyData(
  editData: Data,
  optimizedConfig: OptimizedThemeConfig
): Data {
  // Merge optimized CSS into data for render mode
  const renderData = mergeOptimizedCSSIntoData(editData, optimizedConfig);
  
  console.log('🎨 Created render-ready data with optimized CSS:', {
    hash: optimizedConfig.hash,
    cssSize: optimizedConfig.metadata.stats.minifiedSize,
    classes: optimizedConfig.metadata.stats.uniqueClasses,
  });
  
  return renderData;
}

/**
 * Separate edit data from render data
 */
export function separateEditAndRenderData(data: Data): {
  editData: Data;
  renderData: Data;
  optimizedCSS?: string;
  cssHash?: string;
  cssMetadata?: any;
} {
  const { optimizedCSS, cssHash, cssMetadata } = extractOptimizedCSSFromData(data);
  
  // Create clean edit data without optimized CSS
  const editData: Data = {
    ...data,
    root: {
      ...data.root,
      props: {
        ...data.root.props,
        // Remove optimized CSS fields for edit mode
        optimizedCSS: undefined,
        cssHash: undefined,
        cssMetadata: undefined,
      },
    },
  };
  
  // Keep render data as is
  const renderData = data;
  
  return {
    editData,
    renderData,
    optimizedCSS,
    cssHash,
    cssMetadata,
  };
}

/**
 * Update optimized CSS in existing data
 */
export function updateOptimizedCSSInData(
  data: Data,
  optimizedCSS: string,
  cssHash: string,
  cssMetadata: any
): Data {
  return {
    ...data,
    root: {
      ...data.root,
      props: {
        ...data.root.props,
        optimizedCSS,
        cssHash,
        cssMetadata,
      },
    },
  };
}

/**
 * Remove optimized CSS from data (for edit mode)
 */
export function removeOptimizedCSSFromData(data: Data): Data {
  const { optimizedCSS, cssHash, cssMetadata, ...cleanProps } = data.root?.props || {};
  
  return {
    ...data,
    root: {
      ...data.root,
      props: cleanProps,
    },
  };
}

/**
 * Validate optimized CSS data
 */
export function validateOptimizedCSSData(data: Data): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const { optimizedCSS, cssHash, cssMetadata } = extractOptimizedCSSFromData(data);
  
  // Check if optimized CSS exists
  if (!optimizedCSS) {
    warnings.push('No optimized CSS found in data');
  } else {
    // Validate CSS content
    if (optimizedCSS.length === 0) {
      errors.push('Optimized CSS is empty');
    }
    
    // Validate hash
    if (!cssHash) {
      warnings.push('No CSS hash found');
    }
    
    // Validate metadata
    if (!cssMetadata) {
      warnings.push('No CSS metadata found');
    } else {
      if (!cssMetadata.stats) {
        warnings.push('No CSS stats in metadata');
      }
      
      if (!cssMetadata.extractedAt) {
        warnings.push('No extraction timestamp in metadata');
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Get CSS stats from data
 */
export function getCSSStatsFromData(data: Data): {
  hasOptimizedCSS: boolean;
  cssSize: number;
  classCount: number;
  compressionRatio: number;
  extractedAt?: string;
} {
  const { optimizedCSS, cssMetadata } = extractOptimizedCSSFromData(data);
  
  if (!optimizedCSS || !cssMetadata?.stats) {
    return {
      hasOptimizedCSS: false,
      cssSize: 0,
      classCount: 0,
      compressionRatio: 0,
    };
  }
  
  return {
    hasOptimizedCSS: true,
    cssSize: cssMetadata.stats.minifiedSize || 0,
    classCount: cssMetadata.stats.uniqueClasses || 0,
    compressionRatio: cssMetadata.stats.compressionRatio || 0,
    extractedAt: cssMetadata.extractedAt,
  };
}

export default {
  mergeOptimizedCSSIntoData,
  extractOptimizedCSSFromData,
  hasOptimizedCSS,
  createRenderReadyData,
  separateEditAndRenderData,
  updateOptimizedCSSInData,
  removeOptimizedCSSFromData,
  validateOptimizedCSSData,
  getCSSStatsFromData,
};
