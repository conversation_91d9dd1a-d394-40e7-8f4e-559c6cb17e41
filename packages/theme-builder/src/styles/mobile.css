/* Mobile-specific styles for Puck Editor */

/* Base mobile layout */
.mobile-layout {
  position: relative;
  overflow: hidden;
}

.desktop-layout {
  /* Keep default desktop styles */
}

/* Mobile-optimized Puck styles */
@media (max-width: 768px) {
  /* Hide default Puck sidebar on mobile */
  [data-puck-sidebar] {
    display: none !important;
  }

  /* Optimize component items for touch */
  [data-puck-component-list] > div {
    padding: 8px !important;
  }

  [data-puck-component-list] button {
    min-height: 48px !important;
    padding: 12px 16px !important;
    font-size: 14px !important;
    border-radius: 8px !important;
    margin-bottom: 8px !important;
    width: 100% !important;
    text-align: left !important;
    border: 1px solid #e5e7eb !important;
    background: white !important;
    transition: all 0.2s ease !important;
    touch-action: manipulation !important;
  }

  [data-puck-component-list] button:hover {
    border-color: #3b82f6 !important;
    background: #f8fafc !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1) !important;
  }

  /* Optimize fields for mobile */
  [data-puck-fields] input,
  [data-puck-fields] select,
  [data-puck-fields] textarea {
    min-height: 44px !important;
    padding: 12px 16px !important;
    font-size: 16px !important; /* Prevent zoom on iOS */
    border-radius: 8px !important;
    border: 1px solid #d1d5db !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  [data-puck-fields] label {
    font-size: 14px !important;
    font-weight: 500 !important;
    margin-bottom: 8px !important;
    display: block !important;
    color: #374151 !important;
  }

  /* Optimize outline for mobile */
  [data-puck-outline] {
    font-size: 14px !important;
  }

  [data-puck-outline] button {
    min-height: 40px !important;
    padding: 8px 12px !important;
    width: 100% !important;
    text-align: left !important;
    border-radius: 6px !important;
    margin-bottom: 4px !important;
  }

  /* Optimize drag handles for touch */
  [data-puck-drag-handle] {
    min-width: 44px !important;
    min-height: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: #f3f4f6 !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    cursor: grab !important;
    touch-action: manipulation !important;
  }

  [data-puck-drag-handle]:active {
    cursor: grabbing !important;
    background: #e5e7eb !important;
  }

  /* Optimize drop zones for touch */
  [data-puck-drop-zone] {
    min-height: 60px !important;
    border: 2px dashed #d1d5db !important;
    border-radius: 8px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 8px 0 !important;
    transition: all 0.2s ease !important;
  }

  [data-puck-drop-zone]:hover,
  [data-puck-drop-zone].drag-over {
    border-color: #3b82f6 !important;
    background: #f0f9ff !important;
  }

  [data-puck-drop-zone]:empty::before {
    content: "Drop component here" !important;
    color: #9ca3af !important;
    font-size: 14px !important;
    font-weight: 500 !important;
  }
}

/* Mobile-optimized Puck container */
@media (max-width: 768px) {
  .theme-builder-container.mobile-layout {
    flex-direction: column;
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
  }

  /* Hide default Puck sidebar on mobile */
  .theme-builder-container.mobile-layout [data-rfd-droppable-id="component-list"] {
    display: none;
  }

  /* Adjust main editor area */
  .theme-builder-container.mobile-layout .flex-1 {
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  /* Mobile header adjustments */
  .theme-builder-container.mobile-layout [data-puck-header] {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 12px 16px;
    min-height: 56px;
  }

  /* Mobile preview area */
  .theme-builder-container.mobile-layout [data-puck-preview] {
    flex: 1;
    overflow: auto;
    background: #f9fafb;
  }

  /* Mobile fields panel */
  .theme-builder-container.mobile-layout [data-puck-fields] {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e5e7eb;
    max-height: 50vh;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(100%);
    transition: transform 0.3s ease;
  }

  .theme-builder-container.mobile-layout [data-puck-fields].open {
    transform: translateY(0);
  }
}

/* Touch-friendly component items */
.mobile-component-item {
  margin-bottom: 8px;
}

.mobile-component-item button,
.mobile-component-item [role="button"] {
  min-height: 48px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  width: 100%;
  text-align: left;
  border: 1px solid #e5e7eb;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  touch-action: manipulation;
}

.mobile-component-item button:hover,
.mobile-component-item [role="button"]:hover {
  border-color: #3b82f6;
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.mobile-component-item button:active,
.mobile-component-item [role="button"]:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(59, 130, 246, 0.1);
}

/* Mobile form fields */
@media (max-width: 768px) {
  .mobile-fields input,
  .mobile-fields select,
  .mobile-fields textarea {
    min-height: 44px;
    padding: 12px 16px;
    font-size: 16px; /* Prevent zoom on iOS */
    border-radius: 8px;
    border: 1px solid #d1d5db;
    width: 100%;
    box-sizing: border-box;
  }

  .mobile-fields label {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
    color: #374151;
  }

  .mobile-fields .field-group {
    margin-bottom: 20px;
  }

  .mobile-fields .field-group:last-child {
    margin-bottom: 0;
  }
}

/* Mobile drag and drop */
@media (max-width: 768px) {
  /* Larger drop zones for touch */
  [data-puck-drop-zone] {
    min-height: 60px;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 8px 0;
    transition: all 0.2s ease;
  }

  [data-puck-drop-zone]:hover,
  [data-puck-drop-zone].drag-over {
    border-color: #3b82f6;
    background: #f0f9ff;
  }

  [data-puck-drop-zone]::before {
    content: "Drop component here";
    color: #9ca3af;
    font-size: 14px;
    font-weight: 500;
  }

  [data-puck-drop-zone]:not(:empty)::before {
    display: none;
  }

  /* Touch-friendly drag handles */
  [data-puck-drag-handle] {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    cursor: grab;
    touch-action: manipulation;
  }

  [data-puck-drag-handle]:active {
    cursor: grabbing;
    background: #e5e7eb;
  }
}

/* Mobile viewport optimizations */
@media (max-width: 768px) {
  /* Prevent horizontal scroll */
  .theme-builder-container.mobile-layout * {
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Optimize text selection */
  .theme-builder-container.mobile-layout {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }

  /* Smooth scrolling */
  .theme-builder-container.mobile-layout * {
    -webkit-overflow-scrolling: touch;
  }

  /* Hide scrollbars on mobile */
  .theme-builder-container.mobile-layout *::-webkit-scrollbar {
    display: none;
  }

  .theme-builder-container.mobile-layout * {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* Mobile action buttons */
.mobile-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 12px 16px;
  display: flex;
  gap: 12px;
  z-index: 1001;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.mobile-action-bar button {
  flex: 1;
  min-height: 44px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #d1d5db;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  touch-action: manipulation;
}

.mobile-action-bar button.primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.mobile-action-bar button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mobile-action-bar button:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* Mobile modal/overlay styles */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding: 0;
}

.mobile-modal {
  background: white;
  border-radius: 16px 16px 0 0;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* Safe area adjustments for mobile */
@supports (padding: max(0px)) {
  .mobile-layout {
    padding-bottom: max(0px, env(safe-area-inset-bottom));
  }

  .mobile-action-bar {
    padding-bottom: max(12px, calc(12px + env(safe-area-inset-bottom)));
  }
}
