import { Config } from '@measured/puck';

import {
  Avatar,
  Badge,
  Button,
  ButtonDynamic,
  CTASection,
  TailwindBox,
  Calendar,
  Card,
  Checkbox,
  ContactSection,
  DatePicker,
  FeaturesSection,
  Flex,
  Grid as GridComponent,
  HeroSection,
  ImageViewer,
  Input,
  List,
  Modal,
  OTP,
  Password,
  Picker,
  ProgramsSection,
  Progress,
  Radio,
  Search,
  Select,
  Sheet,
  Slider,
  Spinner,
  StatsSection,
  Swiper,
  Switch,
  TestimonialsSection,
  Text,
  TextArea,
} from './zmp-blocks';
import { ZMPRoot } from './zmp-root';

// ZMP UI Config following Puck patterns
export const ZMP_PUCK_CONFIG: Config = {
  root: ZMPRoot,
  categories: {
    layout: {
      title: 'Layout',
      components: ['Grid', 'Flex'],
    },
    display: {
      title: 'Display',
      components: [
        'Avatar',
        'Badge',
        'Calendar',
        'ImageViewer',
        'List',
        'Progress',
        'Spinner',
        'Swiper',
        'Card',
      ],
    },
    typography: {
      title: 'Typography',
      components: ['Text'],
    },
    form: {
      title: 'Form',
      components: [
        'Button',
        'ButtonDynamic',
        'Input',
        'Password',
        'Search',
        'TextArea',
        'OTP',
        'Select',
        'Picker',
        'DatePicker',
        'Switch',
        'Checkbox',
        'Radio',
        'Slider',
      ],
    },

    templates: {
      title: 'Templates',
      components: [
        'HeroSection',
        'FeaturesSection',
        'ContactSection',
        'ProgramsSection',
        'TestimonialsSection',
        'StatsSection',
        'CTASection',
      ],
    },
    overlay: {
      title: 'Overlay',
      components: ['Modal', 'Sheet'],
    },
  },
  components: {
    // Display Components
    Avatar,
    Badge,
    Calendar,
    ImageViewer,
    List,
    Progress,
    Spinner,
    Swiper,
    Card,

    // Typography
    Text,

    // Form Components
    Button,
    ButtonDynamic,
    Input,
    Password,
    Search,
    TextArea,
    OTP,
    Select,
    Picker,
    DatePicker,
    Switch,
    Checkbox,
    Radio,
    Slider,

    // Layout Components
    Grid: GridComponent,
    Flex,

    // Section Templates
    HeroSection,
    FeaturesSection,
    ContactSection,
    ProgramsSection,
    TestimonialsSection,
    StatsSection,
    CTASection,

    // Overlay Components
    Modal,
    Sheet,
  },
};


export default ZMP_PUCK_CONFIG;
