import React from 'react';

import { DefaultRootProps, RootConfig } from '@measured/puck';

import {
  BORDER_RADIUS_OPTIONS,
  DEFAULT_COLORS,
  DEFAULT_THEME_NAME,
  DEFAULT_TYPOGRAPHY,
  FONT_FAMILY_OPTIONS,
  SPACING_OPTIONS,
} from '../constants/theme-defaults';
import { ZMPCSSManager } from './zmp-css-manager';

export type ZMPRootProps = DefaultRootProps & {
  title?: string;
  // Colors - Primary
  primaryColor?: string;
  primaryLightColor?: string;
  primaryDarkColor?: string;
  // Colors - Secondary
  secondaryColor?: string;
  secondaryLightColor?: string;
  secondaryDarkColor?: string;
  // Colors - Accent
  accentColor?: string;
  accentLightColor?: string;
  accentDarkColor?: string;
  // Colors - Background
  backgroundColor?: string;
  paperBackgroundColor?: string;
  // Colors - Text
  textColor?: string;
  secondaryTextColor?: string;
  // Typography
  fontFamily?: string;
  headingsFontFamily?: string;
  headingsFontWeight?: string;
  bodyFontFamily?: string;
  bodyFontWeight?: string;
  // Layout
  borderRadius?: string;
  spacing?: string;

  // Optimized CSS for render mode
  optimizedCSS?: string;
  cssHash?: string;
  cssMetadata?: {
    extractedAt: string;
    classes: string[];
    stats: {
      totalClasses: number;
      uniqueClasses: number;
      cssSize: number;
      originalSize: number;
      minifiedSize: number;
      compressionRatio: number;
    };
  };
};

export const ZMPRoot: RootConfig<ZMPRootProps> = {
  defaultProps: {
    title: DEFAULT_THEME_NAME,
    // Colors - Primary
    primaryColor: DEFAULT_COLORS.primary.main,
    primaryLightColor: DEFAULT_COLORS.primary.light,
    primaryDarkColor: DEFAULT_COLORS.primary.dark,
    // Colors - Secondary
    secondaryColor: DEFAULT_COLORS.secondary.main,
    secondaryLightColor: DEFAULT_COLORS.secondary.light,
    secondaryDarkColor: DEFAULT_COLORS.secondary.dark,
    // Colors - Accent
    accentColor: DEFAULT_COLORS.accent.main,
    accentLightColor: DEFAULT_COLORS.accent.light,
    accentDarkColor: DEFAULT_COLORS.accent.dark,
    // Colors - Background
    backgroundColor: DEFAULT_COLORS.background.default,
    paperBackgroundColor: DEFAULT_COLORS.background.paper,
    // Colors - Text
    textColor: DEFAULT_COLORS.text.primary,
    secondaryTextColor: DEFAULT_COLORS.text.secondary,
    // Typography
    fontFamily: DEFAULT_TYPOGRAPHY.fontFamily,
    headingsFontFamily: DEFAULT_TYPOGRAPHY.headings.fontFamily,
    headingsFontWeight: DEFAULT_TYPOGRAPHY.headings.fontWeight,
    bodyFontFamily: DEFAULT_TYPOGRAPHY.body.fontFamily,
    bodyFontWeight: DEFAULT_TYPOGRAPHY.body.fontWeight,
    // Layout
    borderRadius: '8px',
    spacing: '16px',

    // Optimized CSS for render mode (populated during publish)
    optimizedCSS: '',
    cssHash: '',
    cssMetadata: undefined,
  },
  fields: {
    title: {
      type: 'text',
      label: 'App Title',
    },
    // Primary Colors
    primaryColor: {
      type: 'text',
      label: 'Primary Color',
    },
    primaryLightColor: {
      type: 'text',
      label: 'Primary Light Color',
    },
    primaryDarkColor: {
      type: 'text',
      label: 'Primary Dark Color',
    },
    // Secondary Colors
    secondaryColor: {
      type: 'text',
      label: 'Secondary Color',
    },
    secondaryLightColor: {
      type: 'text',
      label: 'Secondary Light Color',
    },
    secondaryDarkColor: {
      type: 'text',
      label: 'Secondary Dark Color',
    },
    // Accent Colors
    accentColor: {
      type: 'text',
      label: 'Accent Color',
    },
    accentLightColor: {
      type: 'text',
      label: 'Accent Light Color',
    },
    accentDarkColor: {
      type: 'text',
      label: 'Accent Dark Color',
    },
    // Background Colors
    backgroundColor: {
      type: 'text',
      label: 'Background Color',
    },
    paperBackgroundColor: {
      type: 'text',
      label: 'Paper Background Color',
    },
    // Text Colors
    textColor: {
      type: 'text',
      label: 'Primary Text Color',
    },
    secondaryTextColor: {
      type: 'text',
      label: 'Secondary Text Color',
    },
    // Typography
    fontFamily: {
      type: 'select',
      label: 'Font Family',
      options: FONT_FAMILY_OPTIONS,
    },
    headingsFontFamily: {
      type: 'select',
      label: 'Headings Font Family',
      options: FONT_FAMILY_OPTIONS,
    },
    headingsFontWeight: {
      type: 'select',
      label: 'Headings Font Weight',
      options: [
        { label: 'Normal', value: '400' },
        { label: 'Medium', value: '500' },
        { label: 'Semi Bold', value: '600' },
        { label: 'Bold', value: '700' },
      ],
    },
    bodyFontFamily: {
      type: 'select',
      label: 'Body Font Family',
      options: FONT_FAMILY_OPTIONS,
    },
    bodyFontWeight: {
      type: 'select',
      label: 'Body Font Weight',
      options: [
        { label: 'Light', value: '300' },
        { label: 'Normal', value: '400' },
        { label: 'Medium', value: '500' },
      ],
    },
    // Layout
    borderRadius: {
      type: 'select',
      label: 'Border Radius',
      options: BORDER_RADIUS_OPTIONS,
    },
    spacing: {
      type: 'select',
      label: 'Base Spacing',
      options: SPACING_OPTIONS,
    },
  },
  render: ({
    puck: { isEditing },
    title,
    primaryColor,
    primaryLightColor,
    primaryDarkColor,
    secondaryColor,
    secondaryLightColor,
    secondaryDarkColor,
    accentColor,
    accentLightColor,
    accentDarkColor,
    backgroundColor,
    paperBackgroundColor,
    textColor,
    secondaryTextColor,
    fontFamily,
    headingsFontFamily,
    headingsFontWeight,
    bodyFontFamily,
    bodyFontWeight,
    borderRadius,
    spacing,
    optimizedCSS,
    cssHash,
    cssMetadata,
    children,
  }) => {
    // CSS Manager handles client-side logic separately
    const cssManagerProps = {
      isEditing,
      optimizedCSS,
      cssHash,
      cssMetadata,
    };
    return (
      <div
        style={
          {
            backgroundColor,
            color: textColor,
            fontFamily,
            // CSS Custom Properties for ZMP components - Full color palette
            '--zmp-primary-color': primaryColor,
            '--zmp-primary-light-color': primaryLightColor,
            '--zmp-primary-dark-color': primaryDarkColor,
            '--zmp-secondary-color': secondaryColor,
            '--zmp-secondary-light-color': secondaryLightColor,
            '--zmp-secondary-dark-color': secondaryDarkColor,
            '--zmp-accent-color': accentColor,
            '--zmp-accent-light-color': accentLightColor,
            '--zmp-accent-dark-color': accentDarkColor,
            '--zmp-background-color': backgroundColor,
            '--zmp-paper-background-color': paperBackgroundColor,
            '--zmp-text-color': textColor,
            '--zmp-secondary-text-color': secondaryTextColor,
            '--zmp-font-family': fontFamily,
            '--zmp-headings-font-family': headingsFontFamily,
            '--zmp-headings-font-weight': headingsFontWeight,
            '--zmp-body-font-family': bodyFontFamily,
            '--zmp-body-font-weight': bodyFontWeight,
            '--zmp-border-radius': borderRadius,
            '--zmp-spacing': spacing,
          } as React.CSSProperties
        }
      >
        log
        {/* CSS Manager for client-side logic */}
        <ZMPCSSManager {...cssManagerProps} />
        {/* Mobile App Header */}
        {/*        {isEditing && (
          <div
            style={{
              padding: spacing,
              backgroundColor: primaryColor,
              color: 'white',
              textAlign: 'center',
              fontSize: '18px',
              fontWeight: 'bold',
            }}
          >
            {title} - Editing Mode
          </div>
        )}*/}
        {/* Main Content Area */}
        <div
          style={{
            flex: 1,
            padding: isEditing ? spacing : '0',
            minHeight: isEditing ? '400px' : 'auto',
          }}
        >
          {children}
        </div>
        {/* Mobile App Footer */}
        {/*     {isEditing && (
          <div
            style={{
              padding: spacing,
              backgroundColor: secondaryColor,
              color: textColor,
              textAlign: 'center',
              fontSize: '12px',
              borderTop: `1px solid ${primaryColor}`,
            }}
          >
            Zalo Mini App Footer
          </div>
        )}*/}
      </div>
    );
  },
};

export default ZMPRoot;
