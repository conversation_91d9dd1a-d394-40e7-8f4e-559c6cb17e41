'use client';

import { useEffect } from 'react';

interface ZMPCSSManagerProps {
  isEditing: boolean;
  optimizedCSS?: string;
  cssHash?: string;
  cssMetadata?: any;
}

/**
 * Client-side CSS Manager for ZMP
 * Handles RunCSS watching in edit mode and optimized CSS injection in render mode
 */
export function ZMPCSSManager({
  isEditing,
  optimizedCSS,
  cssHash,
  cssMetadata,
}: ZMPCSSManagerProps) {
  // Handle CSS based on mode
  useEffect(() => {
    if (isEditing) {
      // EDIT MODE: Initialize RunCSS watching for real-time preview
      import('../services/runcss-service').then(({ getPreviewInstance }) => {
        try {
          // Initialize RunCSS with watching enabled for the entire editor
          getPreviewInstance();
          console.log('🎯 ZMPCSSManager: RunCSS watching initialized for edit mode');
        } catch (error) {
          console.warn('ZMPCSSManager: Failed to initialize RunCSS:', error);
        }
      }).catch(error => {
        console.warn('ZMPCSSManager: Failed to import RunCSS service:', error);
      });
    } else {
      // RENDER MODE: Use optimized CSS if available
      if (optimizedCSS) {
        // Inject optimized CSS for production rendering
        let optimizedStyleElement = document.getElementById('zmp-optimized-css');
        
        if (!optimizedStyleElement) {
          optimizedStyleElement = document.createElement('style');
          optimizedStyleElement.id = 'zmp-optimized-css';
          optimizedStyleElement.setAttribute('data-css-hash', cssHash || 'unknown');
          document.head.appendChild(optimizedStyleElement);
        }
        
        optimizedStyleElement.textContent = optimizedCSS;
        
        console.log('🎨 ZMPCSSManager: Optimized CSS injected for render mode', {
          hash: cssHash,
          size: optimizedCSS.length,
          metadata: cssMetadata,
        });
      }
      
      // Reset RunCSS instance when not in edit mode
      import('../services/runcss-service').then(({ resetPreviewInstance }) => {
        resetPreviewInstance();
        console.log('🛑 ZMPCSSManager: RunCSS watching stopped (render mode)');
      }).catch(error => {
        console.warn('ZMPCSSManager: Failed to reset RunCSS:', error);
      });
    }
  }, [isEditing, optimizedCSS, cssHash, cssMetadata]);

  // Cleanup optimized CSS when component unmounts
  useEffect(() => {
    return () => {
      const optimizedStyleElement = document.getElementById('zmp-optimized-css');
      if (optimizedStyleElement && optimizedStyleElement.parentNode) {
        optimizedStyleElement.parentNode.removeChild(optimizedStyleElement);
        console.log('🧹 ZMPCSSManager: Cleaned up optimized CSS');
      }
    };
  }, []);

  // This component doesn't render anything visible
  return null;
}

export default ZMPCSSManager;
