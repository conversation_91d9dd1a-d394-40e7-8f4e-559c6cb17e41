import React from 'react';
import { ComponentConfig } from '@measured/puck';
import { addTailwindFields, getTailwindDefaultProps } from '../../utils/tailwind-field-utils';

export type InputProps = {
  label?: string;
  placeholder?: string;
  type?: 'text' | 'password' | 'email' | 'number' | 'tel' | 'url';
  value?: string;
  disabled?: boolean;
  required?: boolean;
  helperText?: string;
  errorText?: string;
  status?: 'default' | 'error';
  clearable?: boolean;
  prefix?: string;
  suffix?: string;

  // Tailwind CSS fields
  className: string;
  inputClassName: string;
  labelClassName: string;
};

export const Input: ComponentConfig<InputProps> = {
  label: "Input",
  fields: addTailwindFields({
    label: {
      type: "text",
      label: "Label",
      placeholder: "Enter label...",
    },
    placeholder: {
      type: "text",
      label: "Placeholder",
      placeholder: "Enter placeholder...",
    },
    type: {
      type: "select",
      label: "Input Type",
      options: [
        { label: "Text", value: "text" },
        { label: "Password", value: "password" },
        { label: "Email", value: "email" },
        { label: "Number", value: "number" },
        { label: "Phone", value: "tel" },
        { label: "URL", value: "url" },
      ],
    },
    value: {
      type: "text",
      label: "Default Value",
      placeholder: "Default value...",
    },
    disabled: {
      type: "radio",
      label: "Disabled",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    required: {
      type: "radio",
      label: "Required",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    helperText: {
      type: "text",
      label: "Helper Text",
      placeholder: "Helper text...",
    },
    errorText: {
      type: "text",
      label: "Error Text",
      placeholder: "Error message...",
    },
    status: {
      type: "radio",
      label: "Status",
      options: [
        { label: "Default", value: "default" },
        { label: "Error", value: "error" },
      ],
    },
    clearable: {
      type: "radio",
      label: "Clearable",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    prefix: {
      type: "text",
      label: "Prefix (emoji/icon)",
      placeholder: "🔍",
    },
    suffix: {
      type: "text",
      label: "Suffix (emoji/icon)",
      placeholder: "📧",
    },
  }, ['className', 'inputClassName', 'labelClassName']),
  defaultProps: {
    label: "Input Label",
    placeholder: "Enter text...",
    type: "text",
    value: "",
    disabled: false,
    required: false,
    helperText: "",
    errorText: "",
    status: "default",
    clearable: false,
    prefix: "",
    suffix: "",
    ...getTailwindDefaultProps(['className', 'inputClassName', 'labelClassName']),
  },
  render: ({
    label,
    placeholder,
    type,
    value,
    disabled,
    required,
    helperText,
    errorText,
    status,
    clearable,
    prefix,
    suffix,
    className,
    inputClassName,
    labelClassName,
    puck
  }) => {
    const containerStyles: React.CSSProperties = {
      marginBottom: '16px',
    };

    const labelStyles: React.CSSProperties = {
      display: 'block',
      fontSize: '14px',
      fontWeight: '500',
      color: '#333',
      marginBottom: '8px',
    };

    const inputWrapperStyles: React.CSSProperties = {
      position: 'relative',
      display: 'flex',
      alignItems: 'center',
      backgroundColor: disabled ? '#f5f5f5' : 'white',
      border: `1px solid ${status === 'error' ? '#ff4d4f' : '#d9d9d9'}`,
      borderRadius: '8px',
      padding: '12px',
      transition: 'border-color 0.2s ease',
    };

    const inputStyles: React.CSSProperties = {
      flex: 1,
      border: 'none',
      outline: 'none',
      fontSize: '16px',
      color: disabled ? '#999' : '#333',
      backgroundColor: 'transparent',
      padding: '0',
    };

    const affixStyles: React.CSSProperties = {
      fontSize: '16px',
      color: '#999',
      margin: '0 8px',
    };

    const helperStyles: React.CSSProperties = {
      fontSize: '12px',
      color: status === 'error' ? '#ff4d4f' : '#666',
      marginTop: '4px',
      lineHeight: '1.4',
    };

    const clearButtonStyles: React.CSSProperties = {
      background: 'none',
      border: 'none',
      fontSize: '16px',
      color: '#999',
      cursor: 'pointer',
      padding: '0 4px',
    };

    // Combine built-in styles with Tailwind classes
    const containerClassName = [
      'zmp-component',
      className,
    ].filter(Boolean).join(' ');

    const combinedLabelClassName = [
      labelClassName,
    ].filter(Boolean).join(' ');

    const combinedInputClassName = [
      inputClassName,
    ].filter(Boolean).join(' ');

    return (
      <div className={containerClassName}>
        <div style={containerStyles}>
          {label && (
            <label style={labelStyles} className={combinedLabelClassName}>
              {label}
              {required && <span style={{ color: '#ff4d4f' }}> *</span>}
            </label>
          )}

          <div style={inputWrapperStyles}>
            {prefix && (
              <span style={affixStyles}>
                {prefix}
              </span>
            )}

            <input
              type={type}
              placeholder={placeholder}
              defaultValue={value}
              disabled={disabled}
              required={required}
              style={inputStyles}
              className={combinedInputClassName}
              readOnly={puck.isEditing}
            />
            
            {clearable && value && !disabled && (
              <button
                type="button"
                style={clearButtonStyles}
                onClick={() => console.log('Clear input')}
              >
                ✕
              </button>
            )}
            
            {suffix && (
              <span style={affixStyles}>
                {suffix}
              </span>
            )}
          </div>
          
          {(helperText || errorText) && (
            <div style={helperStyles}>
              {status === 'error' && errorText ? errorText : helperText}
            </div>
          )}
        </div>
      </div>
    );
  },
};

export default Input;
