import React from 'react';
import { ComponentConfig, Slot } from '@measured/puck';
import { addTailwindFields, getTailwindDefaultProps } from '../../utils/tailwind-field-utils';

export type CardProps = {
  title?: string;
  subtitle?: string;
  image?: string;
  imageAlt?: string;
  padding?: string;
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  border?: boolean;
  borderRadius?: string;
  backgroundColor?: string;
  content: Slot;

  // Tailwind CSS fields
  className: string;
  containerClassName: string;
  headerClassName: string;
  bodyClassName: string;
  footerClassName: string;
};

export const Card: ComponentConfig<CardProps> = {
  label: "Card",
  fields: {
    title: {
      type: "text",
      label: "Card Title",
      placeholder: "Card title...",
    },
    subtitle: {
      type: "text",
      label: "Card Subtitle",
      placeholder: "Card subtitle...",
    },
    image: {
      type: "text",
      label: "Image URL",
      placeholder: "https://...",
    },
    imageAlt: {
      type: "text",
      label: "Image Alt Text",
      placeholder: "Image description",
    },
    padding: {
      type: "text",
      label: "Padding",
      placeholder: "16px",
    },
    shadow: {
      type: "select",
      label: "Shadow",
      options: [
        { label: "None", value: "none" },
        { label: "Small", value: "sm" },
        { label: "Medium", value: "md" },
        { label: "Large", value: "lg" },
      ],
    },
    border: {
      type: "radio",
      label: "Show Border",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    borderRadius: {
      type: "text",
      label: "Border Radius",
      placeholder: "8px",
    },
    backgroundColor: {
      type: "text",
      label: "Background Color",
      placeholder: "#ffffff",
    },
    content: {
      type: "slot",
      label: "Card Content",
    },
  },
  defaultProps: {
    title: "Card Title",
    subtitle: "Card subtitle or description",
    image: "",
    imageAlt: "Card image",
    padding: "16px",
    shadow: "md",
    border: true,
    borderRadius: "8px",
    backgroundColor: "#ffffff",
    content: [],
  },
  render: ({
    title,
    subtitle,
    image,
    imageAlt,
    padding,
    shadow,
    border,
    borderRadius,
    backgroundColor,
    content: Content,
    puck
  }) => {
    const getShadowStyles = () => {
      switch (shadow) {
        case 'sm':
          return { boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)' };
        case 'md':
          return { boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' };
        case 'lg':
          return { boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' };
        default:
          return { boxShadow: 'none' };
      }
    };

    const cardStyles: React.CSSProperties = {
      backgroundColor: backgroundColor || 'var(--zmp-background-color, #ffffff)',
      borderRadius: borderRadius || 'var(--zmp-border-radius, 8px)',
      padding: padding || 'var(--zmp-spacing, 16px)',
      border: border ? '1px solid var(--zmp-secondary-color, #f5f5f5)' : 'none',
      ...getShadowStyles(),
      overflow: 'hidden',
      transition: 'all 0.2s ease',
    };

    const imageStyles: React.CSSProperties = {
      width: '100%',
      height: '200px',
      objectFit: 'cover',
      marginBottom: '12px',
      borderRadius: '4px',
    };

    const titleStyles: React.CSSProperties = {
      fontSize: '18px',
      fontWeight: '600',
      color: 'var(--zmp-text-color, #333333)',
      margin: '0 0 8px 0',
      fontFamily: 'inherit',
    };

    const subtitleStyles: React.CSSProperties = {
      fontSize: '14px',
      fontWeight: '400',
      color: 'var(--zmp-text-color, #666666)',
      margin: '0',
      fontFamily: 'inherit',
      lineHeight: '1.5',
    };

    return (
      <div className="zmp-component">
        <div style={cardStyles} className="zmp-card">
          {image && (
            <img
              src={image}
              alt={imageAlt}
              style={imageStyles}
              onError={(e) => {
                // Hide broken images in editing mode
                if (puck.isEditing) {
                  e.currentTarget.style.display = 'none';
                }
              }}
            />
          )}
          
          {title && (
            <h3 style={titleStyles}>
              {title}
            </h3>
          )}
          
          {subtitle && (
            <p style={subtitleStyles}>
              {subtitle}
            </p>
          )}

          {/* Slot for nested content */}
          <div style={{ marginTop: '16px' }}>
            <Content
              minEmptyHeight={40}
              style={{ minHeight: '40px' }}
            />
          </div>
        </div>
      </div>
    );
  },
};

export default Card;
