import React from 'react';
import { ComponentConfig } from '@measured/puck';
import { TailwindClassField } from '../../fields/TailwindClassField';

export type TailwindBoxProps = {
  content: string;
  className: string;
  containerClassName: string;
  textClassName: string;
  showPreview: boolean;
};

export const TailwindBox: ComponentConfig<TailwindBoxProps> = {
  label: 'Tailwind Box',
  category: 'Layout',
  fields: {
    content: {
      type: 'textarea',
      label: 'Content',
      placeholder: 'Enter your content here...',
    },
    
    className: {
      type: 'custom',
      label: 'Container Classes',
      render: ({ name, onChange, value, field }) => (
        <TailwindClassField
          name={name}
          value={value}
          onChange={onChange}
          field={{
            label: 'Container Classes',
            placeholder: 'p-4 bg-blue-500 rounded-lg shadow-md...',
          }}
        />
      ),
    },
    
    containerClassName: {
      type: 'custom',
      label: 'Wrapper Classes',
      render: ({ name, onChange, value, field }) => (
        <TailwindClassField
          name={name}
          value={value}
          onChange={onChange}
          field={{
            label: 'Wrapper Classes',
            placeholder: 'max-w-md mx-auto...',
          }}
        />
      ),
    },
    
    textClassName: {
      type: 'custom',
      label: 'Text Classes',
      render: ({ name, onChange, value, field }) => (
        <TailwindClassField
          name={name}
          value={value}
          onChange={onChange}
          field={{
            label: 'Text Classes',
            placeholder: 'text-white text-lg font-semibold...',
          }}
        />
      ),
    },
    
    showPreview: {
      type: 'radio',
      label: 'Show Live Preview',
      options: [
        { label: 'Yes', value: true },
        { label: 'No', value: false },
      ],
    },
  },

  defaultProps: {
    content: 'This is a Tailwind CSS styled box. You can customize its appearance using the class fields above.',
    className: 'p-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-lg',
    containerClassName: 'max-w-md mx-auto my-4',
    textClassName: 'text-white text-center',
    showPreview: true,
  },

  render: ({ content, className, containerClassName, textClassName, showPreview, puck }) => {
    return (
      <div className="zmp-component tailwind-box-component">
        {/* Main Content */}
        <div className={containerClassName}>
          <div className={className}>
            <div className={textClassName}>
              {content}
            </div>
          </div>
        </div>
        
        {/* Development Preview */}
        {showPreview && puck.isEditing && (
          <div className="mt-4 p-3 bg-gray-100 border rounded text-xs">
            <div className="font-semibold text-gray-700 mb-2">🎨 Applied Classes:</div>
            <div className="space-y-1">
              {containerClassName && (
                <div>
                  <span className="font-medium text-blue-600">Container:</span>
                  <code className="ml-2 px-1 bg-blue-50 text-blue-800 rounded">{containerClassName}</code>
                </div>
              )}
              {className && (
                <div>
                  <span className="font-medium text-purple-600">Box:</span>
                  <code className="ml-2 px-1 bg-purple-50 text-purple-800 rounded">{className}</code>
                </div>
              )}
              {textClassName && (
                <div>
                  <span className="font-medium text-green-600">Text:</span>
                  <code className="ml-2 px-1 bg-green-50 text-green-800 rounded">{textClassName}</code>
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* CSS for better styling in editor */}
        <style jsx>{`
          .tailwind-box-component {
            position: relative;
          }
          
          .tailwind-box-component code {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 11px;
          }
          
          /* Ensure proper spacing in editor */
          .tailwind-box-component > div:first-child {
            min-height: 40px;
          }
          
          /* Highlight on hover in editor */
          .tailwind-box-component:hover {
            outline: 2px dashed rgba(59, 130, 246, 0.3);
            outline-offset: 2px;
          }
        `}</style>
      </div>
    );
  },
};

export default TailwindBox;
