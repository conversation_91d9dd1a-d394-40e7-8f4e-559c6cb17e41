import React from 'react';
import { ComponentConfig } from '@measured/puck';

export type SliderProps = {
  label?: string;
  value?: number;
  defaultValue?: number;
  min?: number;
  max?: number;
  step?: number;
  disabled?: boolean;
  showValue?: boolean;
  marks?: boolean;
  name?: string;
  prefix?: string;
  suffix?: string;
  range?: boolean;
  rangeValue?: [number, number];
  rangeDefaultValue?: [number, number];
  minRange?: number;
};

export const Slider: ComponentConfig<SliderProps> = {
  label: "Slider",
  fields: {
    label: {
      type: "text",
      label: "Label",
      placeholder: "Slider label",
    },
    value: {
      type: "text",
      label: "Value",
      placeholder: "50",
    },
    defaultValue: {
      type: "text",
      label: "Default Value",
      placeholder: "50",
    },
    min: {
      type: "text",
      label: "Min Value",
      placeholder: "0",
    },
    max: {
      type: "text",
      label: "Max Value",
      placeholder: "100",
    },
    step: {
      type: "text",
      label: "Step",
      placeholder: "1",
    },
    disabled: {
      type: "radio",
      label: "Disabled",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    showValue: {
      type: "radio",
      label: "Show Value",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    marks: {
      type: "radio",
      label: "Show Marks",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    name: {
      type: "text",
      label: "Name",
      placeholder: "slider-name",
    },
    prefix: {
      type: "text",
      label: "Prefix",
      placeholder: "🔉",
    },
    suffix: {
      type: "text",
      label: "Suffix",
      placeholder: "🔊",
    },
    range: {
      type: "radio",
      label: "Range Mode",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    rangeValue: {
      type: "text",
      label: "Range Value (comma separated)",
      placeholder: "20,80",
    },
    rangeDefaultValue: {
      type: "text",
      label: "Range Default Value (comma separated)",
      placeholder: "20,80",
    },
    minRange: {
      type: "text",
      label: "Min Range",
      placeholder: "10",
    },
  },
  defaultProps: {
    label: "Slider",
    value: 50,
    defaultValue: 50,
    min: 0,
    max: 100,
    step: 1,
    disabled: false,
    showValue: true,
    marks: false,
    name: "",
    prefix: "",
    suffix: "",
    range: false,
    rangeValue: [20, 80],
    rangeDefaultValue: [20, 80],
    minRange: 10,
  },
  render: ({ 
    label, 
    value, 
    defaultValue, 
    min, 
    max, 
    step, 
    disabled, 
    showValue, 
    marks, 
    name, 
    prefix, 
    suffix, 
    range, 
    rangeValue, 
    rangeDefaultValue, 
    minRange,
    puck 
  }) => {
    const minVal = typeof min === 'number' ? min : parseInt(min?.toString() || '0');
    const maxVal = typeof max === 'number' ? max : parseInt(max?.toString() || '100');
    const stepVal = typeof step === 'number' ? step : parseInt(step?.toString() || '1');
    
    const [currentValue, setCurrentValue] = React.useState(
      range 
        ? (rangeValue || rangeDefaultValue || [20, 80])
        : (value ?? defaultValue ?? 50)
    );

    React.useEffect(() => {
      if (range) {
        setCurrentValue(rangeValue || rangeDefaultValue || [20, 80]);
      } else {
        setCurrentValue(value ?? defaultValue ?? 50);
      }
    }, [value, defaultValue, range, rangeValue, rangeDefaultValue]);

    const containerStyles: React.CSSProperties = {
      marginBottom: '16px',
    };

    const headerStyles: React.CSSProperties = {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '12px',
    };

    const labelStyles: React.CSSProperties = {
      fontSize: '14px',
      fontWeight: '500',
      color: '#333',
    };

    const valueStyles: React.CSSProperties = {
      fontSize: '14px',
      color: '#666',
      fontWeight: '500',
    };

    const sliderContainerStyles: React.CSSProperties = {
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
    };

    const prefixSuffixStyles: React.CSSProperties = {
      fontSize: '16px',
      color: disabled ? '#999' : '#666',
    };

    const trackContainerStyles: React.CSSProperties = {
      flex: 1,
      position: 'relative',
      height: '20px',
      display: 'flex',
      alignItems: 'center',
    };

    const trackStyles: React.CSSProperties = {
      width: '100%',
      height: '4px',
      backgroundColor: '#f0f0f0',
      borderRadius: '2px',
      position: 'relative',
    };

    const getPercentage = (val: number) => {
      return ((val - minVal) / (maxVal - minVal)) * 100;
    };

    const getActiveTrackStyles = () => {
      if (range && Array.isArray(currentValue)) {
        const [start, end] = currentValue;
        return {
          position: 'absolute' as const,
          left: `${getPercentage(start)}%`,
          width: `${getPercentage(end) - getPercentage(start)}%`,
          height: '4px',
          backgroundColor: disabled ? '#ccc' : '#1677ff',
          borderRadius: '2px',
        };
      } else {
        const val = typeof currentValue === 'number' ? currentValue : 50;
        return {
          position: 'absolute' as const,
          left: '0%',
          width: `${getPercentage(val)}%`,
          height: '4px',
          backgroundColor: disabled ? '#ccc' : '#1677ff',
          borderRadius: '2px',
        };
      }
    };

    const thumbStyles: React.CSSProperties = {
      position: 'absolute',
      width: '16px',
      height: '16px',
      backgroundColor: disabled ? '#ccc' : '#1677ff',
      borderRadius: '50%',
      cursor: disabled ? 'not-allowed' : 'pointer',
      transform: 'translateX(-50%)',
      transition: 'all 0.2s ease',
      border: '2px solid white',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
    };

    const renderMarks = () => {
      if (!marks) return null;
      
      const markCount = Math.floor((maxVal - minVal) / stepVal) + 1;
      const markElements = [];
      
      for (let i = 0; i < markCount; i++) {
        const markValue = minVal + (i * stepVal);
        const percentage = getPercentage(markValue);
        
        markElements.push(
          <div
            key={markValue}
            style={{
              position: 'absolute',
              left: `${percentage}%`,
              top: '50%',
              transform: 'translate(-50%, -50%)',
              width: '4px',
              height: '4px',
              backgroundColor: '#d9d9d9',
              borderRadius: '50%',
            }}
          />
        );
      }
      
      return markElements;
    };

    const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (disabled || puck.isEditing) return;
      
      const newValue = parseInt(e.target.value);
      setCurrentValue(newValue);
      console.log('Slider changed:', newValue);
    };

    const formatValue = () => {
      if (range && Array.isArray(currentValue)) {
        return `${currentValue[0]} - ${currentValue[1]}`;
      }
      return currentValue.toString();
    };

    return (
      <div className="zmp-component">
        <div style={containerStyles}>
          {(label || showValue) && (
            <div style={headerStyles}>
              {label && <label style={labelStyles}>{label}</label>}
              {showValue && <span style={valueStyles}>{formatValue()}</span>}
            </div>
          )}
          
          <div style={sliderContainerStyles}>
            {prefix && <span style={prefixSuffixStyles}>{prefix}</span>}
            
            <div style={trackContainerStyles}>
              <div style={trackStyles}>
                <div style={getActiveTrackStyles()} />
                {renderMarks()}
                
                {range && Array.isArray(currentValue) ? (
                  <>
                    <div
                      style={{
                        ...thumbStyles,
                        left: `${getPercentage(currentValue[0])}%`,
                      }}
                    />
                    <div
                      style={{
                        ...thumbStyles,
                        left: `${getPercentage(currentValue[1])}%`,
                      }}
                    />
                  </>
                ) : (
                  <div
                    style={{
                      ...thumbStyles,
                      left: `${getPercentage(typeof currentValue === 'number' ? currentValue : 50)}%`,
                    }}
                  />
                )}
              </div>
              
              {/* Hidden input for form submission */}
              <input
                type="range"
                min={minVal}
                max={maxVal}
                step={stepVal}
                value={typeof currentValue === 'number' ? currentValue : 50}
                name={name}
                disabled={disabled}
                onChange={handleSliderChange}
                style={{
                  position: 'absolute',
                  width: '100%',
                  height: '20px',
                  opacity: 0,
                  cursor: disabled ? 'not-allowed' : 'pointer',
                }}
                readOnly={puck.isEditing}
              />
            </div>
            
            {suffix && <span style={prefixSuffixStyles}>{suffix}</span>}
          </div>
        </div>
      </div>
    );
  },
};

export default Slider;
