import React from 'react';
import { ComponentConfig } from '@measured/puck';

export type ButtonDynamicProps = {
  label: string;
  actionType: 'link' | 'phone' | 'chat' | 'share' | 'custom';
  
  // Link action
  href?: string;
  openInApp?: boolean;
  
  // Phone action
  phoneNumber?: string;
  
  // Chat action
  chatType?: 'user' | 'oa';
  chatId?: string;
  chatMessage?: string;
  
  // Share action
  shareTitle?: string;
  shareDescription?: string;
  shareImageUrl?: string;
  
  // Custom action
  customAction?: string;
  
  // Styling
  variant: 'primary' | 'secondary' | 'ghost' | 'zalo' | 'success' | 'warning' | 'danger';
  size: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  disabled?: boolean;
  icon?: string;
  iconPosition?: 'left' | 'right';
  
  // Animation & Effects
  animationType?: 'none' | 'bounce' | 'pulse' | 'shake';
  showRipple?: boolean;
};

export const ButtonDynamic: ComponentConfig<ButtonDynamicProps> = {
  label: 'Button (Dynamic)',
  category: 'Interactive',
  fields: ({ data }) => {
    const actionType = data?.actionType || 'link';
    
    const baseFields = {
      label: {
        type: 'text' as const,
        label: 'Button Text',
        placeholder: 'Click me...',
      },
      
      actionType: {
        type: 'select' as const,
        label: 'Action Type',
        options: [
          { label: '🔗 Link', value: 'link' },
          { label: '📞 Phone Call', value: 'phone' },
          { label: '💬 Chat', value: 'chat' },
          { label: '📤 Share', value: 'share' },
          { label: '⚙️ Custom', value: 'custom' },
        ],
      },
    };

    const actionFields: any = {};

    // Add fields based on action type
    if (actionType === 'link') {
      actionFields.href = {
        type: 'text' as const,
        label: 'Link URL',
        placeholder: 'https://example.com',
      };
      actionFields.openInApp = {
        type: 'radio' as const,
        label: 'Open in App',
        options: [
          { label: 'Yes', value: true },
          { label: 'No', value: false },
        ],
      };
    }

    if (actionType === 'phone') {
      actionFields.phoneNumber = {
        type: 'text' as const,
        label: 'Phone Number',
        placeholder: '+84 123 456 789',
      };
    }

    if (actionType === 'chat') {
      actionFields.chatType = {
        type: 'radio' as const,
        label: 'Chat Type',
        options: [
          { label: 'User', value: 'user' },
          { label: 'Official Account', value: 'oa' },
        ],
      };
      actionFields.chatId = {
        type: 'text' as const,
        label: 'Chat ID',
        placeholder: 'User ID or OA ID',
      };
      actionFields.chatMessage = {
        type: 'textarea' as const,
        label: 'Pre-filled Message',
        placeholder: 'Xin chào, tôi muốn tư vấn về...',
      };
    }

    if (actionType === 'share') {
      actionFields.shareTitle = {
        type: 'text' as const,
        label: 'Share Title',
        placeholder: 'Check out this amazing content!',
      };
      actionFields.shareDescription = {
        type: 'textarea' as const,
        label: 'Share Description',
        placeholder: 'Description for shared content...',
      };
      actionFields.shareImageUrl = {
        type: 'text' as const,
        label: 'Share Image URL',
        placeholder: 'https://example.com/image.jpg',
      };
    }

    if (actionType === 'custom') {
      actionFields.customAction = {
        type: 'textarea' as const,
        label: 'Custom JavaScript Code',
        placeholder: "console.log('Custom action executed');",
      };
    }

    const stylingFields = {
      variant: {
        type: 'select' as const,
        label: 'Button Style',
        options: [
          { label: '🔵 Primary', value: 'primary' },
          { label: '⚪ Secondary', value: 'secondary' },
          { label: '👻 Ghost', value: 'ghost' },
          { label: '🟦 Zalo Blue', value: 'zalo' },
          { label: '🟢 Success', value: 'success' },
          { label: '🟡 Warning', value: 'warning' },
          { label: '🔴 Danger', value: 'danger' },
        ],
      },
      size: {
        type: 'radio' as const,
        label: 'Button Size',
        options: [
          { label: 'Small', value: 'small' },
          { label: 'Medium', value: 'medium' },
          { label: 'Large', value: 'large' },
        ],
      },
      fullWidth: {
        type: 'radio' as const,
        label: 'Full Width',
        options: [
          { label: 'Yes', value: true },
          { label: 'No', value: false },
        ],
      },
      disabled: {
        type: 'radio' as const,
        label: 'Disabled',
        options: [
          { label: 'Yes', value: true },
          { label: 'No', value: false },
        ],
      },
      icon: {
        type: 'text' as const,
        label: 'Icon (emoji or text)',
        placeholder: '🚀',
      },
      iconPosition: {
        type: 'radio' as const,
        label: 'Icon Position',
        options: [
          { label: 'Left', value: 'left' },
          { label: 'Right', value: 'right' },
        ],
      },
      animationType: {
        type: 'select' as const,
        label: 'Animation',
        options: [
          { label: 'None', value: 'none' },
          { label: 'Bounce', value: 'bounce' },
          { label: 'Pulse', value: 'pulse' },
          { label: 'Shake', value: 'shake' },
        ],
      },
      showRipple: {
        type: 'radio' as const,
        label: 'Ripple Effect',
        options: [
          { label: 'Yes', value: true },
          { label: 'No', value: false },
        ],
      },
    };

    return {
      ...baseFields,
      ...actionFields,
      ...stylingFields,
    };
  },

  defaultProps: {
    label: 'Button',
    actionType: 'link',
    href: '#',
    openInApp: false,
    phoneNumber: '',
    chatType: 'oa',
    chatId: '',
    chatMessage: '',
    shareTitle: '',
    shareDescription: '',
    shareImageUrl: '',
    customAction: '',
    variant: 'primary',
    size: 'medium',
    fullWidth: false,
    disabled: false,
    icon: '',
    iconPosition: 'left',
    animationType: 'none',
    showRipple: true,
  },

  render: (props) => {
    const { 
      label, 
      actionType,
      variant, 
      size, 
      fullWidth, 
      disabled, 
      icon, 
      iconPosition,
      animationType,
      puck 
    } = props;

    const getSizeStyles = () => {
      switch (size) {
        case 'small':
          return { padding: '8px 16px', fontSize: '14px' };
        case 'large':
          return { padding: '16px 32px', fontSize: '18px' };
        default:
          return { padding: '12px 24px', fontSize: '16px' };
      }
    };

    const getVariantStyles = () => {
      switch (variant) {
        case 'secondary':
          return {
            backgroundColor: 'var(--zmp-secondary-color, #f5f5f5)',
            color: 'var(--zmp-text-color, #333)',
            border: '1px solid var(--zmp-primary-color, #1677ff)',
          };
        case 'ghost':
          return {
            backgroundColor: 'transparent',
            color: 'var(--zmp-primary-color, #1677ff)',
            border: '1px solid var(--zmp-primary-color, #1677ff)',
          };
        case 'zalo':
          return {
            backgroundColor: '#0068ff',
            color: 'white',
            border: 'none',
          };
        case 'success':
          return {
            backgroundColor: '#52c41a',
            color: 'white',
            border: 'none',
          };
        case 'warning':
          return {
            backgroundColor: '#faad14',
            color: 'white',
            border: 'none',
          };
        case 'danger':
          return {
            backgroundColor: '#ff4d4f',
            color: 'white',
            border: 'none',
          };
        default:
          return {
            backgroundColor: 'var(--zmp-primary-color, #1677ff)',
            color: 'white',
            border: 'none',
          };
      }
    };

    const getAnimationStyles = () => {
      switch (animationType) {
        case 'bounce':
          return { animation: 'zmp-bounce 2s infinite' };
        case 'pulse':
          return { animation: 'zmp-pulse 2s infinite' };
        case 'shake':
          return { animation: 'zmp-shake 0.5s ease-in-out infinite alternate' };
        default:
          return {};
      }
    };

    const buttonStyles: React.CSSProperties = {
      ...getSizeStyles(),
      ...getVariantStyles(),
      ...getAnimationStyles(),
      borderRadius: 'var(--zmp-border-radius, 8px)',
      fontFamily: 'inherit',
      cursor: disabled ? 'not-allowed' : 'pointer',
      opacity: disabled ? 0.6 : 1,
      width: fullWidth ? '100%' : 'auto',
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: icon ? '8px' : '0',
      textDecoration: 'none',
      transition: 'all 0.2s ease',
      fontWeight: '500',
      position: 'relative',
      overflow: 'hidden',
    };

    const handleClick = (e: React.MouseEvent) => {
      if (puck.isEditing) {
        e.preventDefault();
        return;
      }
      
      console.log(`Button clicked: ${actionType}`, props);
    };

    return (
      <div className="zmp-component">
        <button
          style={buttonStyles}
          onClick={handleClick}
          disabled={disabled}
          type="button"
          className="zmp-button-dynamic"
        >
          {icon && iconPosition === 'left' && <span>{icon}</span>}
          <span>{label}</span>
          {icon && iconPosition === 'right' && <span>{icon}</span>}
        </button>
        
        <style jsx>{`
          @keyframes zmp-bounce {
            0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
            40%, 43% { transform: translate3d(0, -8px, 0); }
            70% { transform: translate3d(0, -4px, 0); }
            90% { transform: translate3d(0, -2px, 0); }
          }
          
          @keyframes zmp-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
          }
          
          @keyframes zmp-shake {
            0% { transform: translateX(0); }
            25% { transform: translateX(-2px); }
            50% { transform: translateX(2px); }
            75% { transform: translateX(-1px); }
            100% { transform: translateX(0); }
          }
          
          .zmp-button-dynamic:hover:not(:disabled) {
            opacity: 0.9;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
          
          .zmp-button-dynamic:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
          }
        `}</style>
      </div>
    );
  },
};

export default ButtonDynamic;
