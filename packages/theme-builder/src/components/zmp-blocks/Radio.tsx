import React from 'react';
import { ComponentConfig } from '@measured/puck';

export type RadioProps = {
  label?: string;
  checked?: boolean;
  defaultChecked?: boolean;
  disabled?: boolean;
  size?: 'small' | 'medium';
  value?: string;
  name?: string;
  required?: boolean;
  helperText?: string;
  errorText?: string;
  status?: 'default' | 'error';
  options?: Array<{
    label: string;
    value: string;
    disabled?: boolean;
  }>;
  groupValue?: string;
  groupDefaultValue?: string;
};

export const Radio: ComponentConfig<RadioProps> = {
  label: "Radio",
  fields: {
    label: {
      type: "text",
      label: "Label",
      placeholder: "Radio option",
    },
    checked: {
      type: "radio",
      label: "Checked",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    defaultChecked: {
      type: "radio",
      label: "Default Checked",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    disabled: {
      type: "radio",
      label: "Disabled",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    size: {
      type: "select",
      label: "Size",
      options: [
        { label: "Small", value: "small" },
        { label: "Medium", value: "medium" },
      ],
    },
    value: {
      type: "text",
      label: "Value",
      placeholder: "radio-value",
    },
    name: {
      type: "text",
      label: "Name",
      placeholder: "radio-group",
    },
    required: {
      type: "radio",
      label: "Required",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    helperText: {
      type: "text",
      label: "Helper Text",
      placeholder: "Helper text...",
    },
    errorText: {
      type: "text",
      label: "Error Text",
      placeholder: "Error message...",
    },
    status: {
      type: "radio",
      label: "Status",
      options: [
        { label: "Default", value: "default" },
        { label: "Error", value: "error" },
      ],
    },
    options: {
      type: "array",
      label: "Radio Options (Group Mode)",
      arrayFields: {
        label: { type: "text", label: "Label" },
        value: { type: "text", label: "Value" },
        disabled: { 
          type: "radio", 
          label: "Disabled",
          options: [
            { label: "Yes", value: true },
            { label: "No", value: false },
          ],
        },
      },
      defaultItemProps: {
        label: "Option 1",
        value: "option1",
        disabled: false,
      },
    },
    groupValue: {
      type: "text",
      label: "Group Selected Value",
      placeholder: "option1",
    },
    groupDefaultValue: {
      type: "text",
      label: "Group Default Value",
      placeholder: "option1",
    },
  },
  defaultProps: {
    label: "Radio option",
    checked: false,
    defaultChecked: false,
    disabled: false,
    size: "medium",
    value: "",
    name: "",
    required: false,
    helperText: "",
    errorText: "",
    status: "default",
    options: [
      { label: "Option 1", value: "option1", disabled: false },
      { label: "Option 2", value: "option2", disabled: false },
      { label: "Option 3", value: "option3", disabled: false },
    ],
    groupValue: "",
    groupDefaultValue: "",
  },
  render: ({ 
    label, 
    checked, 
    defaultChecked, 
    disabled, 
    size, 
    value, 
    name, 
    required, 
    helperText, 
    errorText, 
    status,
    options,
    groupValue,
    groupDefaultValue,
    puck 
  }) => {
    const [selectedValue, setSelectedValue] = React.useState(
      groupValue || groupDefaultValue || (checked || defaultChecked ? value : '')
    );

    React.useEffect(() => {
      if (groupValue !== undefined) {
        setSelectedValue(groupValue);
      }
    }, [groupValue]);

    const getSizeStyles = () => {
      switch (size) {
        case 'small':
          return { width: '16px', height: '16px' };
        default:
          return { width: '20px', height: '20px' };
      }
    };

    const containerStyles: React.CSSProperties = {
      marginBottom: '16px',
    };

    const radioContainerStyles: React.CSSProperties = {
      display: 'flex',
      alignItems: 'flex-start',
      gap: '8px',
      cursor: disabled ? 'not-allowed' : 'pointer',
      opacity: disabled ? 0.6 : 1,
      marginBottom: '8px',
    };

    const radioStyles: React.CSSProperties = {
      ...getSizeStyles(),
      border: `2px solid ${status === 'error' ? '#ff4d4f' : '#d9d9d9'}`,
      borderRadius: '50%',
      backgroundColor: 'white',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      transition: 'all 0.2s ease',
      flexShrink: 0,
      marginTop: '2px',
    };

    const selectedRadioStyles: React.CSSProperties = {
      ...radioStyles,
      borderColor: '#1677ff',
    };

    const labelStyles: React.CSSProperties = {
      fontSize: size === 'small' ? '14px' : '16px',
      color: disabled ? '#999' : '#333',
      lineHeight: '1.5',
      userSelect: 'none',
    };

    const helperStyles: React.CSSProperties = {
      fontSize: '12px',
      color: status === 'error' ? '#ff4d4f' : '#666',
      marginTop: '4px',
      lineHeight: '1.4',
    };

    const getRadioIcon = (isSelected: boolean) => {
      if (isSelected) {
        return (
          <div
            style={{
              width: size === 'small' ? '8px' : '10px',
              height: size === 'small' ? '8px' : '10px',
              backgroundColor: '#1677ff',
              borderRadius: '50%',
            }}
          />
        );
      }
      return null;
    };

    const handleChange = (optionValue: string) => {
      if (disabled || puck.isEditing) return;
      
      setSelectedValue(optionValue);
      console.log('Radio changed:', { name, value: optionValue });
    };

    // If options are provided, render as radio group
    if (options && options.length > 0) {
      return (
        <div className="zmp-component">
          <div style={containerStyles}>
            {options.map((option, index) => {
              const isSelected = selectedValue === option.value;
              const isDisabled = disabled || option.disabled;
              
              return (
                <div 
                  key={option.value || index}
                  style={{
                    ...radioContainerStyles,
                    cursor: isDisabled ? 'not-allowed' : 'pointer',
                    opacity: isDisabled ? 0.6 : 1,
                  }}
                  onClick={() => !isDisabled && handleChange(option.value)}
                >
                  <div style={isSelected ? selectedRadioStyles : radioStyles}>
                    {getRadioIcon(isSelected)}
                  </div>
                  
                  <label style={labelStyles}>
                    {option.label}
                    {required && index === 0 && <span style={{ color: '#ff4d4f' }}> *</span>}
                  </label>
                  
                  {/* Hidden input for form submission */}
                  <input
                    type="radio"
                    checked={isSelected}
                    value={option.value}
                    name={name}
                    required={required && index === 0}
                    disabled={isDisabled}
                    onChange={() => handleChange(option.value)}
                    style={{ display: 'none' }}
                    readOnly={puck.isEditing}
                  />
                </div>
              );
            })}
            
            {(helperText || errorText) && (
              <div style={helperStyles}>
                {status === 'error' && errorText ? errorText : helperText}
              </div>
            )}
          </div>
        </div>
      );
    }

    // Single radio mode
    const isSelected = checked || (value && selectedValue === value);
    
    return (
      <div className="zmp-component">
        <div style={containerStyles}>
          <div 
            style={radioContainerStyles}
            onClick={() => handleChange(value || '')}
          >
            <div style={isSelected ? selectedRadioStyles : radioStyles}>
              {getRadioIcon(isSelected)}
            </div>
            
            {label && (
              <label style={labelStyles}>
                {label}
                {required && <span style={{ color: '#ff4d4f' }}> *</span>}
              </label>
            )}
          </div>
          
          {(helperText || errorText) && (
            <div style={helperStyles}>
              {status === 'error' && errorText ? errorText : helperText}
            </div>
          )}
          
          {/* Hidden input for form submission */}
          <input
            type="radio"
            checked={isSelected}
            value={value}
            name={name}
            required={required}
            disabled={disabled}
            onChange={() => handleChange(value || '')}
            style={{ display: 'none' }}
            readOnly={puck.isEditing}
          />
        </div>
      </div>
    );
  },
};

export default Radio;
