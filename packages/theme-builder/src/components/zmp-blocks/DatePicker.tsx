import React from 'react';
import { ComponentConfig } from '@measured/puck';

export type DatePickerProps = {
  label?: string;
  placeholder?: string;
  title?: string;
  disabled?: boolean;
  required?: boolean;
  helperText?: string;
  errorText?: string;
  status?: 'default' | 'error' | 'success';
  mask?: boolean;
  maskClosable?: boolean;
  defaultOpen?: boolean;
  defaultValue?: string;
  value?: string;
  dateFormat?: string;
  columnsFormat?: 'MM-DD-YYYY' | 'DD-MM-YYYY' | 'YYYY-MM-DD';
  startDate?: string;
  endDate?: string;
  startYear?: number;
  endYear?: number;
  locale?: string;
};

export const DatePicker: ComponentConfig<DatePickerProps> = {
  label: "DatePicker",
  fields: {
    label: {
      type: "text",
      label: "Label",
      placeholder: "Select date",
    },
    placeholder: {
      type: "text",
      label: "Placeholder",
      placeholder: "Choose date...",
    },
    title: {
      type: "text",
      label: "Picker Title",
      placeholder: "Select Date",
    },
    disabled: {
      type: "radio",
      label: "Disabled",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    required: {
      type: "radio",
      label: "Required",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    helperText: {
      type: "text",
      label: "Helper Text",
      placeholder: "Helper text...",
    },
    errorText: {
      type: "text",
      label: "Error Text",
      placeholder: "Error message...",
    },
    status: {
      type: "select",
      label: "Status",
      options: [
        { label: "Default", value: "default" },
        { label: "Error", value: "error" },
        { label: "Success", value: "success" },
      ],
    },
    mask: {
      type: "radio",
      label: "Show Mask",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    maskClosable: {
      type: "radio",
      label: "Mask Closable",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    defaultOpen: {
      type: "radio",
      label: "Default Open",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    defaultValue: {
      type: "text",
      label: "Default Value",
      placeholder: "2024-01-01",
    },
    value: {
      type: "text",
      label: "Current Value",
      placeholder: "2024-01-01",
    },
    dateFormat: {
      type: "text",
      label: "Date Format",
      placeholder: "dd/mm/yyyy",
    },
    columnsFormat: {
      type: "select",
      label: "Columns Format",
      options: [
        { label: "MM-DD-YYYY", value: "MM-DD-YYYY" },
        { label: "DD-MM-YYYY", value: "DD-MM-YYYY" },
        { label: "YYYY-MM-DD", value: "YYYY-MM-DD" },
      ],
    },
    startDate: {
      type: "text",
      label: "Start Date",
      placeholder: "2020-01-01",
    },
    endDate: {
      type: "text",
      label: "End Date",
      placeholder: "2030-12-31",
    },
    startYear: {
      type: "text",
      label: "Start Year",
      placeholder: "2020",
    },
    endYear: {
      type: "text",
      label: "End Year",
      placeholder: "2030",
    },
    locale: {
      type: "text",
      label: "Locale",
      placeholder: "vi-VN",
    },
  },
  defaultProps: {
    label: "Date Picker",
    placeholder: "Choose date...",
    title: "Select Date",
    disabled: false,
    required: false,
    helperText: "",
    errorText: "",
    status: "default",
    mask: true,
    maskClosable: true,
    defaultOpen: false,
    defaultValue: "",
    value: "",
    dateFormat: "dd/mm/yyyy",
    columnsFormat: "DD-MM-YYYY",
    startDate: "",
    endDate: "",
    startYear: 2020,
    endYear: 2030,
    locale: "vi-VN",
  },
  render: ({ 
    label, 
    placeholder, 
    title, 
    disabled, 
    required, 
    helperText, 
    errorText, 
    status, 
    mask, 
    maskClosable, 
    defaultOpen, 
    defaultValue, 
    value, 
    dateFormat, 
    columnsFormat, 
    startDate, 
    endDate, 
    startYear, 
    endYear, 
    locale,
    puck 
  }) => {
    const [isOpen, setIsOpen] = React.useState(defaultOpen || false);
    const [selectedDate, setSelectedDate] = React.useState<Date | null>(
      value ? new Date(value) : defaultValue ? new Date(defaultValue) : null
    );

    React.useEffect(() => {
      if (value) {
        setSelectedDate(new Date(value));
      } else if (defaultValue) {
        setSelectedDate(new Date(defaultValue));
      }
    }, [value, defaultValue]);

    const containerStyles: React.CSSProperties = {
      marginBottom: '16px',
      position: 'relative',
    };

    const labelStyles: React.CSSProperties = {
      display: 'block',
      fontSize: '14px',
      fontWeight: '500',
      color: '#333',
      marginBottom: '8px',
    };

    const inputStyles: React.CSSProperties = {
      width: '100%',
      padding: '12px',
      border: `1px solid ${
        status === 'error' ? '#ff4d4f' : 
        status === 'success' ? '#52c41a' : '#d9d9d9'
      }`,
      borderRadius: '8px',
      outline: 'none',
      fontSize: '16px',
      color: disabled ? '#999' : '#333',
      backgroundColor: disabled ? '#f5f5f5' : 'white',
      cursor: disabled ? 'not-allowed' : 'pointer',
      transition: 'border-color 0.2s ease',
    };

    const helperStyles: React.CSSProperties = {
      fontSize: '12px',
      color: status === 'error' ? '#ff4d4f' : status === 'success' ? '#52c41a' : '#666',
      marginTop: '4px',
      lineHeight: '1.4',
    };

    const formatDate = (date: Date | null) => {
      if (!date) return '';
      
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear().toString();
      
      if (dateFormat) {
        return dateFormat
          .replace('dd', day)
          .replace('mm', month)
          .replace('yyyy', year)
          .replace('yy', year.slice(-2));
      }
      
      switch (columnsFormat) {
        case 'MM-DD-YYYY':
          return `${month}/${day}/${year}`;
        case 'YYYY-MM-DD':
          return `${year}-${month}-${day}`;
        default:
          return `${day}/${month}/${year}`;
      }
    };

    const handleInputClick = () => {
      if (!disabled && !puck.isEditing) {
        setIsOpen(true);
      }
    };

    const handleDateSelect = (date: Date) => {
      setSelectedDate(date);
      setIsOpen(false);
      console.log('Date selected:', date);
    };

    const handleCancel = () => {
      setIsOpen(false);
    };

    // Simple date picker implementation
    const renderDatePicker = () => {
      const currentDate = selectedDate || new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth();
      
      const years = Array.from(
        { length: (endYear || 2030) - (startYear || 2020) + 1 }, 
        (_, i) => (startYear || 2020) + i
      );
      
      const months = Array.from({ length: 12 }, (_, i) => i);
      const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
      const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);

      const maskStyles: React.CSSProperties = {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'flex-end',
        justifyContent: 'center',
        zIndex: 1000,
      };

      const pickerStyles: React.CSSProperties = {
        backgroundColor: 'white',
        borderTopLeftRadius: '16px',
        borderTopRightRadius: '16px',
        width: '100%',
        maxWidth: '400px',
        maxHeight: '60vh',
        overflow: 'hidden',
        boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',
      };

      const headerStyles: React.CSSProperties = {
        padding: '16px 20px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      };

      const columnsContainerStyles: React.CSSProperties = {
        display: 'flex',
        height: '200px',
        overflow: 'hidden',
      };

      const columnStyles: React.CSSProperties = {
        flex: 1,
        overflowY: 'auto',
        borderRight: '1px solid #f0f0f0',
      };

      const optionStyles: React.CSSProperties = {
        padding: '12px 16px',
        fontSize: '16px',
        color: '#333',
        cursor: 'pointer',
        textAlign: 'center',
        transition: 'background-color 0.2s ease',
      };

      const selectedOptionStyles: React.CSSProperties = {
        ...optionStyles,
        backgroundColor: '#e6f7ff',
        color: '#1677ff',
        fontWeight: '500',
      };

      return (
        <div 
          style={maskStyles}
          onClick={maskClosable ? handleCancel : undefined}
        >
          <div 
            style={pickerStyles}
            onClick={(e) => e.stopPropagation()}
          >
            <div style={headerStyles}>
              <h3 style={{ margin: 0, fontSize: '18px', fontWeight: '600' }}>{title}</h3>
              <button
                style={{ background: 'none', border: 'none', fontSize: '18px', cursor: 'pointer' }}
                onClick={handleCancel}
              >
                ✕
              </button>
            </div>
            
            <div style={columnsContainerStyles}>
              {/* Day Column */}
              <div style={columnStyles}>
                {days.map((day) => (
                  <div
                    key={day}
                    style={day === currentDate.getDate() ? selectedOptionStyles : optionStyles}
                    onClick={() => {
                      const newDate = new Date(currentYear, currentMonth, day);
                      handleDateSelect(newDate);
                    }}
                  >
                    {day}
                  </div>
                ))}
              </div>
              
              {/* Month Column */}
              <div style={columnStyles}>
                {months.map((month) => (
                  <div
                    key={month}
                    style={month === currentMonth ? selectedOptionStyles : optionStyles}
                    onClick={() => {
                      const newDate = new Date(currentYear, month, Math.min(currentDate.getDate(), new Date(currentYear, month + 1, 0).getDate()));
                      setSelectedDate(newDate);
                    }}
                  >
                    {new Date(2024, month).toLocaleDateString(locale || 'vi-VN', { month: 'long' })}
                  </div>
                ))}
              </div>
              
              {/* Year Column */}
              <div style={{ ...columnStyles, borderRight: 'none' }}>
                {years.map((year) => (
                  <div
                    key={year}
                    style={year === currentYear ? selectedOptionStyles : optionStyles}
                    onClick={() => {
                      const newDate = new Date(year, currentMonth, Math.min(currentDate.getDate(), new Date(year, currentMonth + 1, 0).getDate()));
                      setSelectedDate(newDate);
                    }}
                  >
                    {year}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      );
    };

    return (
      <div className="zmp-component">
        <div style={containerStyles}>
          {label && (
            <label style={labelStyles}>
              {label}
              {required && <span style={{ color: '#ff4d4f' }}> *</span>}
            </label>
          )}
          
          <input
            type="text"
            placeholder={placeholder}
            value={formatDate(selectedDate)}
            disabled={disabled}
            required={required}
            style={inputStyles}
            onClick={handleInputClick}
            readOnly
            onFocus={(e) => {
              if (!puck.isEditing) {
                e.target.style.borderColor = '#1677ff';
              }
            }}
            onBlur={(e) => {
              if (!puck.isEditing) {
                e.target.style.borderColor = 
                  status === 'error' ? '#ff4d4f' : 
                  status === 'success' ? '#52c41a' : '#d9d9d9';
              }
            }}
          />
          
          {(helperText || errorText) && (
            <div style={helperStyles}>
              {status === 'error' && errorText ? errorText : helperText}
            </div>
          )}
        </div>

        {/* Date Picker Modal */}
        {isOpen && renderDatePicker()}
      </div>
    );
  },
};

export default DatePicker;
