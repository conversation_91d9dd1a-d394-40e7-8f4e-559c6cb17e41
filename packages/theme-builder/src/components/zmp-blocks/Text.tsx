import React from 'react';
import { ComponentConfig } from '@measured/puck';
import { addTailwindFields, getTailwindDefaultProps } from '../../utils/tailwind-field-utils';

export type TextProps = {
  text: string;
  tag: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span';
  size: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl';
  weight: 'normal' | 'medium' | 'semibold' | 'bold';
  align: 'left' | 'center' | 'right';
  color?: string;
  margin?: string;

  // Tailwind CSS fields
  className: string;
  textClassName: string;
};

export const Text: ComponentConfig<TextProps> = {
  label: "Text",
  fields: addTailwindFields({
    text: {
      type: "textarea",
      label: "Text Content",
      placeholder: "Enter your text here...",
    },
    tag: {
      type: "select",
      label: "HTML Tag",
      options: [
        { label: "Heading 1", value: "h1" },
        { label: "Heading 2", value: "h2" },
        { label: "Heading 3", value: "h3" },
        { label: "Heading 4", value: "h4" },
        { label: "Heading 5", value: "h5" },
        { label: "Heading 6", value: "h6" },
        { label: "Paragraph", value: "p" },
        { label: "Span", value: "span" },
      ],
    },
    size: {
      type: "select",
      label: "Text Size",
      options: [
        { label: "Extra Small", value: "xs" },
        { label: "Small", value: "sm" },
        { label: "Base", value: "base" },
        { label: "Large", value: "lg" },
        { label: "Extra Large", value: "xl" },
        { label: "2X Large", value: "2xl" },
        { label: "3X Large", value: "3xl" },
      ],
    },
    weight: {
      type: "select",
      label: "Font Weight",
      options: [
        { label: "Normal", value: "normal" },
        { label: "Medium", value: "medium" },
        { label: "Semi Bold", value: "semibold" },
        { label: "Bold", value: "bold" },
      ],
    },
    align: {
      type: "radio",
      label: "Text Alignment",
      options: [
        { label: "Left", value: "left" },
        { label: "Center", value: "center" },
        { label: "Right", value: "right" },
      ],
    },
    color: {
      type: "text",
      label: "Text Color",
      placeholder: "#333333",
    },
    margin: {
      type: "text",
      label: "Margin",
      placeholder: "16px 0",
    },
  }, ['className', 'textClassName']),
  defaultProps: {
    text: "Your text here",
    tag: "p",
    size: "base",
    weight: "normal",
    align: "left",
    color: "",
    margin: "16px 0",
    ...getTailwindDefaultProps(['className', 'textClassName']),
  },
  render: ({ text, tag, size, weight, align, color, margin, className, textClassName }) => {
    const getSizeStyles = () => {
      switch (size) {
        case 'xs':
          return { fontSize: '12px', lineHeight: '16px' };
        case 'sm':
          return { fontSize: '14px', lineHeight: '20px' };
        case 'lg':
          return { fontSize: '18px', lineHeight: '28px' };
        case 'xl':
          return { fontSize: '20px', lineHeight: '28px' };
        case '2xl':
          return { fontSize: '24px', lineHeight: '32px' };
        case '3xl':
          return { fontSize: '30px', lineHeight: '36px' };
        default:
          return { fontSize: '16px', lineHeight: '24px' };
      }
    };

    const getWeightStyles = () => {
      switch (weight) {
        case 'medium':
          return { fontWeight: '500' };
        case 'semibold':
          return { fontWeight: '600' };
        case 'bold':
          return { fontWeight: '700' };
        default:
          return { fontWeight: '400' };
      }
    };

    const textStyles: React.CSSProperties = {
      ...getSizeStyles(),
      ...getWeightStyles(),
      textAlign: align,
      color: color || 'var(--zmp-text-color, #333333)',
      margin: margin || '16px 0',
      fontFamily: 'inherit',
      lineHeight: getSizeStyles().lineHeight,
    };

    const Tag = tag as keyof JSX.IntrinsicElements;

    // Combine built-in styles with Tailwind classes
    const combinedClassName = [
      'zmp-text',
      textClassName,
    ].filter(Boolean).join(' ');

    const containerClassName = [
      'zmp-component',
      className,
    ].filter(Boolean).join(' ');

    return (
      <div className={containerClassName}>
        <Tag style={textStyles} className={combinedClassName}>
          {text}
        </Tag>
      </div>
    );
  },
};

export default Text;
