'use client';

import React from 'react';
import { ComponentConfig } from '@measured/puck';

export type SwiperProps = {
  slides: Array<{
    id: string;
    content: string;
    image?: string;
    backgroundColor?: string;
  }>;
  autoplay?: boolean;
  defaultActive?: number;
  disableSwipe?: boolean;
  dots?: boolean;
  duration?: number;
  loop?: boolean;
};

export const Swiper: ComponentConfig<SwiperProps> = {
  label: "Swiper",
  fields: {
    slides: {
      type: "array",
      label: "Slides",
      arrayFields: {
        id: { type: "text", label: "ID" },
        content: { type: "textarea", label: "Content" },
        image: { type: "text", label: "Background Image URL" },
        backgroundColor: { type: "text", label: "Background Color" },
      },
      defaultItemProps: {
        id: "slide-1",
        content: "Slide content",
        image: "",
        backgroundColor: "#1677ff",
      },
    },
    autoplay: {
      type: "radio",
      label: "Autoplay",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    defaultActive: {
      type: "text",
      label: "Default Active Index",
      placeholder: "0",
    },
    disableSwipe: {
      type: "radio",
      label: "Disable Swipe",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    dots: {
      type: "radio",
      label: "Show Dots",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    duration: {
      type: "text",
      label: "Autoplay Duration (ms)",
      placeholder: "3000",
    },
    loop: {
      type: "radio",
      label: "Loop",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
  },
  defaultProps: {
    slides: [
      {
        id: "slide-1",
        content: "First Slide",
        image: "https://via.placeholder.com/400x200/1677ff/white?text=Slide+1",
        backgroundColor: "#1677ff",
      },
      {
        id: "slide-2",
        content: "Second Slide",
        image: "https://via.placeholder.com/400x200/52c41a/white?text=Slide+2",
        backgroundColor: "#52c41a",
      },
      {
        id: "slide-3",
        content: "Third Slide",
        image: "https://via.placeholder.com/400x200/faad14/white?text=Slide+3",
        backgroundColor: "#faad14",
      },
    ],
    autoplay: false,
    defaultActive: 0,
    disableSwipe: false,
    dots: true,
    duration: 3000,
    loop: false,
  },
  render: ({ 
    slides, 
    autoplay, 
    defaultActive, 
    disableSwipe, 
    dots, 
    duration, 
    loop,
    puck 
  }) => {
    const [currentIndex, setCurrentIndex] = React.useState(
      typeof defaultActive === 'number' ? defaultActive : parseInt(defaultActive?.toString() || '0')
    );

    const swiperStyles: React.CSSProperties = {
      position: 'relative',
      width: '100%',
      height: '200px',
      borderRadius: '8px',
      overflow: 'hidden',
      backgroundColor: '#f0f0f0',
    };

    const slideContainerStyles: React.CSSProperties = {
      display: 'flex',
      width: `${slides.length * 100}%`,
      height: '100%',
      transform: `translateX(-${(currentIndex * 100) / slides.length}%)`,
      transition: 'transform 0.3s ease',
    };

    const slideStyles: React.CSSProperties = {
      width: `${100 / slides.length}%`,
      height: '100%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white',
      fontSize: '18px',
      fontWeight: '600',
      textAlign: 'center',
      padding: '20px',
      position: 'relative',
    };

    const dotsContainerStyles: React.CSSProperties = {
      position: 'absolute',
      bottom: '12px',
      left: '50%',
      transform: 'translateX(-50%)',
      display: 'flex',
      gap: '8px',
    };

    const dotStyles: React.CSSProperties = {
      width: '8px',
      height: '8px',
      borderRadius: '50%',
      backgroundColor: 'rgba(255, 255, 255, 0.5)',
      cursor: 'pointer',
      transition: 'background-color 0.2s ease',
    };

    const activeDotStyles: React.CSSProperties = {
      ...dotStyles,
      backgroundColor: 'white',
    };

    const navigationStyles: React.CSSProperties = {
      position: 'absolute',
      top: '50%',
      transform: 'translateY(-50%)',
      background: 'rgba(0, 0, 0, 0.3)',
      border: 'none',
      color: 'white',
      fontSize: '18px',
      cursor: 'pointer',
      padding: '8px 12px',
      borderRadius: '4px',
      transition: 'background-color 0.2s ease',
    };

    const goToSlide = (index: number) => {
      if (index < 0) {
        setCurrentIndex(loop ? slides.length - 1 : 0);
      } else if (index >= slides.length) {
        setCurrentIndex(loop ? 0 : slides.length - 1);
      } else {
        setCurrentIndex(index);
      }
    };

    const nextSlide = () => {
      goToSlide(currentIndex + 1);
    };

    const prevSlide = () => {
      goToSlide(currentIndex - 1);
    };

    // Autoplay effect
    React.useEffect(() => {
      if (autoplay && !puck.isEditing) {
        const interval = setInterval(() => {
          nextSlide();
        }, typeof duration === 'number' ? duration : parseInt(duration?.toString() || '3000'));

        return () => clearInterval(interval);
      }
    }, [autoplay, currentIndex, duration, puck.isEditing]);

    return (
      <div className="zmp-component">
        <div style={swiperStyles}>
          <div style={slideContainerStyles}>
            {slides.map((slide, index) => (
              <div
                key={slide.id || index}
                style={{
                  ...slideStyles,
                  backgroundColor: slide.backgroundColor || '#1677ff',
                  backgroundImage: slide.image ? `url(${slide.image})` : 'none',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                }}
              >
                {slide.image && (
                  <div
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'rgba(0, 0, 0, 0.3)',
                    }}
                  />
                )}
                <div style={{ position: 'relative', zIndex: 1 }}>
                  {slide.content}
                </div>
              </div>
            ))}
          </div>

          {/* Navigation arrows */}
          {!disableSwipe && slides.length > 1 && (
            <>
              <button
                style={{ ...navigationStyles, left: '12px' }}
                onClick={prevSlide}
                disabled={!loop && currentIndex === 0}
              >
                ‹
              </button>
              <button
                style={{ ...navigationStyles, right: '12px' }}
                onClick={nextSlide}
                disabled={!loop && currentIndex === slides.length - 1}
              >
                ›
              </button>
            </>
          )}

          {/* Dots indicator */}
          {dots && slides.length > 1 && (
            <div style={dotsContainerStyles}>
              {slides.map((_, index) => (
                <div
                  key={index}
                  style={index === currentIndex ? activeDotStyles : dotStyles}
                  onClick={() => goToSlide(index)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Swiper info for editing mode */}
        {puck.isEditing && (
          <div style={{
            marginTop: '8px',
            fontSize: '12px',
            color: '#666',
            textAlign: 'center',
          }}>
            Swiper: {slides.length} slides, Active: {currentIndex + 1}
            {autoplay && ` (Autoplay: ${duration}ms)`}
          </div>
        )}
      </div>
    );
  },
};

export default Swiper;
