import React from 'react';
import { ComponentConfig } from '@measured/puck';

export type ButtonProps = {
  label: string;
  actionType: 'link' | 'phone' | 'chat' | 'share' | 'custom';

  // Link action
  href?: string;
  openInApp?: boolean;

  // Phone action
  phoneNumber?: string;

  // Chat action
  chatType?: 'user' | 'oa';
  chatId?: string;
  chatMessage?: string;

  // Share action
  shareTitle?: string;
  shareDescription?: string;
  shareImageUrl?: string;

  // Custom action
  customAction?: string;

  // Styling
  variant: 'primary' | 'secondary' | 'ghost' | 'zalo' | 'success' | 'warning' | 'danger';
  size: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  disabled?: boolean;
  icon?: string;
  iconPosition?: 'left' | 'right';

  // Animation & Effects
  animationType?: 'none' | 'bounce' | 'pulse' | 'shake';
  showRipple?: boolean;
};

export const Button: ComponentConfig<ButtonProps> = {
  label: "Button",
  category: "Interactive",
  fields: ({ data }) => {
    const actionType = data?.actionType || 'link';

    return {
      label: {
        type: "text",
        label: "Button Text",
        placeholder: "Click me..."
      },

      actionType: {
        type: "select",
        label: "Action Type",
        options: [
          { label: "🔗 Link", value: "link" },
          { label: "📞 Phone Call", value: "phone" },
          { label: "💬 Chat", value: "chat" },
          { label: "📤 Share", value: "share" },
          { label: "⚙️ Custom", value: "custom" },
        ],
      },

      // Conditional fields based on actionType
      ...(actionType === 'link' && {
        href: {
          type: "text",
          label: "Link URL",
          placeholder: "https://example.com",
        },
        openInApp: {
          type: "radio",
          label: "Open in App",
          options: [
            { label: "Yes", value: true },
            { label: "No", value: false },
          ],
        },
      }),

      ...(actionType === 'phone' && {
        phoneNumber: {
          type: "text",
          label: "Phone Number",
          placeholder: "+84 123 456 789",
        },
      }),

      ...(actionType === 'chat' && {
        chatType: {
          type: "radio",
          label: "Chat Type",
          options: [
            { label: "User", value: "user" },
            { label: "Official Account", value: "oa" },
          ],
        },
        chatId: {
          type: "text",
          label: "Chat ID",
          placeholder: "User ID or OA ID",
        },
        chatMessage: {
          type: "textarea",
          label: "Pre-filled Message",
          placeholder: "Xin chào, tôi muốn tư vấn về...",
        },
      }),

      ...(actionType === 'share' && {
        shareTitle: {
          type: "text",
          label: "Share Title",
          placeholder: "Check out this amazing content!",
        },
        shareDescription: {
          type: "textarea",
          label: "Share Description",
          placeholder: "Description for shared content...",
        },
        shareImageUrl: {
          type: "text",
          label: "Share Image URL",
          placeholder: "https://example.com/image.jpg",
        },
      }),

      ...(actionType === 'custom' && {
        customAction: {
          type: "textarea",
          label: "Custom JavaScript Code",
          placeholder: "console.log('Custom action executed');",
        },
      }),

      // Styling fields
      variant: {
        type: "select",
        label: "Button Style",
        options: [
          { label: "🔵 Primary", value: "primary" },
          { label: "⚪ Secondary", value: "secondary" },
          { label: "👻 Ghost", value: "ghost" },
          { label: "🟦 Zalo Blue", value: "zalo" },
          { label: "🟢 Success", value: "success" },
          { label: "🟡 Warning", value: "warning" },
          { label: "🔴 Danger", value: "danger" },
        ],
      },
      size: {
        type: "radio",
        label: "Button Size",
        options: [
          { label: "Small", value: "small" },
          { label: "Medium", value: "medium" },
          { label: "Large", value: "large" },
        ],
      },
      fullWidth: {
        type: "radio",
        label: "Full Width",
        options: [
          { label: "Yes", value: true },
          { label: "No", value: false },
        ],
      },
      disabled: {
        type: "radio",
        label: "Disabled",
        options: [
          { label: "Yes", value: true },
          { label: "No", value: false },
        ],
      },
      icon: {
        type: "text",
        label: "Icon (emoji or text)",
        placeholder: "🚀"
      },
      iconPosition: {
        type: "radio",
        label: "Icon Position",
        options: [
          { label: "Left", value: "left" },
          { label: "Right", value: "right" },
        ],
      },

      // Animation fields
      animationType: {
        type: "select",
        label: "Animation",
        options: [
          { label: "None", value: "none" },
          { label: "Bounce", value: "bounce" },
          { label: "Pulse", value: "pulse" },
          { label: "Shake", value: "shake" },
        ],
      },
      showRipple: {
        type: "radio",
        label: "Ripple Effect",
        options: [
          { label: "Yes", value: true },
          { label: "No", value: false },
        ],
      },
    };
  },

  defaultProps: {
    label: "Button",
    actionType: "link",
    href: "#",
    openInApp: false,
    phoneNumber: "",
    chatType: "oa",
    chatId: "",
    chatMessage: "",
    shareTitle: "",
    shareDescription: "",
    shareImageUrl: "",
    customAction: "",
    variant: "primary",
    size: "medium",
    fullWidth: false,
    disabled: false,
    icon: "",
    iconPosition: "left",
    animationType: "none",
    showRipple: true,
  },
  render: (props) => {
    const {
      label,
      actionType,
      href,
      openInApp,
      phoneNumber,
      chatType,
      chatId,
      chatMessage,
      shareTitle,
      shareDescription,
      shareImageUrl,
      customAction,
      variant,
      size,
      fullWidth,
      disabled,
      icon,
      iconPosition,
      animationType,
      showRipple,
      puck
    } = props;
    const getSizeStyles = () => {
      switch (size) {
        case 'small':
          return { padding: '8px 16px', fontSize: '14px' };
        case 'large':
          return { padding: '16px 32px', fontSize: '18px' };
        default:
          return { padding: '12px 24px', fontSize: '16px' };
      }
    };

    const getVariantStyles = () => {
      switch (variant) {
        case 'secondary':
          return {
            backgroundColor: 'var(--zmp-secondary-color, #f5f5f5)',
            color: 'var(--zmp-text-color, #333)',
            border: '1px solid var(--zmp-primary-color, #1677ff)',
          };
        case 'ghost':
          return {
            backgroundColor: 'transparent',
            color: 'var(--zmp-primary-color, #1677ff)',
            border: '1px solid var(--zmp-primary-color, #1677ff)',
          };
        case 'zalo':
          return {
            backgroundColor: '#0068ff',
            color: 'white',
            border: 'none',
          };
        case 'success':
          return {
            backgroundColor: '#52c41a',
            color: 'white',
            border: 'none',
          };
        case 'warning':
          return {
            backgroundColor: '#faad14',
            color: 'white',
            border: 'none',
          };
        case 'danger':
          return {
            backgroundColor: '#ff4d4f',
            color: 'white',
            border: 'none',
          };
        default:
          return {
            backgroundColor: 'var(--zmp-primary-color, #1677ff)',
            color: 'white',
            border: 'none',
          };
      }
    };

    const getAnimationStyles = () => {
      switch (animationType) {
        case 'bounce':
          return {
            animation: 'zmp-bounce 2s infinite',
          };
        case 'pulse':
          return {
            animation: 'zmp-pulse 2s infinite',
          };
        case 'shake':
          return {
            animation: 'zmp-shake 0.5s ease-in-out infinite alternate',
          };
        default:
          return {};
      }
    };

    const buttonStyles: React.CSSProperties = {
      ...getSizeStyles(),
      ...getVariantStyles(),
      ...getAnimationStyles(),
      borderRadius: 'var(--zmp-border-radius, 8px)',
      fontFamily: 'inherit',
      cursor: disabled ? 'not-allowed' : 'pointer',
      opacity: disabled ? 0.6 : 1,
      width: fullWidth ? '100%' : 'auto',
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: icon ? '8px' : '0',
      textDecoration: 'none',
      transition: 'all 0.2s ease',
      fontWeight: '500',
      position: 'relative',
      overflow: 'hidden',
    };

    // Action handlers
    const handleAction = async (e: React.MouseEvent) => {
      if (disabled) {
        e.preventDefault();
        return;
      }

      // Add ripple effect
      if (showRipple && !puck.isEditing) {
        const button = e.currentTarget as HTMLElement;
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        const ripple = document.createElement('span');
        ripple.style.cssText = `
          position: absolute;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.6);
          transform: scale(0);
          animation: zmp-ripple 0.6s linear;
          left: ${x}px;
          top: ${y}px;
          width: ${size}px;
          height: ${size}px;
        `;

        button.appendChild(ripple);
        setTimeout(() => ripple.remove(), 600);
      }

      // Handle different action types
      if (puck.isEditing) {
        e.preventDefault();
        return;
      }

      try {
        switch (actionType) {
          case 'phone':
            if (phoneNumber) {
              window.location.href = `tel:${phoneNumber}`;
            }
            break;

          case 'chat':
            if (chatId) {
              // Simulate Zalo Mini App API call
              if (typeof window !== 'undefined' && (window as any).ZaloJavaScriptInterface) {
                await (window as any).ZaloJavaScriptInterface.openChat({
                  type: chatType,
                  id: chatId,
                  message: chatMessage || undefined,
                });
              } else {
                console.log('Zalo Chat:', { type: chatType, id: chatId, message: chatMessage });
              }
            }
            break;

          case 'share':
            if (typeof navigator !== 'undefined' && navigator.share) {
              await navigator.share({
                title: shareTitle || label,
                text: shareDescription || '',
                url: shareImageUrl || window.location.href,
              });
            } else {
              console.log('Share:', { title: shareTitle, description: shareDescription, image: shareImageUrl });
            }
            break;

          case 'custom':
            if (customAction) {
              // Execute custom JavaScript code
              try {
                new Function(customAction)();
              } catch (error) {
                console.error('Custom action error:', error);
              }
            }
            break;

          case 'link':
          default:
            if (href && href !== '#') {
              if (openInApp && typeof window !== 'undefined' && (window as any).ZaloJavaScriptInterface) {
                // Open in Zalo Mini App
                (window as any).ZaloJavaScriptInterface.openWebview(href);
              } else {
                window.open(href, '_blank');
              }
            }
            break;
        }
      } catch (error) {
        console.error('Action error:', error);
      }
    };

    const ButtonContent = () => (
      <>
        {icon && iconPosition === 'left' && <span>{icon}</span>}
        <span>{label}</span>
        {icon && iconPosition === 'right' && <span>{icon}</span>}
      </>
    );

    return (
      <div className="zmp-component">
        <button
          style={buttonStyles}
          onClick={handleAction}
          disabled={disabled}
          type="button"
          className="zmp-button"
        >
          <ButtonContent />
        </button>

        {/* CSS Animations */}
        <style jsx>{`
          @keyframes zmp-bounce {
            0%, 20%, 53%, 80%, 100% {
              transform: translate3d(0, 0, 0);
            }
            40%, 43% {
              transform: translate3d(0, -8px, 0);
            }
            70% {
              transform: translate3d(0, -4px, 0);
            }
            90% {
              transform: translate3d(0, -2px, 0);
            }
          }

          @keyframes zmp-pulse {
            0% {
              transform: scale(1);
            }
            50% {
              transform: scale(1.05);
            }
            100% {
              transform: scale(1);
            }
          }

          @keyframes zmp-shake {
            0% {
              transform: translateX(0);
            }
            25% {
              transform: translateX(-2px);
            }
            50% {
              transform: translateX(2px);
            }
            75% {
              transform: translateX(-1px);
            }
            100% {
              transform: translateX(0);
            }
          }

          @keyframes zmp-ripple {
            to {
              transform: scale(4);
              opacity: 0;
            }
          }

          .zmp-button:hover:not(:disabled) {
            opacity: 0.9;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }

          .zmp-button:active:not(:disabled) {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }

          .zmp-button:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
          }
        `}</style>
      </div>
    );
  },
};

export default Button;
