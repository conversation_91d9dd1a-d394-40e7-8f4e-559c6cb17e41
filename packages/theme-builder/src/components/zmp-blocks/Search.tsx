import React from 'react';
import { ComponentConfig } from '@measured/puck';

export type SearchProps = {
  label?: string;
  placeholder?: string;
  value?: string;
  disabled?: boolean;
  required?: boolean;
  helperText?: string;
  errorText?: string;
  status?: 'default' | 'error';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
};

export const Search: ComponentConfig<SearchProps> = {
  label: "Search",
  fields: {
    label: {
      type: "text",
      label: "Label",
      placeholder: "Enter label...",
    },
    placeholder: {
      type: "text",
      label: "Placeholder",
      placeholder: "Search...",
    },
    value: {
      type: "text",
      label: "Default Value",
      placeholder: "Default value...",
    },
    disabled: {
      type: "radio",
      label: "Disabled",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    required: {
      type: "radio",
      label: "Required",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    helperText: {
      type: "text",
      label: "Helper Text",
      placeholder: "Helper text...",
    },
    errorText: {
      type: "text",
      label: "Error Text",
      placeholder: "Error message...",
    },
    status: {
      type: "radio",
      label: "Status",
      options: [
        { label: "Default", value: "default" },
        { label: "Error", value: "error" },
      ],
    },
    size: {
      type: "select",
      label: "Size",
      options: [
        { label: "Small", value: "small" },
        { label: "Medium", value: "medium" },
        { label: "Large", value: "large" },
      ],
    },
    loading: {
      type: "radio",
      label: "Loading State",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
  },
  defaultProps: {
    label: "Search",
    placeholder: "Search...",
    value: "",
    disabled: false,
    required: false,
    helperText: "",
    errorText: "",
    status: "default",
    size: "medium",
    loading: false,
  },
  render: ({ 
    label, 
    placeholder, 
    value, 
    disabled, 
    required, 
    helperText, 
    errorText, 
    status, 
    size, 
    loading,
    puck 
  }) => {
    const [currentValue, setCurrentValue] = React.useState(value || '');

    React.useEffect(() => {
      setCurrentValue(value || '');
    }, [value]);

    const getSizeStyles = () => {
      switch (size) {
        case 'small':
          return { fontSize: '14px', padding: '8px 12px' };
        case 'large':
          return { fontSize: '18px', padding: '16px' };
        default:
          return { fontSize: '16px', padding: '12px' };
      }
    };

    const containerStyles: React.CSSProperties = {
      marginBottom: '16px',
    };

    const labelStyles: React.CSSProperties = {
      display: 'block',
      fontSize: '14px',
      fontWeight: '500',
      color: '#333',
      marginBottom: '8px',
    };

    const inputWrapperStyles: React.CSSProperties = {
      position: 'relative',
      display: 'flex',
      alignItems: 'center',
      backgroundColor: disabled ? '#f5f5f5' : 'white',
      border: `1px solid ${status === 'error' ? '#ff4d4f' : '#d9d9d9'}`,
      borderRadius: '8px',
      transition: 'border-color 0.2s ease',
    };

    const searchIconStyles: React.CSSProperties = {
      padding: '8px 12px',
      fontSize: '16px',
      color: '#999',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    };

    const inputStyles: React.CSSProperties = {
      flex: 1,
      border: 'none',
      outline: 'none',
      color: disabled ? '#999' : '#333',
      backgroundColor: 'transparent',
      fontFamily: 'inherit',
      ...getSizeStyles(),
      paddingLeft: '0',
    };

    const searchButtonStyles: React.CSSProperties = {
      background: 'none',
      border: 'none',
      fontSize: '16px',
      color: '#1677ff',
      cursor: 'pointer',
      padding: '8px 12px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      transition: 'color 0.2s ease',
    };

    const helperStyles: React.CSSProperties = {
      fontSize: '12px',
      color: status === 'error' ? '#ff4d4f' : '#666',
      marginTop: '4px',
      lineHeight: '1.4',
    };

    const spinnerStyles: React.CSSProperties = {
      width: '16px',
      height: '16px',
      border: '2px solid #f0f0f0',
      borderTop: '2px solid #1677ff',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite',
    };

    const handleSearch = () => {
      if (!disabled && !puck.isEditing) {
        console.log('Search:', currentValue);
      }
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !disabled && !puck.isEditing) {
        handleSearch();
      }
    };

    return (
      <div className="zmp-component">
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
        
        <div style={containerStyles}>
          {label && (
            <label style={labelStyles}>
              {label}
              {required && <span style={{ color: '#ff4d4f' }}> *</span>}
            </label>
          )}
          
          <div style={inputWrapperStyles}>
            <div style={searchIconStyles}>
              {loading ? (
                <div style={spinnerStyles} />
              ) : (
                <span>🔍</span>
              )}
            </div>
            
            <input
              type="search"
              placeholder={placeholder}
              value={currentValue}
              disabled={disabled}
              required={required}
              style={inputStyles}
              onChange={(e) => setCurrentValue(e.target.value)}
              onKeyPress={handleKeyPress}
              readOnly={puck.isEditing}
              onFocus={(e) => {
                if (!puck.isEditing) {
                  e.target.parentElement!.style.borderColor = '#1677ff';
                }
              }}
              onBlur={(e) => {
                if (!puck.isEditing) {
                  e.target.parentElement!.style.borderColor = status === 'error' ? '#ff4d4f' : '#d9d9d9';
                }
              }}
            />
            
            {currentValue && !loading && (
              <button
                type="button"
                style={searchButtonStyles}
                onClick={handleSearch}
                disabled={disabled}
                title="Search"
              >
                ➤
              </button>
            )}
          </div>
          
          {(helperText || errorText) && (
            <div style={helperStyles}>
              {status === 'error' && errorText ? errorText : helperText}
            </div>
          )}
        </div>
      </div>
    );
  },
};

export default Search;
