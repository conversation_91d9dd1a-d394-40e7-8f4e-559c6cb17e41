import React from 'react';

import { Data, Render } from '@measured/puck';

import { ZMP_PUCK_CONFIG } from '../components/zmp-config';

export interface ThemeRendererProps {
  data: Data;
  config?: typeof ZMP_PUCK_CONFIG;
  className?: string;
}

export const ThemeRenderer: React.FC<ThemeRendererProps> = ({
  data,
  className = '',
}) => {
  return (
    <div className={`theme-renderer ${className}`}>
      <Render config={ZMP_PUCK_CONFIG} data={data} />
    </div>
  );
};

export default ThemeRenderer;
