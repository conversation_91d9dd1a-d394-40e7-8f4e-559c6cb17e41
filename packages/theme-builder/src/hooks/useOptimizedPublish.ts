import { useState, useCallback } from 'react';
import { Data } from '@measured/puck';
import { createOptimizedThemeConfig } from '../utils/runcss-extractor';

export interface OptimizedThemeConfig {
  data: Data;
  css: string;
  minified: string;
  hash: string;
  metadata: {
    extractedAt: string;
    classes: string[];
    stats: {
      totalClasses: number;
      uniqueClasses: number;
      cssSize: number;
      originalSize: number;
      minifiedSize: number;
      compressionRatio: number;
    };
  };
}

export interface UseOptimizedPublishOptions {
  onPublish?: (config: OptimizedThemeConfig) => Promise<void> | void;
  onError?: (error: Error) => void;
  onProgress?: (step: string, progress: number) => void;
}

export interface UseOptimizedPublishReturn {
  publish: (data: Data) => Promise<void>;
  isPublishing: boolean;
  error: Error | null;
  progress: {
    step: string;
    percentage: number;
  } | null;
  lastPublished: OptimizedThemeConfig | null;
}

export function useOptimizedPublish(options: UseOptimizedPublishOptions = {}): UseOptimizedPublishReturn {
  const { onPublish, onError, onProgress } = options;
  
  const [isPublishing, setIsPublishing] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [progress, setProgress] = useState<{ step: string; percentage: number } | null>(null);
  const [lastPublished, setLastPublished] = useState<OptimizedThemeConfig | null>(null);

  const updateProgress = useCallback((step: string, percentage: number) => {
    setProgress({ step, percentage });
    onProgress?.(step, percentage);
  }, [onProgress]);

  const publish = useCallback(async (data: Data) => {
    if (isPublishing) return;

    setIsPublishing(true);
    setError(null);
    setProgress(null);

    try {
      // Step 1: Validate data
      updateProgress('Validating theme data...', 10);
      if (!data || !data.content) {
        throw new Error('Invalid theme data: missing content');
      }

      // Step 2: Extract Tailwind classes
      updateProgress('Extracting Tailwind CSS classes...', 25);
      await new Promise(resolve => setTimeout(resolve, 100)); // Small delay for UX

      // Step 3: Generate optimized CSS
      updateProgress('Generating optimized CSS...', 50);
      const optimizedConfig = await createOptimizedThemeConfig(data);

      // Step 4: Validate generated CSS
      updateProgress('Validating generated CSS...', 75);
      if (!optimizedConfig.css) {
        console.warn('No CSS generated from Tailwind classes');
      }

      // Step 5: Save/publish
      updateProgress('Publishing theme...', 90);
      if (onPublish) {
        await onPublish(optimizedConfig);
      }

      // Step 6: Complete
      updateProgress('Theme published successfully!', 100);
      setLastPublished(optimizedConfig);

      // Clear progress after a short delay
      setTimeout(() => {
        setProgress(null);
      }, 2000);

    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred');
      setError(error);
      onError?.(error);
      console.error('Publish error:', error);
    } finally {
      setIsPublishing(false);
    }
  }, [isPublishing, onPublish, onError, updateProgress]);

  return {
    publish,
    isPublishing,
    error,
    progress,
    lastPublished,
  };
}

// Helper hook for theme builder integration
export function useThemeBuilderPublish() {
  return useOptimizedPublish({
    onPublish: async (config) => {
      console.log('📦 Optimized Theme Config:', config);
      console.log('🎨 Generated CSS:', config.css);
      console.log('📊 Stats:', config.metadata.stats);
      
      // Here you would typically save to your backend
      // For now, we'll save to localStorage for demo
      localStorage.setItem('optimized-theme-config', JSON.stringify({
        data: config.data,
        metadata: config.metadata,
      }));

      // Save minified CSS separately for production use
      localStorage.setItem('optimized-theme-css', config.minified);
      localStorage.setItem('optimized-theme-hash', config.hash);

      // Show success message
      if (typeof window !== 'undefined') {
        const compressionPercent = ((1 - config.metadata.stats.compressionRatio) * 100).toFixed(1);
        const message = `Theme published successfully!\n\n` +
          `📊 Stats:\n` +
          `• Classes: ${config.metadata.stats.uniqueClasses}\n` +
          `• Original CSS: ${(config.metadata.stats.originalSize / 1024).toFixed(2)} KB\n` +
          `• Minified CSS: ${(config.metadata.stats.minifiedSize / 1024).toFixed(2)} KB\n` +
          `• Compression: ${compressionPercent}% smaller\n` +
          `• Hash: ${config.hash}\n` +
          `• Generated: ${new Date(config.metadata.extractedAt).toLocaleString()}`;

        alert(message);
      }
    },
    onError: (error) => {
      console.error('❌ Publish failed:', error);
      if (typeof window !== 'undefined') {
        alert(`Failed to publish theme: ${error.message}`);
      }
    },
    onProgress: (step, progress) => {
      console.log(`🔄 ${step} (${progress}%)`);
    },
  });
}

export default useOptimizedPublish;
