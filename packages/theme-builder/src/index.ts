// Main exports for the theme builder package
export * from './components';
export * from './editor';
export * from './renderer';
export * from './types';
export * from './utils';
export * from './hooks';

// Re-export commonly used types and utilities
export type {
  ThemeConfig,
  ComponentConfig,
  ZMPComponent,
  PuckConfig,
  ThemeBuilderProps,
  HeaderAction,
} from './types';

export {
  createThemeConfig,
  applyThemeConfig,
  validateThemeConfig,
} from './utils';

export {
  isValidPuckData,
  createEmptyPuckData,
} from './utils/data-validation';

export {
  migrate,
  transformProps,
} from '@measured/puck';

export {
  ThemeBuilder,
} from './editor';

export {
  ThemeRenderer,
} from './renderer';

export {
  useTheme,
  useThemeBuilder,
} from './hooks';

// Export Tailwind CSS utilities
export {
  createTailwindField,
  createCommonTailwindFields,
  addTailwindFields,
  getTailwindDefaultProps,
  TAILWIND_FIELD_NAMES,
} from './utils/tailwind-field-utils';

export {
  applyTailwindToComponent,
  applyTailwindToComponents,
  getTailwindFieldsForComponent,
  combineClassNames,
} from './utils/apply-tailwind-to-all';

// Export RunCSS service
export {
  getPreviewInstance,
  isPreviewInstanceActive,
  createRunCSSInstance,
  processClassesForPreview,
  resetPreviewInstance,
  buildProductionCSS,
  validateClasses,
  minifyCSS,
  generateHash,
  DEFAULT_RUNCSS_CONFIG,
} from './services/runcss-service';

// Export CSS extraction utilities
export {
  extractTailwindClasses,
  generateCSSFromClasses,
  buildOptimizedCSS,
  createOptimizedThemeConfig,
} from './utils/runcss-extractor';

// Export CSS merge utilities
export {
  mergeOptimizedCSSIntoData,
  extractOptimizedCSSFromData,
  hasOptimizedCSS,
  createRenderReadyData,
  separateEditAndRenderData,
  updateOptimizedCSSInData,
  removeOptimizedCSSFromData,
  validateOptimizedCSSData,
  getCSSStatsFromData,
} from './utils/merge-optimized-css';

// Export optimized publish hook
export {
  useOptimizedPublish,
  useThemeBuilderPublish,
} from './hooks/useOptimizedPublish';

// Export Tailwind field component
export {
  TailwindClassField,
} from './fields/TailwindClassField';

// Export debug utilities (for development)
export {
  debugRunCSSProcessing,
  debugMultipleClasses,
  debugResetAndTest,
  runRunCSSTests,
  monitorRunCSSPerformance,
  debugCSSOutput,
  autoFixRunCSSIssues,
} from './utils/debug-runcss';


