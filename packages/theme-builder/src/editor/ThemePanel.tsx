import React, { useState } from 'react';
import { ThemeConfig } from '../types';

interface ThemePanelProps {
  theme: ThemeConfig;
  onChange: (theme: ThemeConfig) => void;
  onClose: () => void;
}

export const ThemePanel: React.FC<ThemePanelProps> = ({
  theme,
  onChange,
  onClose,
}) => {
  const [activeTab, setActiveTab] = useState<'colors' | 'typography' | 'spacing' | 'layout'>('colors');

  const updateTheme = (updates: Partial<ThemeConfig>) => {
    onChange({
      ...theme,
      ...updates,
    });
  };

  const updateColors = (colorUpdates: Partial<ThemeConfig['colors']>) => {
    updateTheme({
      colors: {
        ...theme.colors,
        ...colorUpdates,
      },
    });
  };

  const updateTypography = (typographyUpdates: Partial<ThemeConfig['typography']>) => {
    updateTheme({
      typography: {
        ...theme.typography,
        ...typographyUpdates,
      },
    });
  };

  const tabs = [
    { id: 'colors', label: 'Colors', icon: '🎨' },
    { id: 'typography', label: 'Typography', icon: '📝' },
    { id: 'spacing', label: 'Spacing', icon: '📐' },
    { id: 'layout', label: 'Layout', icon: '📱' },
  ];

  return (
    <div className="h-full bg-white border-r border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        <h2 className="text-lg font-semibold">Theme Settings</h2>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 text-xl"
        >
          ×
        </button>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 px-2 py-3 text-xs font-medium text-center border-b-2 transition-colors ${
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <div className="flex flex-col items-center space-y-1">
              <span className="text-sm">{tab.icon}</span>
              <span>{tab.label}</span>
            </div>
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {activeTab === 'colors' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Primary Color
              </label>
              <input
                type="color"
                value={theme.colors.primary.main}
                onChange={(e) => updateColors({
                  primary: {
                    ...theme.colors.primary,
                    main: e.target.value,
                  }
                })}
                className="w-full h-10 rounded border border-gray-300"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Secondary Color
              </label>
              <input
                type="color"
                value={theme.colors.secondary.main}
                onChange={(e) => updateColors({
                  secondary: {
                    ...theme.colors.secondary,
                    main: e.target.value,
                  }
                })}
                className="w-full h-10 rounded border border-gray-300"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Background Color
              </label>
              <input
                type="color"
                value={theme.colors.background.default}
                onChange={(e) => updateColors({
                  background: {
                    ...theme.colors.background,
                    default: e.target.value,
                  }
                })}
                className="w-full h-10 rounded border border-gray-300"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Text Color
              </label>
              <input
                type="color"
                value={theme.colors.text.primary}
                onChange={(e) => updateColors({
                  text: {
                    ...theme.colors.text,
                    primary: e.target.value,
                  }
                })}
                className="w-full h-10 rounded border border-gray-300"
              />
            </div>
          </div>
        )}

        {activeTab === 'typography' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Font Family
              </label>
              <select
                value={theme.typography.fontFamily.primary}
                onChange={(e) => updateTypography({
                  fontFamily: {
                    ...theme.typography.fontFamily,
                    primary: e.target.value,
                  }
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif">
                  System Font
                </option>
                <option value="'Inter', sans-serif">Inter</option>
                <option value="'Roboto', sans-serif">Roboto</option>
                <option value="'Open Sans', sans-serif">Open Sans</option>
                <option value="'Poppins', sans-serif">Poppins</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Base Font Size
              </label>
              <input
                type="text"
                value={theme.typography.fontSize.base}
                onChange={(e) => updateTypography({
                  fontSize: {
                    ...theme.typography.fontSize,
                    base: e.target.value,
                  }
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="1rem"
              />
            </div>
          </div>
        )}

        {activeTab === 'spacing' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Base Spacing
              </label>
              <input
                type="text"
                value={theme.spacing.md}
                onChange={(e) => updateTheme({
                  spacing: {
                    ...theme.spacing,
                    md: e.target.value,
                  }
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="1rem"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Border Radius
              </label>
              <input
                type="text"
                value={theme.borderRadius.md}
                onChange={(e) => updateTheme({
                  borderRadius: {
                    ...theme.borderRadius,
                    md: e.target.value,
                  }
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="0.5rem"
              />
            </div>
          </div>
        )}

        {activeTab === 'layout' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Width
              </label>
              <input
                type="text"
                value={theme.layout.maxWidth}
                onChange={(e) => updateTheme({
                  layout: {
                    ...theme.layout,
                    maxWidth: e.target.value,
                  }
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="1200px"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Container Padding
              </label>
              <input
                type="text"
                value={theme.layout.containerPadding}
                onChange={(e) => updateTheme({
                  layout: {
                    ...theme.layout,
                    containerPadding: e.target.value,
                  }
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="1rem"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
