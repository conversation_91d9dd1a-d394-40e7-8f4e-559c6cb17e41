import React from 'react';
import { OverrideProps } from '@measured/puck';

interface MobileOverridesProps {
  isMobile: boolean;
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  headerActions?: React.ReactNode;
}

export const MobileOverrides = ({
  isMobile,
  sidebarOpen,
  setSidebarOpen,
  headerActions,
}: MobileOverridesProps) => {
  if (!isMobile) return {};

  return {
    // Custom header for mobile
    header: ({ children, actions }: OverrideProps<'header'>) => (
      <div className="mobile-header">
        <div className="mobile-header-content">
          {/* Mobile menu toggle */}
          <button
            className="mobile-menu-toggle"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '18px',
              padding: '8px',
              cursor: 'pointer',
            }}
          >
            {sidebarOpen ? '✕' : '☰'}
          </button>

          {/* Header title */}
          <div className="mobile-header-title">
            <span style={{ fontSize: '16px', fontWeight: '600' }}>
              Theme Builder
            </span>
          </div>

          {/* Header actions */}
          <div className="mobile-header-actions">
            {headerActions}
            {actions}
          </div>
        </div>

        <style jsx>{`
          .mobile-header {
            position: sticky;
            top: 0;
            z-index: 1000;
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 0;
          }

          .mobile-header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            min-height: 56px;
          }

          .mobile-menu-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 40px;
            min-height: 40px;
            border-radius: 8px;
            transition: background-color 0.2s;
          }

          .mobile-menu-toggle:hover {
            background-color: #f3f4f6;
          }

          .mobile-header-title {
            flex: 1;
            text-align: center;
            margin: 0 16px;
          }

          .mobile-header-actions {
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .mobile-header-actions button {
            padding: 8px 12px;
            font-size: 14px;
            border-radius: 6px;
            min-height: 36px;
          }
        `}</style>
      </div>
    ),

    // Custom component list for mobile
    components: ({ children }: OverrideProps<'components'>) => (
      <div className={`mobile-components ${sidebarOpen ? 'open' : 'closed'}`}>
        <div className="mobile-components-overlay" onClick={() => setSidebarOpen(false)} />
        <div className="mobile-components-sidebar">
          <div className="mobile-components-header">
            <h3>Components</h3>
            <button onClick={() => setSidebarOpen(false)}>✕</button>
          </div>
          <div className="mobile-components-content">
            {children}
          </div>
        </div>

        <style jsx>{`
          .mobile-components {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1001;
            pointer-events: ${sidebarOpen ? 'auto' : 'none'};
            opacity: ${sidebarOpen ? 1 : 0};
            transition: opacity 0.3s ease;
          }

          .mobile-components-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            opacity: ${sidebarOpen ? 1 : 0};
            transition: opacity 0.3s ease;
          }

          .mobile-components-sidebar {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 280px;
            max-width: 80vw;
            background: white;
            transform: translateX(${sidebarOpen ? '0' : '-100%'});
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
          }

          .mobile-components-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
          }

          .mobile-components-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
          }

          .mobile-components-header button {
            background: none;
            border: none;
            font-size: 18px;
            padding: 4px;
            cursor: pointer;
            border-radius: 4px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .mobile-components-header button:hover {
            background: #e5e7eb;
          }

          .mobile-components-content {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
          }
        `}</style>
      </div>
    ),

    // Custom component item for mobile
    componentItem: ({ name, children }: OverrideProps<'componentItem'>) => (
      <div className="mobile-component-item">
        <div className="mobile-component-item-content">
          {children}
        </div>

        <style jsx>{`
          .mobile-component-item {
            margin-bottom: 8px;
          }

          .mobile-component-item-content {
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: 48px;
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
          }

          .mobile-component-item-content:hover {
            border-color: #3b82f6;
            background: #f8fafc;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
          }

          .mobile-component-item-content:active {
            transform: translateY(0);
            box-shadow: 0 1px 4px rgba(59, 130, 246, 0.1);
          }
        `}</style>
      </div>
    ),

    // Custom fields panel for mobile
    fields: ({ children }: OverrideProps<'fields'>) => (
      <div className="mobile-fields">
        <div className="mobile-fields-content">
          {children}
        </div>

        <style jsx>{`
          .mobile-fields {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e5e7eb;
            max-height: 50vh;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
          }

          .mobile-fields-content {
            padding: 16px;
          }

          .mobile-fields-content > * {
            margin-bottom: 16px;
          }

          .mobile-fields-content > *:last-child {
            margin-bottom: 0;
          }
        `}</style>
      </div>
    ),

    // Custom preview for mobile
    preview: ({ children }: OverrideProps<'preview'>) => (
      <div className="mobile-preview">
        <div className="mobile-preview-content">
          {children}
        </div>

        <style jsx>{`
          .mobile-preview {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
          }

          .mobile-preview-content {
            flex: 1;
            overflow: auto;
            padding: 16px;
            background: #f9fafb;
          }

          /* Ensure preview content is touch-friendly */
          .mobile-preview-content * {
            touch-action: manipulation;
          }

          /* Optimize for mobile viewport */
          @media (max-width: 768px) {
            .mobile-preview-content {
              padding: 8px;
            }
          }
        `}</style>
      </div>
    ),
  };
};
