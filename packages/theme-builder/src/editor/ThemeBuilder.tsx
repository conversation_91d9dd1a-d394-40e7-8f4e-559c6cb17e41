/* eslint-disable react-hooks/rules-of-hooks */
'use client';

import React, { useEffect, useRef, useState } from 'react';

import { Button, Data, IconButton, Puck, createUsePuck } from '@measured/puck';
import '@measured/puck/puck.css';
import {
  ChevronDown,
  ChevronUp,
  Globe,
  Palette,
  Smartphone,
} from 'lucide-react';

import { ZMP_PUCK_CONFIG } from '../components/zmp-config';
import '../styles/mobile.css';
import { HeaderAction, ThemeBuilderProps } from '../types';





/* eslint-disable react-hooks/rules-of-hooks */



/* eslint-disable react-hooks/rules-of-hooks */



const usePuck = createUsePuck();

// Helper function to render header actions
const renderHeaderActions = (
  headerActions: HeaderAction[] | React.ReactNode,
) => {
  // If it's already a React node, return as is
  if (
    React.isValidElement(headerActions) ||
    typeof headerActions !== 'object' ||
    !Array.isArray(headerActions)
  ) {
    return headerActions;
  }

  // If it's an array of HeaderAction configs, render Puck buttons
  return (headerActions as HeaderAction[]).map((action, index) => {
    if (action.type === 'button') {
      return (
        <Button
          key={index}
          onClick={action.onClick}
          disabled={action.disabled}
          variant={action.variant === 'primary' ? 'primary' : 'secondary'}
        >
          {action.icon && (
            <span style={{ marginRight: '4px' }}>{action.icon}</span>
          )}
          {action.label}
        </Button>
      );
    }
    return null;
  });
};

// Mobile Tabs Component
const MobileTabs = ({
  tabs,
  onTabCollapse,
  scrollTop,
}: {
  tabs: { label: string; body: React.ReactNode }[];
  onTabCollapse: () => void;
  scrollTop: number;
}) => {
  const [currentTab, setCurrentTab] = useState(-1);
  const itemSelector = usePuck((s) => s.appState.ui.itemSelector);
  const isDragging = usePuck((s) => s.appState.ui.isDragging);
  const currentTabRef = useRef(currentTab);

  useEffect(() => {
    if (currentTabRef.current !== -1 && itemSelector) {
      setCurrentTab(1); // Switch to Fields tab when item is selected
    }
  }, [itemSelector]);

  useEffect(() => {
    currentTabRef.current = currentTab;
  }, [currentTab]);

  useEffect(() => {
    if (isDragging && currentTab === 1) {
      setCurrentTab(-1);
    }
  }, [currentTab, isDragging]);

  useEffect(() => {
    if (scrollTop === 0) {
      setCurrentTab(-1);
      onTabCollapse();
    }
  }, [scrollTop]);

  return (
    <div
      onClick={(e) => e.stopPropagation()}
      style={{
        background: "#ffffff",
        pointerEvents: "all",
        borderTop: "1px solid #e5e7eb",
        boxShadow: "rgba(140, 152, 164, 0.25) 0px 0px 6px 0px",
        borderRadius: "16px 16px 0 0",
      }}
    >
      {/* Tab Headers */}
      <div
        style={{
          display: "flex",
          paddingLeft: 16,
          paddingRight: 16,
          borderBottom: "1px solid #e5e7eb",
          overflowX: "auto",
          background: "#f9fafb",
        }}
      >
        {tabs.map((tab, idx) => {
          const isCurrentTab = currentTab === idx;
          return (
            <button
              key={idx}
              type="button"
              onClick={() => {
                if (currentTab === idx) {
                  setCurrentTab(-1);
                } else {
                  setCurrentTab(idx);
                  if (scrollTop < 20) {
                    setTimeout(() => {
                      document
                        .querySelector("#mobile-action-bar")
                        ?.scroll({ top: 128, behavior: "smooth" });
                    }, 25);
                  }
                }
              }}
              style={{
                fontFamily: "inherit",
                fontSize: 14,
                fontWeight: 500,
                padding: "12px 16px",
                paddingTop: 15,
                color: isCurrentTab ? "#3b82f6" : "#6b7280",
                border: "none",
                borderBottom: isCurrentTab
                  ? "3px solid #3b82f6"
                  : "3px solid transparent",
                background: "transparent",
                cursor: "pointer",
                whiteSpace: "nowrap",
                transition: "all 0.2s ease",
              }}
            >
              {tab.label}
            </button>
          );
        })}

        {/* Collapse/Expand Button */}
        <div
          style={{
            marginLeft: "auto",
            display: "flex",
            alignItems: "center",
            gap: 8,
          }}
        >
          <IconButton
            onClick={() => {
              setCurrentTab(currentTab === -1 ? 0 : -1);
              if (currentTab !== -1) {
                onTabCollapse();
              } else {
                setTimeout(() => {
                  document
                    .querySelector("#mobile-action-bar")
                    ?.scroll({ top: 128, behavior: "smooth" });
                }, 25);
              }
            }}
            title={currentTab !== -1 ? "Collapse Tabs" : "Expand Tabs"}
          >
            {currentTab === -1 ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </IconButton>
        </div>
      </div>

      {/* Tab Content */}
      <div style={{ overflowX: "auto", maxHeight: "50vh", overflowY: "auto" }}>
        {tabs.map((tab, idx) => {
          const isCurrentTab = currentTab === idx;
          return (
            <div
              key={idx}
              style={{
                display: isCurrentTab ? "block" : "none",
                padding: isCurrentTab ? "16px" : "0",
              }}
            >
              {tab.body}
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Custom Mobile Header Component
const MobileHeader = ({
  onPublish,
  onPreview,
  headerActions
}: {
  onPublish?: (data: Data) => void;
  onPreview?: (data: Data) => void;
  headerActions?: HeaderAction[] | React.ReactNode;
}) => {
  const get = usePuck((s) => s.get);
  const dispatch = usePuck((s) => s.dispatch);
  const previewMode = usePuck((s) => s.appState.ui.previewMode);

  const toggleMode = () => {
    dispatch({
      type: "setUi",
      ui: {
        previewMode: previewMode === "edit" ? "interactive" : "edit",
      },
    });
  };

  const handlePublish = () => {
    const data = get().appState.data;
    onPublish?.(data);
  };

  const handlePreview = () => {
    const data = get().appState.data;
    onPreview?.(data);
  };

  return (
    <header
      style={{
        display: "flex",
        flexWrap: "wrap",
        gap: 8,
        padding: "12px 16px",
        background: "white",
        color: "black",
        alignItems: "center",
        borderBottom: "1px solid #e5e7eb",
        minHeight: "56px",
        position: "sticky",
        top: 0,
        zIndex: 1000,
      }}
      onClick={() => dispatch({ type: "setUi", ui: { itemSelector: null } })}
    >
      <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
        <Smartphone size={18} />
        <span style={{ fontWeight: 600, fontSize: "16px" }}>Theme Builder</span>
      </div>

      <div style={{ marginLeft: "auto", display: "flex", gap: 6 }}>
        {/* Custom header actions */}
        {renderHeaderActions(headerActions)}

        <Button
          onClick={toggleMode}
          variant="secondary"
          size="medium"
        >
          {previewMode === "edit" ? "Preview" : "Edit"}
        </Button>

        {onPreview && (
          <Button
            onClick={handlePreview}
            variant="secondary"
            size="medium"
            icon={<Palette size="12" />}
          >
            Preview
          </Button>
        )}

        {onPublish && (
          <Button
            onClick={handlePublish}
            variant="primary"
            size="medium"
            icon={<Globe size="12" />}
          >
            Publish
          </Button>
        )}
      </div>
    </header>
  );
};

// Hook to detect mobile device
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return isMobile;
};

// Mobile Puck Component
const MobilePuck = ({
  config,
  data,
  onChange,
  onPublish,
  onPreview,
  viewports,
  iframe,
  headerActions,
}: {
  config: any;
  data: any;
  onChange?: (data: Data) => void;
  onPublish?: (data: Data) => void;
  onPreview?: (data: Data) => void;
  viewports?: any;
  iframe?: any;
  headerActions?: HeaderAction[] | React.ReactNode;
}) => {
  const [hoveringTabs, setHoveringTabs] = useState(false);
  const [actionBarScroll, setActionBarScroll] = useState(0);

  return (
    <div style={{ position: "relative", height: "100vh", overflow: "hidden" }}>
      {/* Sticky Header */}
      <div style={{ position: "sticky", top: 0, zIndex: 2 }}>
        <MobileHeader
          onPublish={onPublish}
          onPreview={onPreview}
          headerActions={headerActions}
        />
      </div>

      {/* Preview Area */}
      <div
        style={{
          position: "relative",
          overflowY: hoveringTabs ? "hidden" : "auto",
          zIndex: 0,
          height: "calc(100vh - 56px)", // Subtract header height
          background: "#f9fafb",
        }}
      >
        <Puck.Preview />
      </div>

      {/* Bottom Action Bar with Tabs */}
      <div
        id="mobile-action-bar"
        style={{
          position: "fixed",
          bottom: 0,
          overflowY: "auto",
          overflowX: "hidden",
          maxHeight: "100vh",
          width: "100%",
          boxSizing: "border-box",
          paddingTop: "calc(100vh - 120px)", // Leave space for tabs
          pointerEvents: hoveringTabs ? undefined : "none",
          zIndex: 1000,
          overscrollBehavior: "none",
        }}
        onTouchStart={() => setHoveringTabs(false)}
        onScrollCapture={(e) => {
          setActionBarScroll(e.currentTarget.scrollTop);
        }}
      >
        <div
          style={{
            background: "white",
            position: "relative",
            pointerEvents: "none",
            zIndex: 0,
          }}
          onMouseOver={(e) => {
            e.stopPropagation();
            setHoveringTabs(true);
          }}
          onTouchStart={(e) => {
            e.stopPropagation();
            setHoveringTabs(true);
          }}
          onMouseOut={() => {
            setHoveringTabs(false);
          }}
        >
          {/* Force react to render when hoveringTabs changes */}
          {hoveringTabs && <span />}

          <MobileTabs
            onTabCollapse={() => {
              setTimeout(() => setHoveringTabs(false), 50);
            }}
            scrollTop={actionBarScroll}
            tabs={[
              {
                label: "Components",
                body: (
                  <div style={{ padding: "0" }}>
                    <Puck.Components />
                  </div>
                )
              },
              {
                label: "Fields",
                body: (
                  <div style={{ padding: "0" }}>
                    <Puck.Fields />
                  </div>
                )
              },
              {
                label: "Outline",
                body: (
                  <div style={{ padding: "0" }}>
                    <Puck.Outline />
                  </div>
                )
              },
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export const ThemeBuilder: React.FC<ThemeBuilderProps> = ({
  config = ZMP_PUCK_CONFIG,
  data,
  onChange,
  onPreview,
  onPublish,
  showThemePanel = false,
  showPreview = true,
  viewports,
  iframe,
  headerActions,
}) => {
  const isMobile = useIsMobile();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return null;

  // Auto-configure viewports based on device type
  const autoViewports = isMobile
    ? [
        { width: 375, height: 812, label: 'iPhone 13', icon: '📱' },
        { width: 390, height: 844, label: 'iPhone 14', icon: '📱' },
        { width: 360, height: 800, label: 'Android', icon: '📱' },
      ]
    : [
        { width: 1200, height: 800, label: 'Desktop Editor', icon: '💻' },
      ];

  const finalViewports = viewports || autoViewports;

  // Auto-configure iframe settings based on device type
  const autoIframe = isMobile
    ? { enabled: false } // Disable iframe on mobile for better performance
    : iframe || { enabled: false };

  // Mobile Layout
  if (isMobile) {
    return (
      <Puck
        config={config}
        data={data}
        onChange={onChange}
        iframe={autoIframe}
        viewports={finalViewports}
        overrides={{
          puck: () => (
            <MobilePuck
              config={config}
              data={data}
              onChange={onChange}
              onPublish={onPublish}
              onPreview={onPreview}
              viewports={finalViewports}
              iframe={autoIframe}
              headerActions={headerActions}
            />
          ),
        }}
      />
    );
  }

  // Desktop Layout (Original)
  return (
    <div className="theme-builder-container desktop-layout flex h-screen">
      <div className="flex-1">
        <Puck
          config={config}
          data={data}
          onPublish={onPublish}
          onChange={onChange}
          viewports={finalViewports}
          iframe={autoIframe}
          overrides={{
            headerActions: ({ children }) => (
              <>
                {/* Custom header actions from parent */}
                {renderHeaderActions(headerActions)}
                {children}
              </>
            ),
          }}
        />
      </div>
    </div>
  );
};
