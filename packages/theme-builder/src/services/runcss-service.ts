/**
 * RunCSS Service
 * 
 * Provides utility functions for working with RunCSS in both editor and production environments.
 * - Editor: Real-time CSS generation for immediate preview
 * - Production: Optimized CSS extraction and building
 */

import RunCSS from 'runcss';

export interface RunCSSConfig {
  colors?: Record<string, string | Record<string, string>>;
  screens?: Record<string, string>;
  fontFamily?: Record<string, string[]>;
  spacing?: Record<string, string>;
  borderRadius?: Record<string, string>;
  boxShadow?: Record<string, string>;
}

export interface RunCSSInstance {
  processClasses: (classes: string) => void;
  exportCSS: () => string;
  startWatching: (element?: Element) => void;
  stopWatching: () => void;
}

// Singleton instance for preview
let previewInstance: RunCSSInstance | null = null;

export interface CSSBuildResult {
  css: string;
  minified: string;
  hash: string;
  size: number;
  classes: string[];
  stats: {
    originalSize: number;
    minifiedSize: number;
    compressionRatio: number;
    classCount: number;
  };
}

/**
 * Default RunCSS configuration optimized for Zalo Mini Apps
 */
export const DEFAULT_RUNCSS_CONFIG: RunCSSConfig = {
  colors: {
    // Zalo brand colors
    'zalo': '#0068ff',
    'zalo-light': '#4096ff',
    'zalo-dark': '#0050d1',
    
    // Extended color palette
    'primary': '#3b82f6',
    'secondary': '#6b7280',
    'success': '#10b981',
    'warning': '#f59e0b',
    'error': '#ef4444',
    'info': '#06b6d4',
    
    // Gray scale
    'gray': {
      '50': '#f9fafb',
      '100': '#f3f4f6',
      '200': '#e5e7eb',
      '300': '#d1d5db',
      '400': '#9ca3af',
      '500': '#6b7280',
      '600': '#4b5563',
      '700': '#374151',
      '800': '#1f2937',
      '900': '#111827',
    },
  },
  
  screens: {
    'xs': '475px',
    'sm': '640px',
    'md': '768px',
    'lg': '1024px',
    'xl': '1280px',
    '2xl': '1536px',
  },
  
  fontFamily: {
    'sans': ['Inter', 'system-ui', 'sans-serif'],
    'serif': ['Georgia', 'serif'],
    'mono': ['Monaco', 'Consolas', 'monospace'],
  },
  
  spacing: {
    '18': '4.5rem',
    '88': '22rem',
    '128': '32rem',
  },
  
  borderRadius: {
    'xl': '0.75rem',
    '2xl': '1rem',
    '3xl': '1.5rem',
  },
  
  boxShadow: {
    'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
    'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    'hard': '0 10px 40px -10px rgba(0, 0, 0, 0.2)',
  },
};

/**
 * Get or create the preview RunCSS instance (singleton)
 */
export function getPreviewInstance(config: RunCSSConfig = {}): RunCSSInstance {
  if (!previewInstance) {
    const mergedConfig = {
      ...DEFAULT_RUNCSS_CONFIG,
      ...config,
      colors: {
        ...DEFAULT_RUNCSS_CONFIG.colors,
        ...config.colors,
      },
      screens: {
        ...DEFAULT_RUNCSS_CONFIG.screens,
        ...config.screens,
      },
    };

    previewInstance = RunCSS(mergedConfig);
    console.log('🎯 RunCSS instance created (watching not started yet)');
  }

  return previewInstance;
}

/**
 * Create a new RunCSS instance with custom configuration (for production builds)
 */
export function createRunCSSInstance(config: RunCSSConfig = {}): RunCSSInstance {
  const mergedConfig = {
    ...DEFAULT_RUNCSS_CONFIG,
    ...config,
    colors: {
      ...DEFAULT_RUNCSS_CONFIG.colors,
      ...config.colors,
    },
    screens: {
      ...DEFAULT_RUNCSS_CONFIG.screens,
      ...config.screens,
    },
  };

  return RunCSS(mergedConfig);
}

/**
 * Check if preview instance is initialized and watching
 */
export function isPreviewInstanceActive(): boolean {
  return previewInstance !== null;
}

/**
 * Reset the preview instance (useful for testing or config changes)
 */
export function resetPreviewInstance(): void {
  if (previewInstance) {
    try {
      previewInstance.stopWatching();
      console.log('🛑 RunCSS watching stopped');
    } catch (error) {
      console.warn('Failed to stop RunCSS watching:', error);
    }
  }
  previewInstance = null;
}

/**
 * Process classes and generate CSS for editor preview (uses singleton)
 */
export function processClassesForPreview(
  classes: string,
  config?: RunCSSConfig
): string {
  try {
    const runcss = getPreviewInstance(config);
    runcss.processClasses(classes);
    return runcss.exportCSS();
  } catch (error) {
    console.error('Failed to process classes for preview:', error);
    // Try to reset and retry once
    if (error instanceof Error && error.message.includes("can't initialize RunCSS twice")) {
      console.warn('Resetting RunCSS instance and retrying...');
      resetPreviewInstance();
      try {
        const newRuncss = getPreviewInstance(config);
        newRuncss.processClasses(classes);
        return newRuncss.exportCSS();
      } catch (retryError) {
        console.error('Retry failed:', retryError);
        return '';
      }
    }
    return '';
  }
}

/**
 * Build optimized CSS for production
 */
export function buildProductionCSS(
  classes: string[],
  config?: RunCSSConfig
): CSSBuildResult {
  try {
    const runcss = createRunCSSInstance(config);
    
    // Process all classes
    const classString = classes.join(' ');
    runcss.processClasses(classString);
    
    // Export CSS
    const css = runcss.exportCSS();
    
    // Minify CSS
    const minified = minifyCSS(css);
    
    // Generate hash
    const hash = generateHash(minified);
    
    // Calculate stats
    const originalSize = new Blob([css]).size;
    const minifiedSize = new Blob([minified]).size;
    const compressionRatio = originalSize > 0 ? minifiedSize / originalSize : 0;
    
    return {
      css,
      minified,
      hash,
      size: minifiedSize,
      classes,
      stats: {
        originalSize,
        minifiedSize,
        compressionRatio,
        classCount: classes.length,
      },
    };
  } catch (error) {
    console.error('Failed to build production CSS:', error);
    return {
      css: '',
      minified: '',
      hash: '',
      size: 0,
      classes: [],
      stats: {
        originalSize: 0,
        minifiedSize: 0,
        compressionRatio: 0,
        classCount: 0,
      },
    };
  }
}

/**
 * Validate Tailwind CSS classes
 */
export function validateClasses(classes: string[]): {
  valid: string[];
  invalid: string[];
  warnings: string[];
} {
  const valid: string[] = [];
  const invalid: string[] = [];
  const warnings: string[] = [];

  // Common Tailwind patterns
  const patterns = [
    // Responsive prefixes
    /^(xs|sm|md|lg|xl|2xl):/,
    
    // State prefixes
    /^(hover|focus|active|visited|disabled|checked|first|last|odd|even|group-hover|group-focus):/,
    
    // Spacing utilities
    /^(p|m|px|py|pt|pr|pb|pl|mx|my|mt|mr|mb|ml)-(\d+|auto|px)$/,
    
    // Sizing utilities
    /^(w|h|min-w|min-h|max-w|max-h)-(auto|full|screen|min|max|fit|\d+|1\/\d+|\d+\/\d+)$/,
    
    // Text utilities
    /^text-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|7xl|8xl|9xl)$/,
    /^text-\w+(-\d+)?$/,
    
    // Background utilities
    /^bg-\w+(-\d+)?$/,
    
    // Border utilities
    /^border(-\w+)?(-\d+)?$/,
    /^rounded(-\w+)?$/,
    
    // Flexbox utilities
    /^(flex|grid|block|inline|hidden|relative|absolute|fixed|sticky)$/,
    /^(justify|items|content|self)-(start|end|center|between|around|evenly|stretch|auto)$/,
    
    // Display utilities
    /^(block|inline-block|inline|flex|inline-flex|table|grid|hidden)$/,
  ];

  classes.forEach(className => {
    if (!className.trim()) return;

    // Check if class matches any pattern
    const isValid = patterns.some(pattern => pattern.test(className)) ||
                   ['container', 'group', 'sr-only', 'not-sr-only'].includes(className);

    if (isValid) {
      valid.push(className);
    } else {
      // Check for potential custom classes
      if (className.includes('-') && !className.startsWith('_')) {
        warnings.push(`Potential custom class: ${className}`);
        valid.push(className); // Include it anyway
      } else {
        invalid.push(className);
      }
    }
  });

  return { valid, invalid, warnings };
}

/**
 * Minify CSS by removing unnecessary whitespace and comments
 */
export function minifyCSS(css: string): string {
  return css
    .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
    .replace(/\s+/g, ' ') // Collapse whitespace
    .replace(/;\s*}/g, '}') // Remove last semicolon in blocks
    .replace(/\s*{\s*/g, '{') // Clean up braces
    .replace(/\s*}\s*/g, '}')
    .replace(/\s*;\s*/g, ';') // Clean up semicolons
    .replace(/\s*,\s*/g, ',') // Clean up commas
    .replace(/\s*:\s*/g, ':') // Clean up colons
    .trim();
}

/**
 * Generate hash for CSS content (for caching)
 */
export function generateHash(content: string): string {
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

/**
 * Create CSS file with metadata comments
 */
export function createCSSFile(
  css: string,
  metadata: {
    classes: string[];
    generatedAt: string;
    hash: string;
    stats: any;
  }
): string {
  const minified = minifyCSS(css);
  
  return `/*! Generated by RunCSS Service */
/*! Generated at: ${metadata.generatedAt} */
/*! Classes: ${metadata.classes.length} */
/*! Hash: ${metadata.hash} */
/*! Stats: ${JSON.stringify(metadata.stats)} */

${minified}`;
}

/**
 * Extract classes from HTML content
 */
export function extractClassesFromHTML(html: string): string[] {
  const classRegex = /class\s*=\s*["']([^"']+)["']/gi;
  const classes = new Set<string>();
  
  let match;
  while ((match = classRegex.exec(html)) !== null) {
    const classNames = match[1].split(/\s+/).filter(Boolean);
    classNames.forEach(className => classes.add(className));
  }
  
  return Array.from(classes);
}

/**
 * Batch process multiple class strings
 */
export function batchProcessClasses(
  classStrings: string[],
  config?: RunCSSConfig
): CSSBuildResult {
  const allClasses = classStrings
    .flatMap(str => str.split(/\s+/))
    .filter(Boolean)
    .filter((cls, index, arr) => arr.indexOf(cls) === index); // Remove duplicates
  
  return buildProductionCSS(allClasses, config);
}

export default {
  getPreviewInstance,
  isPreviewInstanceActive,
  createRunCSSInstance,
  processClassesForPreview,
  resetPreviewInstance,
  buildProductionCSS,
  validateClasses,
  minifyCSS,
  generateHash,
  createCSSFile,
  extractClassesFromHTML,
  batchProcessClasses,
  DEFAULT_RUNCSS_CONFIG,
};
